{"version": 3, "file": "s3.controller.js", "sourceRoot": "", "sources": ["../../src/s3/s3.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAcwB;AACxB,+DAA2D;AAC3D,6CAA0G;AAC1G,kEAA6D;AAC7D,4DAAwD;AACxD,wEAA2D;AAC3D,6CAAmD;AACnD,kFAA8E;AAU9E,MAAM,mBAAmB;CAKxB;AAED,MAAM,aAAa;CAIlB;AAMM,IAAM,YAAY,GAAlB,MAAM,YAAY;IACvB,YACmB,SAAoB,EACpB,oBAA0C;QAD1C,cAAS,GAAT,SAAS,CAAW;QACpB,yBAAoB,GAApB,oBAAoB,CAAsB;IAC1D,CAAC;IAOE,AAAN,KAAK,CAAC,cAAc,CAAS,OAA4B;QACvD,IAAI,CAAC;YACH,MAAM,MAAM,GAAa;gBACvB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,UAAU,EAAE,OAAO,CAAC,UAAU;aAC/B,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC3D,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,8BAA8B,EAC/C,KAAK,CAAC,MAAM,IAAI,mBAAU,CAAC,qBAAqB,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,UAAU,CACW,cAAsB,EAC/B,IAAyB,EACjC,SAAuC,EACxC,GAAoB;QAE3B,IAAI,CAAC;YAEH,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACnC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnF,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,cAAc,CAAC,CAAC;gBAEnF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,MAAM,IAAI,sBAAa,CAAC,qCAAqC,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;gBACvF,CAAC;YACH,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE7E,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC7D,MAAM,IAAI,4BAAmB,CAAC,uDAAuD,CAAC,CAAC;YACzF,CAAC;YAED,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,4BAAmB,CAAC,kBAAkB,CAAC,CAAC;YACpD,CAAC;YAGD,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;YACjC,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC;gBACxB,MAAM,IAAI,4BAAmB,CAAC,8BAA8B,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,MAAM,GAAa;gBACvB,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW;gBAC9C,eAAe,EAAE,YAAY,CAAC,QAAQ,CAAC,eAAe;gBACtD,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,MAAM;gBACpC,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU;aAC7C,CAAC;YAGF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,GAAG,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAC5C,MAAM,EACN,IAAI,CAAC,MAAM,EACX,QAAQ,EACR,IAAI,CAAC,QAAQ,EACb,cAAc,EACd,SAAS,CAAC,eAAe,CAC1B,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE,MAAM;aACb,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,uBAAuB,EACxC,KAAK,CAAC,MAAM,IAAI,mBAAU,CAAC,qBAAqB,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,UAAU,CACW,cAAsB,EACjC,GAAW,EAClB,GAAoB;QAE3B,IAAI,CAAC;YAEH,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACnC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnF,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,cAAc,CAAC,CAAC;gBAEnF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,MAAM,IAAI,sBAAa,CAAC,qCAAqC,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;gBACvF,CAAC;YACH,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE7E,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC7D,MAAM,IAAI,4BAAmB,CAAC,uDAAuD,CAAC,CAAC;YACzF,CAAC;YAED,MAAM,MAAM,GAAa;gBACvB,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW;gBAC9C,eAAe,EAAE,YAAY,CAAC,QAAQ,CAAC,eAAe;gBACtD,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,MAAM;gBACpC,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU;aAC7C,CAAC;YAGF,MAAM,UAAU,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAE3C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAEnE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE,MAAM;aACb,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,uBAAuB,EACxC,KAAK,CAAC,MAAM,IAAI,mBAAU,CAAC,qBAAqB,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,SAAS,CACY,cAAsB,EACxC,GAAoB;QAE3B,IAAI,CAAC;YAEH,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACnC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnF,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,cAAc,CAAC,CAAC;gBAEnF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,MAAM,IAAI,sBAAa,CAAC,qCAAqC,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;gBACvF,CAAC;YACH,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE7E,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC7D,MAAM,IAAI,4BAAmB,CAAC,uDAAuD,CAAC,CAAC;YACzF,CAAC;YAED,MAAM,MAAM,GAAa;gBACvB,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW;gBAC9C,eAAe,EAAE,YAAY,CAAC,QAAQ,CAAC,eAAe;gBACtD,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,MAAM;gBACpC,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU;aAC7C,CAAC;YAEF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAErE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE,KAAK;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,sBAAsB,EACvC,KAAK,CAAC,MAAM,IAAI,mBAAU,CAAC,qBAAqB,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,eAAe,CACM,cAAsB,EACjC,GAAW,EAClB,GAAoB;QAE3B,IAAI,CAAC;YAEH,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACnC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnF,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,cAAc,CAAC,CAAC;gBAEnF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,MAAM,IAAI,sBAAa,CAAC,qCAAqC,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;gBACvF,CAAC;YACH,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE7E,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC7D,MAAM,IAAI,4BAAmB,CAAC,uDAAuD,CAAC,CAAC;YACzF,CAAC;YAED,MAAM,MAAM,GAAa;gBACvB,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW;gBAC9C,eAAe,EAAE,YAAY,CAAC,QAAQ,CAAC,eAAe;gBACtD,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,MAAM;gBACpC,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU;aAC7C,CAAC;YAGF,MAAM,UAAU,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAE3C,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAErE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,sCAAsC;gBAC/C,IAAI,EAAE,EAAE,GAAG,EAAE;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,OAAO,IAAI,kCAAkC,EACnD,KAAK,CAAC,MAAM,IAAI,mBAAU,CAAC,qBAAqB,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA9PY,oCAAY;AAWjB;IALL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,uBAAK,EAAC,YAAY,EAAE,OAAO,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,8CAA8C,EAAE,CAAC;IACzE,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IACnE,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC;IACjB,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAU,mBAAmB;;kDAiBxD;AAQK;IANL,IAAA,aAAI,EAAC,wBAAwB,CAAC;IAC9B,IAAA,uBAAK,EAAC,YAAY,EAAE,OAAO,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;IAClE,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;IACvE,IAAA,qBAAW,EAAC,qBAAqB,CAAC;IAClC,IAAA,wBAAe,EAAC,IAAA,kCAAe,EAAC,MAAM,CAAC,CAAC;IAEtC,WAAA,IAAA,cAAK,EAAC,gBAAgB,CAAC,CAAA;IACvB,WAAA,IAAA,qBAAY,GAAE,CAAA;IACd,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;8CA6DP;AAMK;IAJL,IAAA,eAAM,EAAC,2BAA2B,CAAC;IACnC,IAAA,uBAAK,EAAC,YAAY,EAAE,OAAO,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC;IACpE,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IAEpE,WAAA,IAAA,cAAK,EAAC,gBAAgB,CAAC,CAAA;IACvB,WAAA,IAAA,cAAK,EAAC,KAAK,CAAC,CAAA;IACZ,WAAA,IAAA,YAAG,GAAE,CAAA;;;;8CA2CP;AAMK;IAJL,IAAA,YAAG,EAAC,uBAAuB,CAAC;IAC5B,IAAA,uBAAK,EAAC,YAAY,EAAE,OAAO,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;IACjE,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IAEpE,WAAA,IAAA,cAAK,EAAC,gBAAgB,CAAC,CAAA;IACvB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;6CAwCP;AAMK;IAJL,IAAA,YAAG,EAAC,oCAAoC,CAAC;IACzC,IAAA,uBAAK,EAAC,YAAY,EAAE,OAAO,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;IAChE,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,sCAAsC,EAAE,CAAC;IAE/E,WAAA,IAAA,cAAK,EAAC,gBAAgB,CAAC,CAAA;IACvB,WAAA,IAAA,cAAK,EAAC,KAAK,CAAC,CAAA;IACZ,WAAA,IAAA,YAAG,GAAE,CAAA;;;;mDA2CP;uBA7PU,YAAY;IAJxB,IAAA,iBAAO,EAAC,IAAI,CAAC;IACb,IAAA,mBAAU,EAAC,IAAI,CAAC;IAChB,IAAA,kBAAS,EAAC,6BAAY,EAAE,wBAAU,CAAC;IACnC,IAAA,uBAAa,GAAE;qCAGgB,sBAAS;QACE,4CAAoB;GAHlD,YAAY,CA8PxB"}