{"version": 3, "file": "s3.service.js", "sourceRoot": "", "sources": ["../../src/s3/s3.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA+F;AAC/F,kDAO4B;AAC5B,wEAA6D;AAgBtD,IAAM,SAAS,GAAf,MAAM,SAAS;IAKZ,cAAc,CAAC,MAAgB;QACrC,OAAO,IAAI,oBAAQ,CAAC;YAClB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,WAAW,EAAE;gBACX,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,eAAe,EAAE,MAAM,CAAC,eAAe;aACxC;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAgB;QACnC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAG7C,MAAM,OAAO,GAAG,IAAI,6BAAiB,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;YACrE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE7B,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,iDAAiD;aAC3D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAEnD,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAClC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,6CAA6C;iBACvD,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBACtC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,+DAA+D;iBACzE,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,oBAAoB,EAAE,CAAC;gBAC/C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,wBAAwB;iBAClC,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,uBAAuB,EAAE,CAAC;gBAClD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,4BAA4B;iBACtC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,sBAAsB,KAAK,CAAC,OAAO,IAAI,eAAe,EAAE;iBAClE,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CACd,MAAgB,EAChB,IAAY,EACZ,GAAW,EACX,WAAmB,EACnB,cAAsB,EACtB,eAAwB;QAExB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAG7C,MAAM,OAAO,GAAG,eAAe;gBAC7B,CAAC,CAAC,iBAAiB,cAAc,oBAAoB,eAAe,IAAI,GAAG,EAAE;gBAC7E,CAAC,CAAC,iBAAiB,cAAc,UAAU,GAAG,EAAE,CAAC;YAEnD,MAAM,OAAO,GAAG,IAAI,4BAAgB,CAAC;gBACnC,MAAM,EAAE,MAAM,CAAC,UAAU;gBACzB,GAAG,EAAE,OAAO;gBACZ,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,WAAW;gBACxB,QAAQ,EAAE;oBACR,cAAc;oBACd,eAAe,EAAE,eAAe,IAAI,EAAE;oBACtC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACrC;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAG7B,MAAM,GAAG,GAAG,WAAW,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,MAAM,kBAAkB,OAAO,EAAE,CAAC;YAExF,OAAO;gBACL,GAAG,EAAE,OAAO;gBACZ,GAAG;gBACH,IAAI,EAAE,IAAI,CAAC,MAAM;aAClB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,MAAM,IAAI,qCAA4B,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,MAAgB,EAAE,GAAW;QAC5C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,OAAO,GAAG,IAAI,+BAAmB,CAAC;gBACtC,MAAM,EAAE,MAAM,CAAC,UAAU;gBACzB,GAAG,EAAE,GAAG;aACT,CAAC,CAAC;YAEH,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE7B,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,2BAA2B;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,MAAM,IAAI,qCAA4B,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CACb,MAAgB,EAChB,cAAsB,EACtB,eAAwB;QAExB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,eAAe;gBAC5B,CAAC,CAAC,iBAAiB,cAAc,oBAAoB,eAAe,GAAG;gBACvE,CAAC,CAAC,iBAAiB,cAAc,SAAS,CAAC;YAE7C,MAAM,OAAO,GAAG,IAAI,gCAAoB,CAAC;gBACvC,MAAM,EAAE,MAAM,CAAC,UAAU;gBACzB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE9C,OAAO,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,qCAA4B,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CACnB,MAAgB,EAChB,GAAW,EACX,YAAoB,IAAI;QAExB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,OAAO,GAAG,IAAI,4BAAgB,CAAC;gBACnC,MAAM,EAAE,MAAM,CAAC,UAAU;gBACzB,GAAG,EAAE,GAAG;aACT,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,IAAA,mCAAY,EAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YACjE,OAAO,GAAG,CAAC;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,qCAA4B,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,MAAgB,EAAE,GAAW;QACjD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,OAAO,GAAG,IAAI,4BAAgB,CAAC;gBACnC,MAAM,EAAE,MAAM,CAAC,UAAU;gBACzB,GAAG,EAAE,GAAG;aACT,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE9C,OAAO;gBACL,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,YAAY,EAAE,QAAQ,CAAC,YAAY;gBACnC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;aAC5B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,IAAI,qCAA4B,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;CACF,CAAA;AAnNY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;GACA,SAAS,CAmNrB"}