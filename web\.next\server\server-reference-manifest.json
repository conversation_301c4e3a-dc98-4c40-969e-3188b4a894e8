{"node": {"006e6a7598517a64a0222bb89b73ddd257764d50ac": {"workers": {"app/(workspace)/brain/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/brain/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/(workspace)/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(workspace)/brain/page": "action-browser", "app/(workspace)/dashboard/page": "action-browser", "app/page": "action-browser"}}, "006f4095f78e30eaa701b85c396d30de187fe7f260": {"workers": {"app/(workspace)/brain/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/brain/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/(workspace)/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(workspace)/brain/page": "action-browser", "app/(workspace)/dashboard/page": "action-browser", "app/page": "action-browser"}}, "00c349c6c9b93ac440dea00915d65f1b054d5a71a5": {"workers": {"app/(workspace)/brain/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/brain/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/(workspace)/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(workspace)/brain/page": "action-browser", "app/(workspace)/dashboard/page": "action-browser", "app/page": "action-browser"}}, "408d4ffcda2adfadc68aafde596a5102a7ebb51409": {"workers": {"app/(workspace)/brain/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/brain/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/(workspace)/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(workspace)/brain/page": "action-browser", "app/(workspace)/dashboard/page": "action-browser", "app/page": "action-browser"}}, "40ae9d6f5ad7238997dd0ef7497e335399ead00b2f": {"workers": {"app/(workspace)/brain/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/brain/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/(workspace)/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/page": {"moduleId": "[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(workspace)/brain/page": "action-browser", "app/(workspace)/dashboard/page": "action-browser", "app/page": "action-browser"}}}, "edge": {}, "encryptionKey": "tEM6TKGLe8j5cQrCriCePVJhjSUUbfs7lZlYOl4PwKc="}