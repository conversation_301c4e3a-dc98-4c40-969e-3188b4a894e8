import { Schema, Types } from 'mongoose';
export declare const OrganizationSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "suspended";
    credits: number;
    monthlyFreeCredits: number;
    monthlyFreeCreditsUsed: number;
    lastMonthlyReset: NativeDate;
    usingFreeCredits: boolean;
    monthlyResetDate: number;
    autoRechargeEnabled: boolean;
    autoRechargeThreshold: number;
    autoRechargeAmount: number;
    callPricePerMinute: number;
    minimumCreditsThreshold: number;
    monthlyMinutesAllowance: number;
    adminUsers: {
        prototype?: Types.ObjectId;
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
        isValid?: {};
    }[];
    users: {
        prototype?: Types.ObjectId;
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
        isValid?: {};
    }[];
    description?: string;
    stripeCustomerId?: string;
    fullName?: string;
    email?: string;
    lastWarningEmailSent?: NativeDate;
    lastRunoutEmailSent?: NativeDate;
    s3Config?: {
        enabled: boolean;
        accessKeyId?: string;
        secretAccessKey?: string;
        region?: string;
        bucketName?: string;
    };
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "suspended";
    credits: number;
    monthlyFreeCredits: number;
    monthlyFreeCreditsUsed: number;
    lastMonthlyReset: NativeDate;
    usingFreeCredits: boolean;
    monthlyResetDate: number;
    autoRechargeEnabled: boolean;
    autoRechargeThreshold: number;
    autoRechargeAmount: number;
    callPricePerMinute: number;
    minimumCreditsThreshold: number;
    monthlyMinutesAllowance: number;
    adminUsers: {
        prototype?: Types.ObjectId;
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
        isValid?: {};
    }[];
    users: {
        prototype?: Types.ObjectId;
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
        isValid?: {};
    }[];
    description?: string;
    stripeCustomerId?: string;
    fullName?: string;
    email?: string;
    lastWarningEmailSent?: NativeDate;
    lastRunoutEmailSent?: NativeDate;
    s3Config?: {
        enabled: boolean;
        accessKeyId?: string;
        secretAccessKey?: string;
        region?: string;
        bucketName?: string;
    };
}>> & import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "suspended";
    credits: number;
    monthlyFreeCredits: number;
    monthlyFreeCreditsUsed: number;
    lastMonthlyReset: NativeDate;
    usingFreeCredits: boolean;
    monthlyResetDate: number;
    autoRechargeEnabled: boolean;
    autoRechargeThreshold: number;
    autoRechargeAmount: number;
    callPricePerMinute: number;
    minimumCreditsThreshold: number;
    monthlyMinutesAllowance: number;
    adminUsers: {
        prototype?: Types.ObjectId;
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
        isValid?: {};
    }[];
    users: {
        prototype?: Types.ObjectId;
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
        isValid?: {};
    }[];
    description?: string;
    stripeCustomerId?: string;
    fullName?: string;
    email?: string;
    lastWarningEmailSent?: NativeDate;
    lastRunoutEmailSent?: NativeDate;
    s3Config?: {
        enabled: boolean;
        accessKeyId?: string;
        secretAccessKey?: string;
        region?: string;
        bucketName?: string;
    };
}> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
