{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a1b8b59d._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_321a5c3e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tEM6TKGLe8j5cQrCriCePVJhjSUUbfs7lZlYOl4PwKc=", "__NEXT_PREVIEW_MODE_ID": "384120ecd06a3907c3856802d6474238", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c5a48e36ab6c0bcb1773692494bccccedfc2266abc82cead80663d80be8dbcfd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7b83b23ba8687f73421dfab49b33a2b8529fdab38c74a02a23b2197f7d8822e1"}}}, "sortedMiddleware": ["/"], "functions": {}}