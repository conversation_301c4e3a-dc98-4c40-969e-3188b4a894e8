import { Injectable, HttpException, HttpStatus } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import moment from "moment-timezone";
import {
  ScheduledCall,
  ScheduledCallDocument,
} from "./schemas/scheduled-call.schema";

@Injectable()
export class ScheduledCallService {
  constructor(
    @InjectModel(ScheduledCall.name)
    private readonly scheduledCallModel: Model<ScheduledCallDocument>
  ) {}

  async createScheduledCall(payload: {
    agentId: string;
    contacts: { Name: string; MobileNumber: string }[];
    scheduledTime: string;
    region: string;
    scheduledByName: string;
  }): Promise<ScheduledCall> {
    // Extract values with fallback for agentId
    let { contacts, scheduledTime, region, scheduledByName } = payload;
    let agentId = payload.agentId;

    try {
      // Validate required fields
      if (!agentId) {
        throw new Error('agentId is required');
      }
      if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
        throw new Error('contacts array is required and must not be empty');
      }
      if (!scheduledTime) {
        throw new Error('scheduledTime is required');
      }
      if (!region) {
        throw new Error('region is required');
      }

      // Convert the scheduled time to UTC
      const utcTime = moment.tz(scheduledTime, region).utc().toDate();
      const scheduledByTimestamp = new Date();

      const newScheduledCall = new this.scheduledCallModel({
        agentId,
        contacts,
        scheduledTime: utcTime,
        region,
        scheduledByName,
        scheduledByTimestamp,
        status: "pending",
      });

      const savedCall = await newScheduledCall.save();
      console.log(`Successfully created scheduled call with ID: ${savedCall._id}`);
      return savedCall;
    } catch (error) {
      // Log the detailed error
      console.error('Error creating scheduled call:', error);

      if (error.name === 'ValidationError') {
        // Handle Mongoose validation errors
        const validationErrors = Object.values(error.errors).map((err: any) => err.message);
        throw new HttpException(
          `Validation error: ${validationErrors.join(', ')}`,
          HttpStatus.BAD_REQUEST
        );
      }

      throw new HttpException(
        `Error creating scheduled call: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getScheduledCalls(page?: number, limit?: number, search?: string, dateFilter?: string): Promise<ScheduledCall[]> {
    try {
       // Build the filter
    const queryFilter: any = {};

    // Add search filter if provided
    if (search && search.trim().length >= 1) {
      const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const searchRegex = new RegExp(escapedSearch, 'i');
      queryFilter['contacts'] = {
        $elemMatch: {
          Name: { $regex: searchRegex }
        }
      };
    }

    if (dateFilter) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Get current week's Monday
      const monday = new Date(today);
      monday.setDate(monday.getDate() - monday.getDay() + 1);

      // Get current week's Sunday
      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);

      // Get next week's Monday and Sunday
      const nextMonday = new Date(sunday);
      nextMonday.setDate(sunday.getDate() + 1);

      const nextSunday = new Date(nextMonday);
      nextSunday.setDate(nextMonday.getDate() + 6);

      switch (dateFilter) {
        case 'today':
          // Only today's schedules (00:00 to 23:59)
          queryFilter.scheduledTime = {
            $gte: today,
            $lt: tomorrow
          };
          break;

        case 'thisWeek':
          // Current week Monday to Sunday
          queryFilter.scheduledTime = {
            $gte: monday,
            $lte: sunday
          };
          break;

        case 'weekend':
          // Any Saturday or Sunday (past or future)
          queryFilter.$expr = {
            $in: [
              { $dayOfWeek: '$scheduledTime' },
              [1, 7] // MongoDB: 1 = Sunday, 7 = Saturday
            ]
          };
          break;

        case 'nextWeek':
          // Next week Monday to Sunday
          queryFilter.scheduledTime = {
            $gte: nextMonday,
            $lt: nextSunday
          };
          break;

        case 'upcoming':
          // From now into the future
          queryFilter.scheduledTime = {
            $gte: now
          };
          break;

        case 'past':
          // Before current date and time
          queryFilter.scheduledTime = {
            $lt: now
          };
          break;
      }
    }
       // Get total count
    const totalCount = await this.scheduledCallModel.countDocuments().exec();

    let query = this.scheduledCallModel.find(queryFilter).sort({ scheduledTime: -1 });

    // If both page and limit are provided, apply pagination
    if (page !== undefined && limit !== undefined) {
      const skip = (page - 1) * limit;
      query = query.skip(skip).limit(limit);
    }

    // Execute the query
    const scheduledCalls = await query.exec();

    // Add the total count to each call object
    scheduledCalls.forEach(call => {
      // Set the value that will be used by the virtual getter
      call['_totalCount'] = totalCount;
    });

    return scheduledCalls;
    } catch (error) {
      throw new HttpException(
        "Error fetching scheduled calls",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


  async getLatestScheduledCallForCampaign(agentId: string): Promise<ScheduledCall[]> {
    try {
      return this.scheduledCallModel
        .find({
          agentId: agentId,
          status: 'pending',
        })
        .sort({ scheduledTime: -1 })
        .limit(1)
        .exec();
    } catch (error) {
      throw new HttpException(
        "Error fetching latest scheduled call",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getPendingCallsForCampaign(agentId: string): Promise<ScheduledCall[]> {
    try {
      return this.scheduledCallModel
        .find({
          agentId: agentId,
          status: 'pending',
        })
        .sort({ scheduledTime: 1 }) // Sort by scheduled time ascending
        .exec();
    } catch (error) {
      throw new HttpException(
        "Error fetching pending calls for campaign",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Remove duplicate pending calls for a campaign
   * @param agentId The agent ID of the campaign
   * @returns Object containing the count of duplicates removed
   */
  async removeDuplicateCalls(
    agentId: string
  ): Promise<{ duplicatesRemoved: number }> {
    try {
      // Get all pending calls for this campaign
      const pendingCalls = await this.getPendingCallsForCampaign(agentId);

      if (!pendingCalls || pendingCalls.length === 0) {
        return {
          duplicatesRemoved: 0
        };
      }

      // Check for duplicate pending calls for the same contact
      const contactMap = new Map<string, ScheduledCall[]>();
      let duplicatesRemoved = 0;

      // Group calls by contact key (Name-MobileNumber)
      for (const call of pendingCalls) {
        for (const contact of call.contacts) {
          const key = `${contact.Name}-${contact.MobileNumber}`;
          if (!contactMap.has(key)) {
            contactMap.set(key, []);
          }
          contactMap.get(key).push(call);
        }
      }

      // For each contact with multiple calls, keep only the most recent one
      for (const [, calls] of contactMap.entries()) {
        if (calls.length > 1) {
          // Sort calls by scheduledTime (most recent first)
          calls.sort((a, b) => new Date(b.scheduledTime).getTime() - new Date(a.scheduledTime).getTime());

          // Keep the first one (most recent) and delete the rest
          for (let i = 1; i < calls.length; i++) {
            await this.scheduledCallModel.findByIdAndDelete((calls[i] as any)._id);
            duplicatesRemoved++;
          }
        }
      }

      return {
        duplicatesRemoved
      };
    } catch (error) {
      throw new HttpException(
        error.message || "Error removing duplicate calls",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async rescheduleCampaignCalls(
    agentId: string,
    concurrentCalls: number,
    batchIntervalMinutes: number,
    callWindow: { startTime: string; endTime: string; daysOfWeek: string[] }
  ): Promise<{ rescheduledCount: number; duplicatesRemoved: number; totalCount: number }> {
    try {
      // Get all pending calls for this campaign
      const pendingCalls = await this.getPendingCallsForCampaign(agentId);

      if (!pendingCalls || pendingCalls.length === 0) {
        return {
          rescheduledCount: 0,
          duplicatesRemoved: 0,
          totalCount: 0
        };
      }

      // Check for duplicate pending calls for the same contact
      const contactMap = new Map<string, ScheduledCall[]>();
      let duplicatesRemoved = 0;

      // Group calls by contact key (Name-MobileNumber)
      for (const call of pendingCalls) {
        for (const contact of call.contacts) {
          const key = `${contact.Name}-${contact.MobileNumber}`;
          if (!contactMap.has(key)) {
            contactMap.set(key, []);
          }
          contactMap.get(key).push(call);
        }
      }

      // For each contact with multiple calls, keep only the most recent one
      for (const [, calls] of contactMap.entries()) {
        if (calls.length > 1) {
          // Sort calls by scheduledTime (most recent first)
          calls.sort((a, b) => new Date(b.scheduledTime).getTime() - new Date(a.scheduledTime).getTime());

          // Keep the first one (most recent) and delete the rest
          for (let i = 1; i < calls.length; i++) {
            await this.scheduledCallModel.findByIdAndDelete((calls[i] as any)._id);
            duplicatesRemoved++;
          }
        }
      }

      // Refresh the pending calls list after removing duplicates
      const updatedPendingCalls = await this.getPendingCallsForCampaign(agentId);

      if (!updatedPendingCalls || updatedPendingCalls.length === 0) {
        return {
          rescheduledCount: 0,
          duplicatesRemoved,
          totalCount: duplicatesRemoved
        };
      }

      // Parse call window times
      const [startHour, startMinute] = callWindow.startTime.split(':').map(Number);
      const [endHour, endMinute] = callWindow.endTime.split(':').map(Number);

      // Group calls by contact (to maintain the same contacts in each batch)
      const contactGroups = new Map<string, ScheduledCall[]>();

      for (const call of pendingCalls) {
        for (const contact of call.contacts) {
          const key = `${contact.Name}-${contact.MobileNumber}`;
          if (!contactGroups.has(key)) {
            contactGroups.set(key, []);
          }
          contactGroups.get(key).push(call);
        }
      }

      // Create batches based on the new concurrency level
      const contacts = Array.from(contactGroups.keys());
      const batches = [];

      for (let i = 0; i < contacts.length; i += concurrentCalls) {
        batches.push(contacts.slice(i, i + concurrentCalls));
      }

      // Start with current time + 5 minutes
      const now = new Date();
      let currentDate = new Date(now.getTime() + 5 * 60 * 1000); // Add 5 minutes

      // Ensure we're starting on an allowed day
      const normalizedDaysOfWeek = callWindow.daysOfWeek.map(day => day.toLowerCase());
      let currentDayName = currentDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

      // If current day is not allowed, find the next allowed day
      if (!normalizedDaysOfWeek.includes(currentDayName)) {
        let daysToAdd = 1;
        while (daysToAdd <= 7) {
          const nextDate = new Date(currentDate);
          nextDate.setDate(nextDate.getDate() + daysToAdd);
          const nextDayName = nextDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
          if (normalizedDaysOfWeek.includes(nextDayName)) {
            currentDate = nextDate;
            currentDayName = nextDayName;
            break;
          }
          daysToAdd++;
        }
      }

      // Check if current time is within the call window
      // We'll use UTC for initial scheduling, and then adjust for each contact's timezone later
      const currentHour = currentDate.getHours();
      const currentMinute = currentDate.getMinutes();

      // Check if current time is outside the call window
      const isBeforeWindow = currentHour < startHour || (currentHour === startHour && currentMinute < startMinute);
      const isAfterWindow = currentHour > endHour || (currentHour === endHour && currentMinute > endMinute);

      if (isBeforeWindow) {
        // If before window start on an allowed day, just set to start time
        currentDate.setHours(startHour, startMinute, 0, 0);
      } else if (isAfterWindow) {
        // If after window end, move to next allowed day at start time
        let daysToAdd = 1;
        let nextAllowedDay = false;

        while (daysToAdd <= 7 && !nextAllowedDay) {
          const nextDate = new Date(currentDate);
          nextDate.setDate(nextDate.getDate() + daysToAdd);
          const nextDayName = nextDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

          if (normalizedDaysOfWeek.includes(nextDayName)) {
            // Found the next allowed day
            nextAllowedDay = true;
            currentDate = new Date(nextDate);
            // Set to start time
            currentDate.setHours(startHour, startMinute, 0, 0);
          }

          daysToAdd++;
        }

        // If no allowed day found (shouldn't happen if daysOfWeek has at least one day)
        if (!nextAllowedDay && normalizedDaysOfWeek.length > 0) {
          // Use the first allowed day of the week
          const today = new Date();
          const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.

          // Map day names to day numbers
          const dayNameToNumber = {
            'sunday': 0,
            'monday': 1,
            'tuesday': 2,
            'wednesday': 3,
            'thursday': 4,
            'friday': 5,
            'saturday': 6
          };

          // Find the first allowed day
          const firstAllowedDayName = normalizedDaysOfWeek[0];
          const firstAllowedDayNumber = dayNameToNumber[firstAllowedDayName];

          // Calculate days to add to get to the first allowed day
          let daysToFirstAllowed = firstAllowedDayNumber - dayOfWeek;
          if (daysToFirstAllowed <= 0) {
            daysToFirstAllowed += 7; // Add a week if it's in the past
          }

          // Set the date to the first allowed day
          const nextDate = new Date(today);
          nextDate.setDate(nextDate.getDate() + daysToFirstAllowed);
          currentDate = new Date(nextDate);
          currentDate.setHours(startHour, startMinute, 0, 0);
        }
      }

      // Reschedule calls for each batch
      let rescheduledCount = 0;

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        const scheduledTime = new Date(currentDate);

        // Reschedule each contact in this batch
        for (const contactKey of batch) {
          const calls = contactGroups.get(contactKey);

          if (calls && calls.length > 0) {
            // Get the first call for this contact to determine the region
            const call = calls[0];
            const contactRegion = call.region || 'UTC';

            // Convert the base scheduled time to the contact's timezone
            const contactLocalTime = moment.utc(scheduledTime).tz(contactRegion);

            // Check if the contact's local time is within the call window
            const contactHour = contactLocalTime.hours();
            const contactMinute = contactLocalTime.minutes();

            // Determine if the contact's local time is outside the call window
            const isContactBeforeWindow = contactHour < startHour ||
                                         (contactHour === startHour && contactMinute < startMinute);
            const isContactAfterWindow = contactHour > endHour ||
                                        (contactHour === endHour && contactMinute > endMinute);

            // Get the current day name in the contact's timezone
            const contactDayName = contactLocalTime.format('dddd').toLowerCase();
            const isAllowedDay = normalizedDaysOfWeek.includes(contactDayName);

            let finalScheduledTime: Date;

            if (!isAllowedDay || isContactBeforeWindow || isContactAfterWindow) {
              // If not an allowed day or outside window, find the next allowed day and time
              let nextAllowedDate = moment.utc(scheduledTime).tz(contactRegion);

              // If not an allowed day or after window, move to next day
              if (!isAllowedDay || isContactAfterWindow) {
                let daysChecked = 0;
                let foundAllowedDay = false;

                while (daysChecked < 7 && !foundAllowedDay) {
                  // Move to next day
                  nextAllowedDate.add(1, 'days').startOf('day');
                  const nextDayName = nextAllowedDate.format('dddd').toLowerCase();

                  if (normalizedDaysOfWeek.includes(nextDayName)) {
                    foundAllowedDay = true;
                    // Set to start of window
                    nextAllowedDate.hours(startHour).minutes(startMinute).seconds(0).milliseconds(0);
                  }

                  daysChecked++;
                }

                // Fallback if no allowed day found
                if (!foundAllowedDay && normalizedDaysOfWeek.length > 0) {
                  // Use the first allowed day of the week
                  const firstAllowedDay = normalizedDaysOfWeek[0];
                  const dayMapping = {
                    'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
                    'thursday': 4, 'friday': 5, 'saturday': 6
                  };

                  const today = nextAllowedDate.day();
                  const targetDay = dayMapping[firstAllowedDay];
                  let daysToAdd = targetDay - today;
                  if (daysToAdd <= 0) daysToAdd += 7;

                  nextAllowedDate.add(daysToAdd, 'days')
                    .hours(startHour).minutes(startMinute).seconds(0).milliseconds(0);
                }
              } else if (isContactBeforeWindow) {
                // If before window on an allowed day, just set to start time
                nextAllowedDate.hours(startHour).minutes(startMinute).seconds(0).milliseconds(0);
              }

              finalScheduledTime = nextAllowedDate.toDate();
            } else {
              // Time is within window on an allowed day, use as is
              finalScheduledTime = scheduledTime;
            }

            // Update the scheduled time for all calls for this contact
            for (const callToUpdate of calls) {
              await this.scheduledCallModel.updateOne(
                { _id: (callToUpdate as any)._id },
                { scheduledTime: finalScheduledTime }
              );
              rescheduledCount++;
            }
          }
        }

        // Move to next batch - add the configured batch interval minutes
        if (batchIndex < batches.length - 1) {
          const intervalMinutes = batchIntervalMinutes || 3; // Default to 3 minutes if not provided
          currentDate = new Date(currentDate.setMinutes(currentDate.getMinutes() + intervalMinutes));

          // If we've passed the end time for today, move to next allowed day
          if (currentDate.getHours() > endHour ||
              (currentDate.getHours() === endHour && currentDate.getMinutes() > endMinute)) {
            // Find the next allowed day
            let daysToAdd = 1;
            let nextAllowedDay = false;

            while (daysToAdd <= 7 && !nextAllowedDay) {
              const nextDate = new Date(currentDate);
              nextDate.setDate(nextDate.getDate() + daysToAdd);
              const nextDayName = nextDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

              if (normalizedDaysOfWeek.includes(nextDayName)) {
                // Found the next allowed day
                nextAllowedDay = true;
                currentDate = new Date(nextDate);
                // Set to start time
                currentDate.setHours(startHour, startMinute, 0, 0);
              }

              daysToAdd++;
            }

            // Fallback if no allowed day found (shouldn't happen if daysOfWeek has at least one day)
            if (!nextAllowedDay && normalizedDaysOfWeek.length > 0) {
              // Use the first allowed day of the week
              const today = new Date();
              const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.

              // Map day names to day numbers
              const dayNameToNumber = {
                'sunday': 0,
                'monday': 1,
                'tuesday': 2,
                'wednesday': 3,
                'thursday': 4,
                'friday': 5,
                'saturday': 6
              };

              // Find the first allowed day
              const firstAllowedDayName = normalizedDaysOfWeek[0];
              const firstAllowedDayNumber = dayNameToNumber[firstAllowedDayName];

              // Calculate days to add to get to the first allowed day
              let daysToFirstAllowed = firstAllowedDayNumber - dayOfWeek;
              if (daysToFirstAllowed <= 0) {
                daysToFirstAllowed += 7; // Add a week if it's in the past
              }

              // Set the date to the first allowed day
              const nextDate = new Date(today);
              nextDate.setDate(nextDate.getDate() + daysToFirstAllowed);
              currentDate = new Date(nextDate);
              currentDate.setHours(startHour, startMinute, 0, 0);
            }
          }
        }
      }

      // Return detailed information about the operation
      return {
        rescheduledCount,
        duplicatesRemoved,
        totalCount: rescheduledCount + duplicatesRemoved
      };
    } catch (error) {
      throw new HttpException(
        error.message || "Error rescheduling campaign calls",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateScheduledCall(
    id: string,
    updateData: Partial<ScheduledCall>
  ): Promise<ScheduledCall> {
    try {
      if (updateData.scheduledTime && updateData.region) {
        updateData.scheduledTime = moment
          .tz(updateData.scheduledTime, updateData.region)
          .utc()
          .toDate();
      }

      const updatedCall = await this.scheduledCallModel
        .findByIdAndUpdate(id, updateData, { new: true })
        .exec();

      if (!updatedCall) {
        throw new HttpException(
          "Scheduled call not found",
          HttpStatus.NOT_FOUND
        );
      }

      return updatedCall;
    } catch (error) {
      throw new HttpException(
        error.message || "Error updating scheduled call",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getScheduledCallsByStatusAndTime(status: string, now: Date) {
    try {
      // Only get calls that are in 'pending' status and due for execution
      // Exclude calls that are in 'processing' status to prevent race conditions
      return this.scheduledCallModel
        .find({
          status,
          scheduledTime: { $lte: now },
        })
        .sort({ scheduledTime: 1 }) // Process oldest calls first
        .exec();
    } catch (error) {
      throw new HttpException(
        "Error fetching scheduled calls by time",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getStuckProcessingCalls(timeThreshold: number = 10) {
    try {
      const thresholdTime = new Date();
      thresholdTime.setMinutes(thresholdTime.getMinutes() - timeThreshold);

      return this.scheduledCallModel
        .find({
          status: 'processing',
          lastProcessedAt: { $lte: thresholdTime }
        })
        .exec();
    } catch (error) {
      throw new HttpException(
        "Error fetching stuck processing calls",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getScheduledCallById(id: string): Promise<ScheduledCall> {
    try {
      const scheduledCall = await this.scheduledCallModel.findById(id).exec();
      if (!scheduledCall) {
        throw new HttpException(
          "Scheduled call not found",
          HttpStatus.NOT_FOUND
        );
      }
      return scheduledCall;
    } catch (error) {
      throw new HttpException(
        error.message || "Error fetching scheduled call",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async deleteScheduledCall(id: string): Promise<boolean> {
    try {
      const result = await this.scheduledCallModel.findByIdAndDelete(id).exec();

      if (!result) {
        throw new HttpException(
          "Scheduled call not found",
          HttpStatus.NOT_FOUND
        );
      }

      return true;
    } catch (error) {
      throw new HttpException(
        error.message || "Error deleting scheduled call",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Update agent ID for pending scheduled calls for contacts
   * @param contactNames Array of contact names
   * @param contactPhones Array of contact phone numbers
   * @param newAgentId New agent ID to set
   * @returns Number of calls updated
   */
  async updateAgentForPendingCalls(
    contactNames: string[],
    contactPhones: string[],
    newAgentId: string
  ): Promise<number> {
    try {
      // Check if arrays are empty or have different lengths
      if (!contactNames || !contactPhones || contactNames.length === 0 || contactPhones.length === 0) {
        return 0; // Nothing to update
      }

      if (contactNames.length !== contactPhones.length) {
        throw new HttpException(
          "Contact names and phone numbers arrays must have the same length",
          HttpStatus.BAD_REQUEST
        );
      }

      // Create an array of conditions to match any of the contacts
      const contactConditions = [];

      for (let i = 0; i < contactNames.length; i++) {
        if (contactNames[i] && contactPhones[i]) {
          contactConditions.push({
            'contacts.Name': contactNames[i],
            'contacts.MobileNumber': contactPhones[i]
          });
        }
      }

      // If no valid contact conditions were created, return 0
      if (contactConditions.length === 0) {
        return 0;
      }

      // Update all pending scheduled calls for these contacts with the new agent ID
      const result = await this.scheduledCallModel.updateMany(
        {
          status: 'pending',
          $or: contactConditions
        },
        { agentId: newAgentId }
      );

      return result.modifiedCount;
    } catch (error) {
      throw new HttpException(
        error.message || "Error updating agent for scheduled calls",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async cancelPendingCallsForContacts(
    contactNames: string[],
    contactPhones: string[]
  ): Promise<number> {
    try {
      // Check if arrays are empty or have different lengths
      if (!contactNames || !contactPhones || contactNames.length === 0 || contactPhones.length === 0) {
        return 0; // Nothing to cancel
      }

      if (contactNames.length !== contactPhones.length) {
        throw new HttpException(
          "Contact names and phone numbers arrays must have the same length",
          HttpStatus.BAD_REQUEST
        );
      }

      // Create an array of conditions to match any of the contacts
      const contactConditions = [];

      for (let i = 0; i < contactNames.length; i++) {
        if (contactNames[i] && contactPhones[i]) {
          contactConditions.push({
            'contacts.Name': contactNames[i],
            'contacts.MobileNumber': contactPhones[i]
          });
        }
      }

      // If no valid contact conditions were created, return 0
      if (contactConditions.length === 0) {
        return 0;
      }

      // Update all pending scheduled calls for these contacts to cancelled
      const result = await this.scheduledCallModel.updateMany(
        {
          status: 'pending',
          $or: contactConditions
        },
        { status: 'cancelled' }
      );

      return result.modifiedCount;
    } catch (error) {
      throw new HttpException(
        error.message || "Error cancelling scheduled calls",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async deleteCallsByStatus(status: string): Promise<{ deletedCount: number }> {
    try {
      const result = await this.scheduledCallModel.deleteMany({ status }).exec();
      return { deletedCount: result.deletedCount };
    } catch (error) {
      throw new HttpException(
        error.message || `Error deleting ${status} scheduled calls`,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
