import { S3Service } from './s3.service';
import { OrganizationsService } from '../organizations/organizations.service';
interface RequestWithUser extends Request {
    user: {
        userId: string;
        email: string;
        role: string;
    };
}
declare class TestS3ConnectionDto {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    bucketName: string;
}
export declare class S3Controller {
    private readonly s3Service;
    private readonly organizationsService;
    constructor(s3Service: S3Service, organizationsService: OrganizationsService);
    testConnection(testDto: TestS3ConnectionDto): Promise<{
        success: boolean;
        message: string;
    }>;
    uploadFile(organizationId: string, file: Express.Multer.File, uploadDto: {
        knowledgeBaseId?: string;
    }, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./s3.service").UploadResult;
    }>;
    deleteFile(organizationId: string, key: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: {
            success: boolean;
            message: string;
        };
    }>;
    listFiles(organizationId: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: any[];
    }>;
    getPresignedUrl(organizationId: string, key: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: {
            url: string;
        };
    }>;
}
export {};
