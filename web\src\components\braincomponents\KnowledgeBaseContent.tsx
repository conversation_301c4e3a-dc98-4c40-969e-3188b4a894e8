"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Search,
  FileText,
  Link as LinkIcon,
  Type,
  MoreVertical,
  Trash2,
  ExternalLink,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type KnowledgeBaseFolder = {
  _id: string;
  name: string;
  description?: string;
  createdAt: string;
  itemCount?: number;
};

type KnowledgeBaseItem = {
  _id: string;
  title: string;
  type: "document" | "url" | "text";
  content?: string;
  url?: string;
  filename?: string;
  originalName?: string;
  size?: number;
  pages?: number;
  status: "uploading" | "processing" | "ready" | "error";
  createdAt: string;
  lastSynced?: string;
  folderId: string;
};

type KnowledgeBaseContentProps = {
  folder: KnowledgeBaseFolder | null;
  items: KnowledgeBaseItem[];
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onAddContent: () => void;
  onDeleteItem: (itemId: string) => void;
};

export function KnowledgeBaseContent({
  folder,
  items,
  searchQuery,
  onSearchChange,
  onAddContent,
  onDeleteItem,
}: KnowledgeBaseContentProps) {
  const filteredItems = items.filter(item =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getItemIcon = (type: string) => {
    switch (type) {
      case "document":
        return <FileText className="h-5 w-5" />;
      case "url":
        return <LinkIcon className="h-5 w-5" />;
      case "text":
        return <Type className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "ready":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "uploading":
      case "processing":
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Ready</Badge>;
      case "uploading":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Uploading</Badge>;
      case "processing":
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Processing</Badge>;
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  if (!folder) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No folder selected
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Select a folder from the sidebar to view its contents
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {folder.name}
            </h1>
            <div className="flex items-center space-x-4 mt-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                ID: know...{folder._id.slice(-3)}
              </span>
              <div className="flex items-center space-x-1">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Created: {new Date(folder.createdAt).toLocaleDateString()} {new Date(folder.createdAt).toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
          <Button onClick={onAddContent}>
            <Plus className="h-4 w-4 mr-2" />
            Add Content
          </Button>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search content..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        {filteredItems.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {searchQuery ? "No content found" : "No content yet"}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {searchQuery
                  ? "Try adjusting your search terms"
                  : "Start by adding documents, URLs, or text content to this folder"
                }
              </p>
              {!searchQuery && (
                <Button onClick={onAddContent}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Content
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredItems.map((item) => (
              <div
                key={item._id}
                className="group border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow bg-white dark:bg-gray-800"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Icon */}
                    <div className={`p-2 rounded-lg ${
                      item.type === "url" ? "bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400" :
                      item.type === "text" ? "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400" :
                      "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400"
                    }`}>
                      {getItemIcon(item.type)}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-medium text-gray-900 dark:text-white truncate">
                          {item.title}
                        </h3>
                        {getStatusBadge(item.status)}
                      </div>

                      {/* Metadata */}
                      <div className="space-y-1">
                        {item.type === "url" && item.url && (
                          <div className="flex items-center space-x-2">
                            <a
                              href={item.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center"
                            >
                              {item.url}
                              <ExternalLink className="h-3 w-3 ml-1" />
                            </a>
                          </div>
                        )}

                        {item.type === "document" && (item.filename || item.originalName) && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {item.filename || item.originalName}
                          </p>
                        )}

                        <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          {item.pages && (
                            <span>{item.pages} Pages</span>
                          )}
                          {item.size && (
                            <span>{formatFileSize(item.size)}</span>
                          )}
                          {item.lastSynced && (
                            <span>Last synced on {new Date(item.lastSynced).toLocaleDateString()} {new Date(item.lastSynced).toLocaleTimeString()}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="text-red-600 dark:text-red-400"
                        onClick={() => onDeleteItem(item._id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
