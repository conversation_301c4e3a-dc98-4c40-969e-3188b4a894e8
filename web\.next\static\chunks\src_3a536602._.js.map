{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border \",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\"flex flex-col gap-1.5 px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger>) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"px-2 py-1.5 text-sm font-medium\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iuBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/assets/img/Binghatti-Lisa.jpeg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1586, height: 1586, blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAIAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDPhIuDPerHi0ScRIvmnATZggfjzWTvzXRppyWP/9k=\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mIAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAm4B,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28workspace%29/dashboard/DashboardContent.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/* eslint-disable @next/next/no-img-element */\r\n\"use client\";\r\n\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport {Select,SelectContent,SelectItem,SelectTrigger,SelectValue} from \"@/components/ui/select\";\r\nimport {Phone,Clock,Info,Calendar,PhoneCall,PhoneOutgoing,BarChart,Users,PhoneOff, UserX, VoicemailIcon, HelpCircle, PhoneIncoming, RefreshCw} from \"lucide-react\";\r\nimport { ResponsiveContainer, AreaChart, Area } from \"recharts\";\r\nimport { useEffect, useState } from \"react\";\r\nimport agentLisa from \"@/assets/img/Binghatti-Lisa.jpeg\";\r\nimport FadeIn from \"@/animations/FadeIn\";\r\nimport { format, formatDistanceToNow } from \"date-fns\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, Toolt<PERSON>Provider, TooltipTrigger } from \"@/components/ui/tooltip\";\r\nimport { DashboardData } from \"@/types/dashboard.types\";\r\n\r\n\r\n\r\n\r\nfunction DashboardContent() {\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [userRole, setUserRole] = useState<string | null>(null);\r\n  const [selectedTimeRange, setSelectedTimeRange] = useState<string>(\"all\");\r\n  const [selectedAgentType, setSelectedAgentType] = useState<string>(\"all\");\r\n  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);\r\n  \r\n\r\n  // Fetch all data\r\n  useEffect(() => {\r\n    const fetchUserProfile = async () => {\r\n      try {\r\n        const token = localStorage.getItem('access_token');\r\n        if (!token) {\r\n          console.error(\"No access token available\");\r\n          return;\r\n        }\r\n        \r\n        const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/me`, {\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        });\r\n        \r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch user profile: ${response.status}`);\r\n        }\r\n        \r\n        const userData = await response.json();\r\n        setUserRole(userData.role);\r\n      } catch (err) {\r\n        console.error(\"Failed to load user profile:\", err);\r\n      }\r\n    };\r\n    \r\n    fetchUserProfile();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchDashboardData = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const token = localStorage.getItem('access_token');\r\n        if (!token) {\r\n          console.error(\"No access token available\");\r\n          setError(\"Authentication required. Please log in again.\");\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        \r\n        const response = await fetch(\r\n          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/dashboard/metrics?timeRange=${selectedTimeRange}&agentType=${selectedAgentType}`, \r\n          {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json',\r\n            },\r\n          }\r\n        );\r\n        \r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch dashboard data: ${response.status}`);\r\n        }\r\n        \r\n        const data = await response.json();\r\n        setDashboardData(data);\r\n        \r\n      } catch (err) {\r\n        console.error(\"Failed to load dashboard data:\", err);\r\n        setError(\"Error loading dashboard data. Please try again later.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    \r\n    fetchDashboardData();\r\n  }, [selectedTimeRange, selectedAgentType]);\r\n\r\n//   const refreshDashboardStats = async () => {\r\n//   setLoading(true);\r\n//   try {\r\n//     const token = localStorage.getItem('access_token');\r\n//     if (!token) {\r\n//       console.error(\"No access token available\");\r\n//       setLoading(false);\r\n//       return;\r\n//     }\r\n    \r\n//     const response = await fetch(\r\n//       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/dashboard/refresh-stats`, \r\n//       {\r\n//         method: 'POST',\r\n//         headers: {\r\n//           'Authorization': `Bearer ${token}`,\r\n//           'Content-Type': 'application/json',\r\n//         },\r\n//       }\r\n//     );\r\n    \r\n//     if (!response.ok) {\r\n//       throw new Error(`Failed to refresh dashboard stats: ${response.status}`);\r\n//     }\r\n    \r\n//     // Fetch fresh data after clearing cache\r\n//     const dataResponse = await fetch(\r\n//       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/dashboard/metrics?timeRange=${selectedTimeRange}&agentType=${selectedAgentType}`, \r\n//       {\r\n//         headers: {\r\n//           'Authorization': `Bearer ${token}`,\r\n//           'Content-Type': 'application/json',\r\n//         },\r\n//       }\r\n//     );\r\n    \r\n//     if (!dataResponse.ok) {\r\n//       throw new Error(`Failed to fetch dashboard data: ${dataResponse.status}`);\r\n//     }\r\n    \r\n//     const data = await dataResponse.json();\r\n//     setDashboardData(data);\r\n    \r\n//   } catch (err) {\r\n//     console.error(\"Failed to refresh dashboard stats:\", err);\r\n    \r\n//   } finally {\r\n//     setLoading(false);\r\n//   }\r\n// };\r\n\r\n  const formatCallDuration = (durationMs: number): string => {\r\n    const totalSeconds = Math.round(durationMs / 1000);\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n    return `${minutes}m ${seconds.toString().padStart(2, '0')}s`;\r\n  };\r\n\r\n\r\n  const formatDuration = (durationMs: string | number) => {\r\n    const ms = typeof durationMs === 'string' ? parseInt(durationMs) : durationMs;\r\n    const minutes = Math.floor(ms / 60000);\r\n    const seconds = Math.floor((ms % 60000) / 1000);\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  };\r\n  \r\n\r\n  const connectionRateSparkline = Array.from({ length: 10 }, (_, i) => {\r\n    const baseValue = dashboardData?.callMetrics.connectionRate || 0;\r\n    let value = baseValue;\r\n    \r\n    if (i < 4) {\r\n      value = baseValue - (13 - i);\r\n    } else if (i > 8) {\r\n      value = baseValue + (i - 2);\r\n    } else if (i === 6) {\r\n      value = baseValue + 2;\r\n    }\r\n    \r\n    return {\r\n      day: i + 1,\r\n      value: Math.max(0, Math.min(100, value))\r\n    };\r\n  });\r\n\r\n\r\n  const getReasonDetails = (reason: string) => {\r\n    switch (reason) {\r\n      case 'customer-busy':\r\n        return { icon: <Clock className=\"h-4 w-4\" />, label: 'Customer Busy' };\r\n      case 'customer-ended-call':\r\n        return { icon: <PhoneOff className=\"h-4 w-4\" />, label: 'Customer Ended The call' };\r\n      case 'assistant-ended-call':\r\n        return { icon: <UserX className=\"h-4 w-4\" />, label: 'Agent Ended The call' };\r\n      case 'customer-did-not-answer':\r\n        return { icon: <PhoneOff className=\"h-4 w-4\" />, label: 'Customer Did not Answer' };\r\n         case 'customer-out-of-reach':\r\n        return { icon: <PhoneOff className=\"h-4 w-4\" />, label: 'Customer Out Of Reach' };\r\n      case 'voicemail':\r\n        return { icon: <VoicemailIcon className=\"h-4 w-4\" />, label: 'Voicemail' };\r\n      case 'silence-timed-out':\r\n        return { icon: <Clock className=\"h-4 w-4\" />, label: 'Silence Timed Out' };\r\n      default:\r\n        return { icon: <HelpCircle className=\"h-4 w-4\" />, label: \"Others (Timed out or failed to reach)\" };\r\n    }\r\n  };\r\n  \r\n \r\n  const answerRateSparklineData = Array.from({ length: 10 }, (_, i) => {\r\n    const baseValue = dashboardData?.callMetrics.answerRate || 0;\r\n    let value = baseValue;\r\n    \r\n    if (i < 3) {\r\n      value = baseValue - (10 - i);\r\n    } else if (i > 7) {\r\n      value = baseValue + (i - 5);\r\n    } else if (i === 5) {\r\n      value = baseValue + 3;\r\n    }\r\n    \r\n    return {\r\n      day: i + 1,\r\n      value: Math.max(0, Math.min(100, value))\r\n    };\r\n  });\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-[60vh] space-y-4\">\r\n        <div className=\"w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin\"></div>\r\n          <p className=\"text-lg font-medium\">Loading...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-[60vh] space-y-4\">\r\n        <div className=\"p-4 bg-red-50 text-red-500 rounded-lg\">\r\n          <p>Error loading dashboard: {error}</p>\r\n          <button \r\n            className=\"mt-2 px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200\"\r\n            onClick={() => window.location.reload()}\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n\r\n  return (\r\n    <>\r\n    <FadeIn direction=\"up\">\r\n      <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6\">\r\n      <h1 className=\"text-2xl font-bold\">\r\n      Dashboard {selectedAgentType !== \"all\" && `(${selectedAgentType} Agents)`} \r\n          {selectedTimeRange !== \"all\" && ` | Last ${selectedTimeRange} days`}\r\n      </h1>\r\n        \r\n        <div className=\"flex flex-col sm:flex-row gap-4 w-full md:w-auto\">\r\n          <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full\">\r\n\r\n             {/* <TooltipProvider>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <Button \r\n                    variant=\"outline\" \r\n                    size=\"icon\" \r\n                    onClick={refreshDashboardStats}\r\n                    disabled={loading}\r\n                    className=\"h-9 w-9\"\r\n                  >\r\n                    <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />\r\n                    <span className=\"sr-only\">Refresh dashboard statistics</span>\r\n                  </Button>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>Refresh dashboard statistics</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n            </TooltipProvider> */}\r\n\r\n          <Select defaultValue=\"30\" \r\n            value={selectedTimeRange}\r\n            onValueChange={setSelectedTimeRange}\r\n            >\r\n            <SelectTrigger className=\"h-9 w-full sm:w-[180px]\">\r\n              <SelectValue placeholder=\"Last 30 days\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"all\">All</SelectItem>\r\n              <SelectItem value=\"7\">Last 7 days</SelectItem>\r\n              <SelectItem value=\"14\">Last 14 days</SelectItem>\r\n              <SelectItem value=\"30\">Last 30 days</SelectItem>\r\n              <SelectItem value=\"90\">Last 90 days</SelectItem>\r\n              \r\n            </SelectContent>\r\n          </Select>\r\n{/* \r\n            <Select defaultValue=\"all\" >\r\n              <SelectTrigger className=\"h-9 w-full sm:w-[180px]\">\r\n                <SelectValue placeholder=\"All Workflows\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"all\">All Campaigns</SelectItem>\r\n                <SelectItem value=\"sales\">Sales</SelectItem>\r\n                <SelectItem value=\"support\">Support</SelectItem>\r\n              </SelectContent>\r\n            </Select> */}\r\n\r\n            <Select \r\n                defaultValue=\"all\" \r\n                value={selectedAgentType}\r\n                onValueChange={setSelectedAgentType}\r\n              >\r\n                <SelectTrigger className=\"h-9 w-full sm:w-[180px]\">\r\n              <SelectValue placeholder=\"All Agents\">\r\n                {selectedAgentType === \"all\" ? \"All Agents\" : `${selectedAgentType} Agents`}\r\n              </SelectValue>\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"all\">All Agents</SelectItem>\r\n              {dashboardData?.agentRoles && dashboardData.agentRoles.map((role) => (\r\n                <SelectItem key={role} value={role}>\r\n                  {role} Agents\r\n                </SelectItem>\r\n              ))} \r\n            </SelectContent>\r\n              </Select>\r\n          </div>\r\n\r\n         \r\n        </div>\r\n      </div>\r\n\r\n      {/* Top Stats Overview */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6 mb-6\">\r\n      <Card className=\"border rounded-lg \">\r\n      <CardContent className=\"pt-6\">\r\n        <div className=\"flex justify-between items-start mb-2\">\r\n          <div>\r\n            <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n              Total Calls\r\n            </h3>\r\n            <div className=\"flex items-baseline\">\r\n            <span className=\"text-2xl font-bold mr-2\">{dashboardData?.callMetrics.totalCalls || 0}</span>\r\n            </div>\r\n          </div>\r\n          <Phone className=\"h-5 w-5 text-gray-400\" />\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n\r\n    \r\n\r\n        <Card className=\"border rounded-lg \">\r\n          <CardContent className=\"pt-6\">\r\n            <div className=\"flex justify-between items-start mb-2\">\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n                  Total Call in Minutes\r\n                </h3>\r\n                <div className=\"flex items-baseline\">\r\n                <span className=\"text-2xl font-bold mr-1\">\r\n                {dashboardData?.callMetrics.totalMinutes.toFixed(1) || 0}\r\n              </span>\r\n                  <span className=\"text-sm text-gray-500\">min</span>\r\n                </div>\r\n              </div>\r\n              <Clock className=\"h-5 w-5 text-gray-400\" />\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card className=\"border rounded-lg \">\r\n          <CardContent className=\"pt-6\">\r\n            <div className=\"flex justify-between items-start mb-2\">\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n                  Av. Call Length\r\n                </h3>\r\n                <div className=\"flex items-baseline\">\r\n                  <span className=\"text-2xl font-bold mr-1\">{formatCallDuration(dashboardData?.callMetrics.averageLength || 0)}</span>\r\n                  <span className=\"text-sm text-gray-500\">sec</span>\r\n                </div>\r\n              </div>\r\n              <Clock className=\"h-5 w-5 text-gray-400\" />\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card className=\"border rounded-lg \">\r\n        <CardContent className=\"pt-6\">\r\n          <div className=\"flex justify-between items-start mb-2\">\r\n            <div>\r\n              <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n                Call Engagement Rate\r\n              </h3>\r\n              <div className=\"flex items-baseline\">\r\n                <span className=\"text-2xl font-bold mr-1\">\r\n                {dashboardData?.callMetrics.connectionRate || 0}%\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <PhoneOutgoing className=\"h-5 w-5 text-gray-400\" />\r\n          </div>\r\n          <FadeIn delay={0.3} direction=\"right\">    \r\n          <div className=\"h-12 mt-2\">\r\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n              <AreaChart\r\n                data={connectionRateSparkline}\r\n                margin={{ top: 0, right: 0, left: 0, bottom: 0 }}\r\n              >\r\n                <Area\r\n                  type=\"monotone\"\r\n                  dataKey=\"value\"\r\n                  stroke=\"#4157ea\"\r\n                  strokeWidth={2}\r\n                  fill=\"#4157ea20\"\r\n                />\r\n              </AreaChart>\r\n            </ResponsiveContainer>\r\n          </div>\r\n          </FadeIn>\r\n        </CardContent>\r\n      </Card>\r\n      </div>\r\n\r\n      {/* Call Answer Rate, Conversion Gain, New Calls Today, and Follow-up Calls */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\r\n      <Card className=\"border rounded-lg\">\r\n        <CardContent className=\"pt-6\">\r\n          <div className=\"flex justify-between items-start mb-2\">\r\n            <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n              Total Campaigns \r\n            </h3>\r\n            <BarChart className=\"h-5 w-5 text-gray-400\" />\r\n          </div>\r\n          <div className=\"flex items-baseline\">\r\n          <span className=\"text-2xl font-bold mr-2\">{dashboardData?.totalCounts.totalCampaigns || 0}</span>\r\n          <span className=\"text-lg text-gray-500\">campaigns</span>\r\n        </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n    <Card className=\"border rounded-lg\">\r\n      <CardContent className=\"pt-6\">\r\n        <div className=\"flex justify-between items-start mb-2\">\r\n          <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n            Total Scheduled Calls\r\n          </h3>\r\n          <Calendar className=\"h-5 w-5 text-gray-400\" />\r\n        </div>\r\n        <div className=\"flex items-baseline\">\r\n        <span className=\"text-2xl font-bold mr-2\">{dashboardData?.totalCounts.totalScheduledCalls || 0}</span>\r\n          <span className=\"text-lg text-gray-500\">scheduled</span>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n\r\n        <Card className=\"border rounded-lg\">\r\n          <CardContent className=\"pt-6\">\r\n            <div className=\"flex justify-between items-start mb-2\">\r\n              <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n                Total Contacts\r\n              </h3>\r\n              <Info className=\"h-5 w-5 text-gray-400\" />\r\n            </div>\r\n            <div className=\"flex items-baseline\">\r\n            <div className=\"text-2xl font-bold mr-2\">{dashboardData?.totalCounts.totalContacts || 0}</div>     \r\n          <span className=\"text-lg text-gray-500\">contacts</span>\r\n        </div>\r\n            \r\n            {/* <div className=\"h-10\">\r\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                <AreaChart\r\n                  data={sparklineData}\r\n                  margin={{ top: 0, right: 0, left: 0, bottom: 0 }}\r\n                >\r\n                  <Area\r\n                    type=\"monotone\"\r\n                    dataKey=\"value\"\r\n                    stroke=\"#4157ea\"\r\n                    strokeWidth={2}\r\n                    fill=\"#4157ea20\"\r\n                  />\r\n                </AreaChart>\r\n              </ResponsiveContainer>\r\n            </div> */}\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card className=\"border rounded-lg\">\r\n  <CardContent className=\"pt-6\">\r\n    <div className=\"flex justify-between items-start mb-2\">\r\n      <div>\r\n        <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\r\n          Answer Call Rate\r\n        </h3>\r\n        <div className=\"flex items-baseline\">\r\n          <span className=\"text-2xl font-bold mr-1\">\r\n          {dashboardData?.callMetrics.answerRate || 0}%\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <PhoneIncoming className=\"h-5 w-5 text-gray-400\" />\r\n    </div>\r\n    <FadeIn delay={0.3} direction=\"right\">         \r\n    <div className=\"h-10\">\r\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n        <AreaChart\r\n          data={answerRateSparklineData}\r\n          margin={{ top: 0, right: 0, left: 0, bottom: 0 }}\r\n        >\r\n          <Area\r\n            type=\"monotone\"\r\n            dataKey=\"value\"\r\n            stroke=\"#4157ea\"\r\n            strokeWidth={2}\r\n            fill=\"#4157ea20\"\r\n          />\r\n        </AreaChart>\r\n      </ResponsiveContainer>\r\n    </div>\r\n    </FadeIn>\r\n  </CardContent>\r\n</Card>\r\n      </div>\r\n\r\n      {/* Most Common Lost Reasons and Topics */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6\">\r\n      <Card className=\"border rounded-lg\">\r\n  <CardContent className=\"pt-6\">\r\n    <div className=\"flex justify-between items-center mb-4\">\r\n      <h3 className=\"text-sm font-medium\">Recent Calls {selectedAgentType !== \"all\" && `(${selectedAgentType})`}</h3>\r\n      <Badge\r\n        variant=\"outline\"\r\n        className=\"bg-blue-50 text-blue-800 hover:bg-blue-50 border-none\"\r\n      >\r\n        {dashboardData?.callMetrics.totalCalls || 0} calls \r\n      </Badge>\r\n    </div>\r\n    <div className=\"space-y-4\">\r\n      {dashboardData?.recentCalls.map((call) => (\r\n        <div\r\n          key={call._id}\r\n          className=\"flex items-center justify-between border-b pb-3\"\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <div className=\"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3\">\r\n              <PhoneCall className=\"h-4 w-4 text-blue-600\" />\r\n            </div>\r\n            <div>\r\n              <p className=\"text-sm font-medium\"> {call.fullName} | {call.mobileNumber}</p>\r\n              <p className=\"text-xs text-gray-400\">\r\n                {formatDistanceToNow(new Date(call.callStartTime), { addSuffix: true })}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center\">\r\n            <PhoneOutgoing className=\"h-3.5 w-3.5 text-blue-600 mr-2\" />\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"bg-blue-100 text-blue-800 hover:bg-blue-100 border-none mr-1\"\r\n          >\r\n            {formatDuration(call.callDuration)}\r\n          </Badge>\r\n        </div>\r\n        </div>\r\n      ))}\r\n      <Link href=\"/history\">\r\n      <Button\r\n        variant=\"ghost\"\r\n        size=\"sm\"\r\n        className=\"w-full text-blue-600 hover:text-blue-700 hover:bg-blue-50\"\r\n        >\r\n        View all calls\r\n      </Button>\r\n        </Link>\r\n    </div>\r\n  </CardContent>\r\n</Card>\r\n\r\n<Card className=\"border rounded-lg\">\r\n  <CardContent className=\"pt-6\">\r\n    <div className=\"flex justify-between items-center mb-4\">\r\n      <h3 className=\"text-sm font-medium\">\r\n        Last Scheduled Cost {selectedAgentType !== \"all\" && `(${selectedAgentType})`}\r\n      </h3>\r\n      <Badge\r\n        variant=\"outline\"\r\n        className=\"bg-amber-50 text-amber-800 hover:bg-amber-50 border-none\"\r\n      >\r\n        {dashboardData?.recentSchedules.length || 0} scheduled\r\n      </Badge>\r\n    </div>\r\n    <div className=\"space-y-4\">\r\n      {dashboardData?.recentSchedules.slice(0, 3).slice(0, 3).map((schedule) => (\r\n        <div\r\n          key={schedule._id}\r\n          className=\"flex items-center justify-between border-b pb-3\"\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <div className=\"w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3\">\r\n              <Calendar className=\"h-4 w-4 text-amber-600\" />\r\n            </div>\r\n            <div>\r\n              <p className=\"text-sm font-medium\">{schedule.contacts[0]?.Name || \"N/A\"} | {schedule.contacts[0]?.MobileNumber || \"N/A\"}</p>\r\n              <p className=\"text-xs text-gray-400\">\r\n                {format(new Date(schedule.scheduledTime), \"MMM d, yyyy h:mm a\")} • {schedule.region || \"N/A\"}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <Badge\r\n            variant=\"outline\"\r\n            className={`\r\n              ${\r\n                schedule.status === \"executed\"\r\n                  ? \"bg-green-100 text-green-800 hover:bg-green-100\"\r\n                  : schedule.status === \"pending\"\r\n                  ? \"bg-amber-100 text-yellow-800 hover:bg-yellow-100\"\r\n                  : \"bg-red-100 text-red-800 hover:bg-red-100\"\r\n              } border-none\r\n            `}\r\n          >\r\n            {schedule.status || \"pending\"}\r\n          </Badge>\r\n        </div>\r\n      ))}\r\n      <Link href=\"/schedule\">\r\n      <Button\r\n        variant=\"ghost\"\r\n        size=\"sm\"\r\n        className=\"w-full text-amber-600 hover:text-amber-700 hover:bg-amber-50\"    \r\n        >\r\n        View all scheduled calls\r\n      </Button>\r\n        </Link>\r\n    </div>\r\n  </CardContent>\r\n</Card>\r\n          </div>\r\n\r\n      {/* Sentiment Overview and Call Logs */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\">\r\n      <Card className=\"border rounded-lg \">\r\n        <CardContent className=\"pt-3\">\r\n          <h3 className=\"text-sm font-medium mb-6\"> Sentiment Overview {selectedAgentType !== \"all\" && `(${selectedAgentType})`} </h3>\r\n          <div className=\"grid grid-cols-3 gap-4 text-center mt-9\">\r\n            <div>\r\n              <div className=\"w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2\">\r\n                <span className=\"text-xl\">😊</span>\r\n              </div>\r\n              <h4 className=\"text-sm\">Positive</h4>\r\n              <p className=\"text-xl font-bold\">{dashboardData?.sentiments.positive || 0}%</p>\r\n            </div>\r\n\r\n            <div>\r\n              <div className=\"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-2\">\r\n                <span className=\"text-xl\">😐</span>\r\n              </div>\r\n              <h4 className=\"text-sm\">Neutral</h4>\r\n              <p className=\"text-xl font-bold\">{dashboardData?.sentiments.neutral || 0}%</p>\r\n            </div>\r\n\r\n            <div>\r\n              <div className=\"w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2\">\r\n                <span className=\"text-xl\">😞</span>\r\n              </div>\r\n              <h4 className=\"text-sm\">Negative</h4>\r\n              <p className=\"text-xl font-bold\">{dashboardData?.sentiments.negative || 0}%</p>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <Card className=\"border rounded-lg\">\r\n      <CardContent className=\"pt-6\">\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <h3 className=\"text-sm font-medium\">  Recent Campaigns {selectedAgentType !== \"all\" && `(${selectedAgentType})`} </h3>\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"bg-green-50 text-green-800 hover:bg-green-50 border-none\"\r\n          >\r\n            {dashboardData?.recentCampaigns.length || 0} campaigns\r\n          </Badge>\r\n        </div>\r\n        <div className=\"space-y-4\">\r\n          {dashboardData?.recentCampaigns.slice(0, 3).map((campaign, index) => (\r\n            <div\r\n              key={campaign._id || index}\r\n              className=\"flex items-center justify-between border-b pb-3\"\r\n            >\r\n              <div className=\"flex items-center\">\r\n                <div className=\"w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3\">\r\n                  <Users className=\"h-4 w-4 text-green-600\" />\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium\">{campaign.name}</p>\r\n                  <p className=\"text-xs text-gray-400\">\r\n                    {format(new Date(campaign.startDate), \"MMM d, yyyy\")} - {campaign.endDate ? format(new Date(campaign.endDate), \"MMM d, yyyy\") : \"No End Date\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <Badge\r\n                variant=\"outline\"\r\n                className={`\r\n                  ${\r\n                    campaign.status === \"active\"\r\n                      ? \"bg-green-100 text-green-800 hover:bg-green-100\"\r\n                      : campaign.status === \"paused\"\r\n                      ? \"bg-amber-100 text-amber-800 hover:bg-amber-100\"\r\n                      : \"bg-gray-100 text-gray-800 hover:bg-gray-100\"\r\n                  } border-none\r\n                `}\r\n              >\r\n                {campaign.status === \"active\" ? \"Active\" : \r\n                campaign.status === \"paused\" ? \"Paused\" : \r\n                campaign.status === \"completed\" ? \"Completed\" : \"Inactive\"}\r\n              </Badge>\r\n            </div>\r\n          ))}\r\n          <Link href=\"/campaign\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className=\"w-full text-green-600 hover:text-green-700 hover:bg-green-50\"\r\n            >\r\n            View all campaigns\r\n          </Button>\r\n            </Link>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n\r\n    <Card className=\"border rounded-lg\">\r\n  <CardContent className=\"pt-6\">\r\n    <div className=\"flex justify-between items-center mb-4\">\r\n      <h3 className=\"text-sm font-medium\">Most Used Agents {selectedAgentType !== \"all\" && `(${selectedAgentType})`}</h3>\r\n      <Badge\r\n        variant=\"outline\"\r\n        className=\"bg-purple-50 text-purple-800 hover:bg-purple-50 border-none\"\r\n      >\r\n       {userRole === \"superadmin\" \r\n          ? selectedAgentType === \"all\" \r\n            ? `${dashboardData?.topAgents.length} agents` \r\n            : `${dashboardData?.topAgents.filter(agent => agent.role === selectedAgentType).length} agents`\r\n          : selectedAgentType === \"all\"\r\n            ? `${dashboardData?.topAgents.filter(agent => agent.status === \"active\").length} agents`\r\n            : `${dashboardData?.topAgents.filter(agent => agent.status === \"active\" && agent.role === selectedAgentType).length} agents`\r\n        }\r\n      </Badge>\r\n    </div>\r\n    <div className=\"space-y-4\">\r\n    {dashboardData?.topAgents.slice(0, 5).map((agent, index) => (\r\n      <div\r\n        key={agent.id || index}\r\n        className=\"flex items-center justify-between border-b pb-3\"\r\n      >\r\n        <div className=\"flex items-center\">\r\n          <div className=\"w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3\">\r\n            {agent.avatar ? (\r\n              <img \r\n                src={agent.avatar}\r\n                alt={`${agent.name} avatar`}\r\n                className=\"h-full w-full rounded-full object-cover\"\r\n                onError={(e) => {\r\n                  e.currentTarget.onerror = null;\r\n                  e.currentTarget.src = agentLisa.src;                  \r\n                }}\r\n              />\r\n            ) : (\r\n              <Image \r\n                src={agentLisa}\r\n                alt={`${agent?.name} avatar`}\r\n                width={64}\r\n                height={64}\r\n                className=\"object-cover h-full w-full\"\r\n              />\r\n            )}\r\n          </div>\r\n          <div>\r\n            <p className=\"text-sm font-medium\">{agent?.name || \"Agent\"}</p>\r\n            <p className=\"text-xs text-gray-400\">\r\n              {agent?.role || \"Assistant\"}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <Badge\r\n          variant=\"outline\"\r\n          className=\"bg-purple-100 text-purple-800 hover:bg-purple-100 border-none\"\r\n        >\r\n          {agent?.callCount || 0} calls\r\n        </Badge>\r\n      </div>\r\n    ))}\r\n  <Link href=\"/agents\">\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"sm\"\r\n      className=\"w-full text-purple-600 hover:text-purple-700 hover:bg-purple-50\"\r\n    >\r\n      View all agents\r\n    </Button>\r\n  </Link>\r\n</div>\r\n  </CardContent>\r\n</Card>\r\n\r\n<Card className=\"border rounded-lg\">\r\n  <CardContent className=\"pt-6\">\r\n    <h3 className=\"text-sm font-medium mb-4\">Call End Reasons {selectedAgentType !== \"all\" && `(${selectedAgentType})`}</h3>\r\n    <div className=\"space-y-4\">\r\n      {dashboardData?.callEndReasons.slice(0, 6).map((item, index) => (\r\n        <div\r\n          key={index}\r\n          className=\"flex items-center justify-between border-b pb-3\"\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <div className=\"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3\">\r\n              {getReasonDetails(item.reason).icon}\r\n            </div>\r\n            <div>\r\n              <p className=\"text-sm font-medium\">{getReasonDetails(item.reason).label}</p>\r\n              <p className=\"text-xs text-gray-400\">\r\n              {String(item.count)} calls\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"bg-blue-100 text-blue-800 hover:bg-blue-100 border-none\"\r\n          >\r\n            {item.percentage}%\r\n          </Badge>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  </CardContent>\r\n</Card>\r\n      </div>\r\n\r\n       </FadeIn>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default DashboardContent;\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,4CAA4C;;;AAyCJ;;AAtCxC;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAoBA,SAAS;;IAEP,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAGzE,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;+DAAmB;oBACvB,IAAI;wBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;wBACnC,IAAI,CAAC,OAAO;4BACV,QAAQ,KAAK,CAAC;4BACd;wBACF;wBAEA,MAAM,WAAW,MAAM,MAAM,6DAAsC,YAAY,CAAC,EAAE;4BAChF,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gCAClC,gBAAgB;4BAClB;wBACF;wBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,MAAM,EAAE;wBACpE;wBAEA,MAAM,WAAW,MAAM,SAAS,IAAI;wBACpC,YAAY,SAAS,IAAI;oBAC3B,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;;YAEA;QACF;qCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB;oBACzB,WAAW;oBACX,IAAI;wBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;wBACnC,IAAI,CAAC,OAAO;4BACV,QAAQ,KAAK,CAAC;4BACd,SAAS;4BACT,WAAW;4BACX;wBACF;wBAEA,MAAM,WAAW,MAAM,MACrB,6DAAsC,iCAAiC,EAAE,kBAAkB,WAAW,EAAE,mBAAmB,EAC3H;4BACE,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gCAClC,gBAAgB;4BAClB;wBACF;wBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,MAAM,EAAE;wBACtE;wBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,iBAAiB;oBAEnB,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;qCAAG;QAAC;QAAmB;KAAkB;IAE3C,gDAAgD;IAChD,sBAAsB;IACtB,UAAU;IACV,0DAA0D;IAC1D,oBAAoB;IACpB,oDAAoD;IACpD,2BAA2B;IAC3B,gBAAgB;IAChB,QAAQ;IAER,oCAAoC;IACpC,8EAA8E;IAC9E,UAAU;IACV,0BAA0B;IAC1B,qBAAqB;IACrB,gDAAgD;IAChD,gDAAgD;IAChD,aAAa;IACb,UAAU;IACV,SAAS;IAET,0BAA0B;IAC1B,kFAAkF;IAClF,QAAQ;IAER,+CAA+C;IAC/C,wCAAwC;IACxC,sIAAsI;IACtI,UAAU;IACV,qBAAqB;IACrB,gDAAgD;IAChD,gDAAgD;IAChD,aAAa;IACb,UAAU;IACV,SAAS;IAET,8BAA8B;IAC9B,mFAAmF;IACnF,QAAQ;IAER,8CAA8C;IAC9C,8BAA8B;IAE9B,oBAAoB;IACpB,gEAAgE;IAEhE,gBAAgB;IAChB,yBAAyB;IACzB,MAAM;IACN,KAAK;IAEH,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;QAC7C,MAAM,UAAU,KAAK,KAAK,CAAC,eAAe;QAC1C,MAAM,UAAU,eAAe;QAC/B,OAAO,GAAG,QAAQ,EAAE,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC;IAC9D;IAGA,MAAM,iBAAiB,CAAC;QACtB,MAAM,KAAK,OAAO,eAAe,WAAW,SAAS,cAAc;QACnE,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;QAChC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,KAAK,QAAS;QAC1C,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAGA,MAAM,0BAA0B,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG;QAC7D,MAAM,YAAY,eAAe,YAAY,kBAAkB;QAC/D,IAAI,QAAQ;QAEZ,IAAI,IAAI,GAAG;YACT,QAAQ,YAAY,CAAC,KAAK,CAAC;QAC7B,OAAO,IAAI,IAAI,GAAG;YAChB,QAAQ,YAAY,CAAC,IAAI,CAAC;QAC5B,OAAO,IAAI,MAAM,GAAG;YAClB,QAAQ,YAAY;QACtB;QAEA,OAAO;YACL,KAAK,IAAI;YACT,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QACnC;IACF;IAGA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBAAc,OAAO;gBAAgB;YACvE,KAAK;gBACH,OAAO;oBAAE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAc,OAAO;gBAA0B;YACpF,KAAK;gBACH,OAAO;oBAAE,oBAAM,6LAAC,2MAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBAAc,OAAO;gBAAuB;YAC9E,KAAK;gBACH,OAAO;oBAAE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAc,OAAO;gBAA0B;YACjF,KAAK;gBACN,OAAO;oBAAE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAc,OAAO;gBAAwB;YAClF,KAAK;gBACH,OAAO;oBAAE,oBAAM,6LAAC,mNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;oBAAc,OAAO;gBAAY;YAC3E,KAAK;gBACH,OAAO;oBAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBAAc,OAAO;gBAAoB;YAC3E;gBACE,OAAO;oBAAE,oBAAM,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;oBAAc,OAAO;gBAAwC;QACtG;IACF;IAGA,MAAM,0BAA0B,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG;QAC7D,MAAM,YAAY,eAAe,YAAY,cAAc;QAC3D,IAAI,QAAQ;QAEZ,IAAI,IAAI,GAAG;YACT,QAAQ,YAAY,CAAC,KAAK,CAAC;QAC7B,OAAO,IAAI,IAAI,GAAG;YAChB,QAAQ,YAAY,CAAC,IAAI,CAAC;QAC5B,OAAO,IAAI,MAAM,GAAG;YAClB,QAAQ,YAAY;QACtB;QAEA,OAAO;YACL,KAAK,IAAI;YACT,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QACnC;IACF;IAGA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACb,6LAAC;oBAAE,WAAU;8BAAsB;;;;;;;;;;;;IAG3C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAE;4BAA0B;;;;;;;kCAC7B,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;kCACtC;;;;;;;;;;;;;;;;;IAMT;IAGA,qBACE;kBACA,cAAA,6LAAC,+HAAA,CAAA,UAAM;YAAC,WAAU;;8BAChB,6LAAC;oBAAI,WAAU;;sCACf,6LAAC;4BAAG,WAAU;;gCAAqB;gCACxB,sBAAsB,SAAS,CAAC,CAAC,EAAE,kBAAkB,QAAQ,CAAC;gCACpE,sBAAsB,SAAS,CAAC,QAAQ,EAAE,kBAAkB,KAAK,CAAC;;;;;;;sCAGrE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAsBf,6LAAC,qIAAA,CAAA,SAAM;wCAAC,cAAa;wCACnB,OAAO;wCACP,eAAe;;0DAEf,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kEACZ,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAK;;;;;;kEACvB,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAK;;;;;;kEACvB,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAK;;;;;;;;;;;;;;;;;;kDAgBzB,6LAAC,qIAAA,CAAA,SAAM;wCACH,cAAa;wCACb,OAAO;wCACP,eAAe;;0DAEf,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DAC3B,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;8DACtB,sBAAsB,QAAQ,eAAe,GAAG,kBAAkB,OAAO,CAAC;;;;;;;;;;;0DAG/E,6LAAC,qIAAA,CAAA,gBAAa;;kEACZ,6LAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;oDACvB,eAAe,cAAc,cAAc,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC1D,6LAAC,qIAAA,CAAA,aAAU;4DAAY,OAAO;;gEAC3B;gEAAK;;2DADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAa3B,6LAAC;oBAAI,WAAU;;sCACf,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCAChB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAuD;;;;;;8DAGrE,6LAAC;oDAAI,WAAU;8DACf,cAAA,6LAAC;wDAAK,WAAU;kEAA2B,eAAe,YAAY,cAAc;;;;;;;;;;;;;;;;;sDAGtF,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAOnB,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAuD;;;;;;8DAGrE,6LAAC;oDAAI,WAAU;;sEACf,6LAAC;4DAAK,WAAU;sEACf,eAAe,YAAY,aAAa,QAAQ,MAAM;;;;;;sEAErD,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAG5C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAuD;;;;;;8DAGrE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA2B,mBAAmB,eAAe,YAAY,iBAAiB;;;;;;sEAC1G,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAG5C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCAChB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAuD;;;;;;kEAGrE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;gEACf,eAAe,YAAY,kBAAkB;gEAAE;;;;;;;;;;;;;;;;;;0DAIpD,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;kDAE3B,6LAAC,+HAAA,CAAA,UAAM;wCAAC,OAAO;wCAAK,WAAU;kDAC9B,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAO,QAAO;0DACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;oDACR,MAAM;oDACN,QAAQ;wDAAE,KAAK;wDAAG,OAAO;wDAAG,MAAM;wDAAG,QAAQ;oDAAE;8DAE/C,cAAA,6LAAC,uJAAA,CAAA,OAAI;wDACH,MAAK;wDACL,SAAQ;wDACR,QAAO;wDACP,aAAa;wDACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWjB,6LAAC;oBAAI,WAAU;;sCACf,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DAGrE,6LAAC,gPAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,6LAAC;wCAAI,WAAU;;0DACf,6LAAC;gDAAK,WAAU;0DAA2B,eAAe,YAAY,kBAAkB;;;;;;0DACxF,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAK9C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DAGrE,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,6LAAC;wCAAI,WAAU;;0DACf,6LAAC;gDAAK,WAAU;0DAA2B,eAAe,YAAY,uBAAuB;;;;;;0DAC3F,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAK1C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DAGrE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;kDAElB,6LAAC;wCAAI,WAAU;;0DACf,6LAAC;gDAAI,WAAU;0DAA2B,eAAe,YAAY,iBAAiB;;;;;;0DACxF,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAsB1C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACtB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAuD;;;;;;kEAGrE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;gEACf,eAAe,YAAY,cAAc;gEAAE;;;;;;;;;;;;;;;;;;0DAIhD,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;kDAE3B,6LAAC,+HAAA,CAAA,UAAM;wCAAC,OAAO;wCAAK,WAAU;kDAC9B,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAO,QAAO;0DACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;oDACR,MAAM;oDACN,QAAQ;wDAAE,KAAK;wDAAG,OAAO;wDAAG,MAAM;wDAAG,QAAQ;oDAAE;8DAE/C,cAAA,6LAAC,uJAAA,CAAA,OAAI;wDACH,MAAK;wDACL,SAAQ;wDACR,QAAO;wDACP,aAAa;wDACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWX,6LAAC;oBAAI,WAAU;;sCACf,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACpB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAsB;oDAAc,sBAAsB,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;;;;;;;0DACzG,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;;oDAET,eAAe,YAAY,cAAc;oDAAE;;;;;;;;;;;;;kDAGhD,6LAAC;wCAAI,WAAU;;4CACZ,eAAe,YAAY,IAAI,CAAC,qBAC/B,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;8EAEvB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;;gFAAsB;gFAAE,KAAK,QAAQ;gFAAC;gFAAI,KAAK,YAAY;;;;;;;sFACxE,6LAAC;4EAAE,WAAU;sFACV,CAAA,GAAA,sJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,KAAK,aAAa,GAAG;gFAAE,WAAW;4EAAK;;;;;;;;;;;;;;;;;;sEAI3E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;8EAC3B,6LAAC,oIAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAU;8EAET,eAAe,KAAK,YAAY;;;;;;;;;;;;;mDApB9B,KAAK,GAAG;;;;;0DAyBjB,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAsB;oDACb,sBAAsB,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;;;;;;;0DAE9E,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;;oDAET,eAAe,gBAAgB,UAAU;oDAAE;;;;;;;;;;;;;kDAGhD,6LAAC;wCAAI,WAAU;;4CACZ,eAAe,gBAAgB,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,yBAC3D,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;;gFAAuB,SAAS,QAAQ,CAAC,EAAE,EAAE,QAAQ;gFAAM;gFAAI,SAAS,QAAQ,CAAC,EAAE,EAAE,gBAAgB;;;;;;;sFAClH,6LAAC;4EAAE,WAAU;;gFACV,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,aAAa,GAAG;gFAAsB;gFAAI,SAAS,MAAM,IAAI;;;;;;;;;;;;;;;;;;;sEAI7F,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,CAAC;cACV,EACE,SAAS,MAAM,KAAK,aAChB,mDACA,SAAS,MAAM,KAAK,YACpB,qDACA,2CACL;YACH,CAAC;sEAEA,SAAS,MAAM,IAAI;;;;;;;mDA1BjB,SAAS,GAAG;;;;;0DA8BrB,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUH,6LAAC;oBAAI,WAAU;;sCACf,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAG,WAAU;;4CAA2B;4CAAqB,sBAAsB,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;4CAAC;;;;;;;kDACtH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;kEAE5B,6LAAC;wDAAG,WAAU;kEAAU;;;;;;kEACxB,6LAAC;wDAAE,WAAU;;4DAAqB,eAAe,WAAW,YAAY;4DAAE;;;;;;;;;;;;;0DAG5E,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;kEAE5B,6LAAC;wDAAG,WAAU;kEAAU;;;;;;kEACxB,6LAAC;wDAAE,WAAU;;4DAAqB,eAAe,WAAW,WAAW;4DAAE;;;;;;;;;;;;;0DAG3E,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;kEAE5B,6LAAC;wDAAG,WAAU;kEAAU;;;;;;kEACxB,6LAAC;wDAAE,WAAU;;4DAAqB,eAAe,WAAW,YAAY;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMlF,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCAChB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAsB;oDAAoB,sBAAsB,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;oDAAC;;;;;;;0DAChH,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;;oDAET,eAAe,gBAAgB,UAAU;oDAAE;;;;;;;;;;;;;kDAGhD,6LAAC;wCAAI,WAAU;;4CACZ,eAAe,gBAAgB,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,sBACzD,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAAuB,SAAS,IAAI;;;;;;sFACjD,6LAAC;4EAAE,WAAU;;gFACV,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,SAAS,GAAG;gFAAe;gFAAI,SAAS,OAAO,GAAG,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,OAAO,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;sEAItI,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,CAAC;kBACV,EACE,SAAS,MAAM,KAAK,WAChB,mDACA,SAAS,MAAM,KAAK,WACpB,mDACA,8CACL;gBACH,CAAC;sEAEA,SAAS,MAAM,KAAK,WAAW,WAChC,SAAS,MAAM,KAAK,WAAW,WAC/B,SAAS,MAAM,KAAK,cAAc,cAAc;;;;;;;mDA5B7C,SAAS,GAAG,IAAI;;;;;0DAgCzB,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCAClB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAsB;oDAAkB,sBAAsB,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;;;;;;;0DAC7G,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAEV,aAAa,eACT,sBAAsB,QACpB,GAAG,eAAe,UAAU,OAAO,OAAO,CAAC,GAC3C,GAAG,eAAe,UAAU,OAAO,CAAA,QAAS,MAAM,IAAI,KAAK,mBAAmB,OAAO,OAAO,CAAC,GAC/F,sBAAsB,QACpB,GAAG,eAAe,UAAU,OAAO,CAAA,QAAS,MAAM,MAAM,KAAK,UAAU,OAAO,OAAO,CAAC,GACtF,GAAG,eAAe,UAAU,OAAO,CAAA,QAAS,MAAM,MAAM,KAAK,YAAY,MAAM,IAAI,KAAK,mBAAmB,OAAO,OAAO,CAAC;;;;;;;;;;;;kDAIpI,6LAAC;wCAAI,WAAU;;4CACd,eAAe,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,sBAChD,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,MAAM,MAAM,iBACX,6LAAC;wEACC,KAAK,MAAM,MAAM;wEACjB,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;wEAC3B,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,aAAa,CAAC,OAAO,GAAG;4EAC1B,EAAE,aAAa,CAAC,GAAG,GAAG,uTAAA,CAAA,UAAS,CAAC,GAAG;wEACrC;;;;;6FAGF,6LAAC,gIAAA,CAAA,UAAK;wEACJ,KAAK,uTAAA,CAAA,UAAS;wEACd,KAAK,GAAG,OAAO,KAAK,OAAO,CAAC;wEAC5B,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;8EAIhB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAAuB,OAAO,QAAQ;;;;;;sFACnD,6LAAC;4EAAE,WAAU;sFACV,OAAO,QAAQ;;;;;;;;;;;;;;;;;;sEAItB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;;gEAET,OAAO,aAAa;gEAAE;;;;;;;;mDApCpB,MAAM,EAAE,IAAI;;;;;0DAwCvB,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQL,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAG,WAAU;;4CAA2B;4CAAkB,sBAAsB,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;;;;;;;kDAClH,6LAAC;wCAAI,WAAU;kDACZ,eAAe,eAAe,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,sBACpD,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,iBAAiB,KAAK,MAAM,EAAE,IAAI;;;;;;0EAErC,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAuB,iBAAiB,KAAK,MAAM,EAAE,KAAK;;;;;;kFACvE,6LAAC;wEAAE,WAAU;;4EACZ,OAAO,KAAK,KAAK;4EAAE;;;;;;;;;;;;;;;;;;;kEAIxB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAU;;4DAET,KAAK,UAAU;4DAAC;;;;;;;;+CAlBd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8Bf;GA7zBS;KAAA;uCA+zBM", "debugId": null}}]}