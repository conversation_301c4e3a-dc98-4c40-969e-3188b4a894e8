import { Injectable, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { 
  S3Client, 
  PutObjectCommand, 
  DeleteObjectCommand, 
  HeadBucketCommand,
  ListObjectsV2Command,
  GetObjectCommand
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export interface S3Config {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  bucketName: string;
}

export interface UploadResult {
  key: string;
  url: string;
  size: number;
}

@Injectable()
export class S3Service {
  
  /**
   * Create S3 client with provided credentials
   */
  private createS3Client(config: S3Config): S3Client {
    return new S3Client({
      region: config.region,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });
  }

  /**
   * Test S3 connection and bucket access
   */
  async testConnection(config: S3Config): Promise<{ success: boolean; message: string }> {
    try {
      const s3Client = this.createS3Client(config);
      
      // Test bucket access
      const command = new HeadBucketCommand({ Bucket: config.bucketName });
      await s3Client.send(command);
      
      return {
        success: true,
        message: 'S3 connection successful! Bucket is accessible.',
      };
    } catch (error) {
      console.error('S3 connection test failed:', error);
      
      if (error.name === 'NoSuchBucket') {
        return {
          success: false,
          message: 'Bucket does not exist or is not accessible.',
        };
      } else if (error.name === 'Forbidden') {
        return {
          success: false,
          message: 'Access denied. Check your credentials and bucket permissions.',
        };
      } else if (error.name === 'InvalidAccessKeyId') {
        return {
          success: false,
          message: 'Invalid Access Key ID.',
        };
      } else if (error.name === 'SignatureDoesNotMatch') {
        return {
          success: false,
          message: 'Invalid Secret Access Key.',
        };
      } else {
        return {
          success: false,
          message: `Connection failed: ${error.message || 'Unknown error'}`,
        };
      }
    }
  }

  /**
   * Upload file to S3
   */
  async uploadFile(
    config: S3Config,
    file: Buffer,
    key: string,
    contentType: string,
    organizationId: string,
    knowledgeBaseId?: string
  ): Promise<UploadResult> {
    try {
      const s3Client = this.createS3Client(config);
      
      // Create organized key structure
      const fullKey = knowledgeBaseId 
        ? `organizations/${organizationId}/knowledge-bases/${knowledgeBaseId}/${key}`
        : `organizations/${organizationId}/files/${key}`;
      
      const command = new PutObjectCommand({
        Bucket: config.bucketName,
        Key: fullKey,
        Body: file,
        ContentType: contentType,
        Metadata: {
          organizationId,
          knowledgeBaseId: knowledgeBaseId || '',
          uploadedAt: new Date().toISOString(),
        },
      });

      await s3Client.send(command);

      // Generate the S3 URL
      const url = `https://${config.bucketName}.s3.${config.region}.amazonaws.com/${fullKey}`;

      return {
        key: fullKey,
        url,
        size: file.length,
      };
    } catch (error) {
      console.error('S3 upload failed:', error);
      throw new InternalServerErrorException(`Failed to upload file to S3: ${error.message}`);
    }
  }

  /**
   * Delete file from S3
   */
  async deleteFile(config: S3Config, key: string): Promise<{ success: boolean; message: string }> {
    try {
      const s3Client = this.createS3Client(config);
      
      const command = new DeleteObjectCommand({
        Bucket: config.bucketName,
        Key: key,
      });

      await s3Client.send(command);

      return {
        success: true,
        message: 'File deleted successfully',
      };
    } catch (error) {
      console.error('S3 delete failed:', error);
      throw new InternalServerErrorException(`Failed to delete file from S3: ${error.message}`);
    }
  }

  /**
   * List files in organization folder
   */
  async listFiles(
    config: S3Config,
    organizationId: string,
    knowledgeBaseId?: string
  ): Promise<any[]> {
    try {
      const s3Client = this.createS3Client(config);
      
      const prefix = knowledgeBaseId 
        ? `organizations/${organizationId}/knowledge-bases/${knowledgeBaseId}/`
        : `organizations/${organizationId}/files/`;
      
      const command = new ListObjectsV2Command({
        Bucket: config.bucketName,
        Prefix: prefix,
      });

      const response = await s3Client.send(command);
      
      return response.Contents || [];
    } catch (error) {
      console.error('S3 list files failed:', error);
      throw new InternalServerErrorException(`Failed to list files from S3: ${error.message}`);
    }
  }

  /**
   * Generate presigned URL for file download
   */
  async getPresignedUrl(
    config: S3Config,
    key: string,
    expiresIn: number = 3600
  ): Promise<string> {
    try {
      const s3Client = this.createS3Client(config);
      
      const command = new GetObjectCommand({
        Bucket: config.bucketName,
        Key: key,
      });

      const url = await getSignedUrl(s3Client, command, { expiresIn });
      return url;
    } catch (error) {
      console.error('S3 presigned URL generation failed:', error);
      throw new InternalServerErrorException(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(config: S3Config, key: string): Promise<any> {
    try {
      const s3Client = this.createS3Client(config);
      
      const command = new GetObjectCommand({
        Bucket: config.bucketName,
        Key: key,
      });

      const response = await s3Client.send(command);
      
      return {
        contentType: response.ContentType,
        contentLength: response.ContentLength,
        lastModified: response.LastModified,
        metadata: response.Metadata,
      };
    } catch (error) {
      console.error('S3 get metadata failed:', error);
      throw new InternalServerErrorException(`Failed to get file metadata: ${error.message}`);
    }
  }
}
