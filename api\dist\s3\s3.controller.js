"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3Controller = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const s3_service_1 = require("./s3.service");
const organizations_service_1 = require("../organizations/organizations.service");
class TestS3ConnectionDto {
}
class UploadFileDto {
}
let S3Controller = class S3Controller {
    constructor(s3Service, organizationsService) {
        this.s3Service = s3Service;
        this.organizationsService = organizationsService;
    }
    async testConnection(testDto) {
        try {
            const config = {
                accessKeyId: testDto.accessKeyId,
                secretAccessKey: testDto.secretAccessKey,
                region: testDto.region,
                bucketName: testDto.bucketName,
            };
            const result = await this.s3Service.testConnection(config);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to test S3 connection', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async uploadFile(organizationId, file, uploadDto, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
                const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            const organization = await this.organizationsService.findOne(organizationId);
            if (!organization.s3Config || !organization.s3Config.enabled) {
                throw new common_1.BadRequestException('S3 is not configured or enabled for this organization');
            }
            if (!file) {
                throw new common_1.BadRequestException('No file provided');
            }
            const maxSize = 50 * 1024 * 1024;
            if (file.size > maxSize) {
                throw new common_1.BadRequestException('File size exceeds 50MB limit');
            }
            const config = {
                accessKeyId: organization.s3Config.accessKeyId,
                secretAccessKey: organization.s3Config.secretAccessKey,
                region: organization.s3Config.region,
                bucketName: organization.s3Config.bucketName,
            };
            const timestamp = Date.now();
            const fileName = `${timestamp}-${file.originalname}`;
            const result = await this.s3Service.uploadFile(config, file.buffer, fileName, file.mimetype, organizationId, uploadDto.knowledgeBaseId);
            return {
                success: true,
                message: 'File uploaded successfully',
                data: result,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to upload file', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteFile(organizationId, key, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
                const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            const organization = await this.organizationsService.findOne(organizationId);
            if (!organization.s3Config || !organization.s3Config.enabled) {
                throw new common_1.BadRequestException('S3 is not configured or enabled for this organization');
            }
            const config = {
                accessKeyId: organization.s3Config.accessKeyId,
                secretAccessKey: organization.s3Config.secretAccessKey,
                region: organization.s3Config.region,
                bucketName: organization.s3Config.bucketName,
            };
            const decodedKey = decodeURIComponent(key);
            const result = await this.s3Service.deleteFile(config, decodedKey);
            return {
                success: true,
                message: 'File deleted successfully',
                data: result,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to delete file', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async listFiles(organizationId, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
                const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            const organization = await this.organizationsService.findOne(organizationId);
            if (!organization.s3Config || !organization.s3Config.enabled) {
                throw new common_1.BadRequestException('S3 is not configured or enabled for this organization');
            }
            const config = {
                accessKeyId: organization.s3Config.accessKeyId,
                secretAccessKey: organization.s3Config.secretAccessKey,
                region: organization.s3Config.region,
                bucketName: organization.s3Config.bucketName,
            };
            const files = await this.s3Service.listFiles(config, organizationId);
            return {
                success: true,
                message: 'Files listed successfully',
                data: files,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to list files', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPresignedUrl(organizationId, key, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
                const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            const organization = await this.organizationsService.findOne(organizationId);
            if (!organization.s3Config || !organization.s3Config.enabled) {
                throw new common_1.BadRequestException('S3 is not configured or enabled for this organization');
            }
            const config = {
                accessKeyId: organization.s3Config.accessKeyId,
                secretAccessKey: organization.s3Config.secretAccessKey,
                region: organization.s3Config.region,
                bucketName: organization.s3Config.bucketName,
            };
            const decodedKey = decodeURIComponent(key);
            const url = await this.s3Service.getPresignedUrl(config, decodedKey);
            return {
                success: true,
                message: 'Presigned URL generated successfully',
                data: { url },
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to generate presigned URL', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.S3Controller = S3Controller;
__decorate([
    (0, common_1.Post)('test-connection'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Test S3 connection with provided credentials' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Connection test result' }),
    (0, swagger_1.ApiBody)({ type: TestS3ConnectionDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [TestS3ConnectionDto]),
    __metadata("design:returntype", Promise)
], S3Controller.prototype, "testConnection", null);
__decorate([
    (0, common_1.Post)('upload/:organizationId'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload file to organization S3 bucket' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'File uploaded successfully' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, Object]),
    __metadata("design:returntype", Promise)
], S3Controller.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Delete)(':organizationId/file/:key'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete file from organization S3 bucket' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'File deleted successfully' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('key')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], S3Controller.prototype, "deleteFile", null);
__decorate([
    (0, common_1.Get)(':organizationId/files'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'List files in organization S3 bucket' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Files listed successfully' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], S3Controller.prototype, "listFiles", null);
__decorate([
    (0, common_1.Get)(':organizationId/presigned-url/:key'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get presigned URL for file download' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Presigned URL generated successfully' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('key')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], S3Controller.prototype, "getPresignedUrl", null);
exports.S3Controller = S3Controller = __decorate([
    (0, swagger_1.ApiTags)('S3'),
    (0, common_1.Controller)('s3'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [s3_service_1.S3Service,
        organizations_service_1.OrganizationsService])
], S3Controller);
//# sourceMappingURL=s3.controller.js.map