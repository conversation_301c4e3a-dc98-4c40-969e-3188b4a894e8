{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a1b8b59d._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_321a5c3e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tEM6TKGLe8j5cQrCriCePVJhjSUUbfs7lZlYOl4PwKc=", "__NEXT_PREVIEW_MODE_ID": "dd7528404108bd4d10171a6f87b4cf3e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ec99dd207b44f882504356e0baa12c1fcbcb88e265dbb525c5a140193abe2b7b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3d361f030406f4ba8b7cbd9e7ce4c1bd097c63a96a8e24fe0f8b13bcb1d83a04"}}}, "instrumentation": null, "functions": {}}