import { authFetch } from '@/lib/authFetch';

const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';

// Types
export interface KnowledgeBaseFolder {
  _id: string;
  name: string;
  description?: string;
  organizationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface KnowledgeBaseItem {
  _id: string;
  title: string;
  type: 'document' | 'url' | 'text';
  folderId: string;
  organizationId: string;
  
  // Document-specific fields
  filename?: string;
  originalName?: string;
  mimeType?: string;
  size?: number;
  pages?: number;
  
  // S3 storage information
  s3Key?: string;
  s3Url?: string;
  s3Bucket?: string;
  
  // URL-specific fields
  url?: string;
  lastSynced?: string;
  
  // Text-specific fields
  content?: string;
  
  // Processing status
  status: 'uploading' | 'processing' | 'ready' | 'error';
  errorMessage?: string;
  
  // Metadata
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
  
  // Processing information
  processedAt?: string;
  trieveDatasetId?: string;
  trieveChunkIds?: string[];
}

export interface CreateFolderRequest {
  name: string;
  description?: string;
}

export interface UpdateFolderRequest {
  name?: string;
  description?: string;
}

export interface CreateItemRequest {
  title: string;
  type: 'document' | 'url' | 'text';
  folderId: string;
  url?: string;
  content?: string;
  originalName?: string;
  size?: number;
  pages?: number;
}

export interface UpdateItemRequest {
  title?: string;
  url?: string;
  content?: string;
  status?: 'uploading' | 'processing' | 'ready' | 'error';
  errorMessage?: string;
}

// API Response wrapper
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

// Folder API functions
export const createKnowledgeBaseFolder = async (
  organizationId: string,
  folderData: CreateFolderRequest
): Promise<KnowledgeBaseFolder> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/folders`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(folderData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create folder');
  }

  const result: ApiResponse<KnowledgeBaseFolder> = await response.json();
  return result.data;
};

export const getKnowledgeBaseFolders = async (organizationId: string): Promise<KnowledgeBaseFolder[]> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/folders`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch folders');
  }

  const result: ApiResponse<KnowledgeBaseFolder[]> = await response.json();
  return result.data;
};

export const getKnowledgeBaseFolder = async (
  organizationId: string,
  folderId: string
): Promise<KnowledgeBaseFolder> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/folders/${folderId}`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch folder');
  }

  const result: ApiResponse<KnowledgeBaseFolder> = await response.json();
  return result.data;
};

export const updateKnowledgeBaseFolder = async (
  organizationId: string,
  folderId: string,
  folderData: UpdateFolderRequest
): Promise<KnowledgeBaseFolder> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/folders/${folderId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(folderData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update folder');
  }

  const result: ApiResponse<KnowledgeBaseFolder> = await response.json();
  return result.data;
};

export const deleteKnowledgeBaseFolder = async (
  organizationId: string,
  folderId: string
): Promise<void> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/folders/${folderId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete folder');
  }
};

// Item API functions
export const createKnowledgeBaseItem = async (
  organizationId: string,
  itemData: CreateItemRequest
): Promise<KnowledgeBaseItem> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/items`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(itemData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create item');
  }

  const result: ApiResponse<KnowledgeBaseItem> = await response.json();
  return result.data;
};

export const uploadKnowledgeBaseDocument = async (
  organizationId: string,
  itemId: string,
  file: File
): Promise<KnowledgeBaseItem> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/items/${itemId}/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to upload document');
  }

  const result: ApiResponse<KnowledgeBaseItem> = await response.json();
  return result.data;
};

export const getKnowledgeBaseItems = async (
  organizationId: string,
  folderId: string
): Promise<KnowledgeBaseItem[]> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/folders/${folderId}/items`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch items');
  }

  const result: ApiResponse<KnowledgeBaseItem[]> = await response.json();
  return result.data;
};

export const getKnowledgeBaseItem = async (
  organizationId: string,
  itemId: string
): Promise<KnowledgeBaseItem> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/items/${itemId}`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch item');
  }

  const result: ApiResponse<KnowledgeBaseItem> = await response.json();
  return result.data;
};

export const updateKnowledgeBaseItem = async (
  organizationId: string,
  itemId: string,
  itemData: UpdateItemRequest
): Promise<KnowledgeBaseItem> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/items/${itemId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(itemData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update item');
  }

  const result: ApiResponse<KnowledgeBaseItem> = await response.json();
  return result.data;
};

export const deleteKnowledgeBaseItem = async (
  organizationId: string,
  itemId: string
): Promise<void> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/items/${itemId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete item');
  }
};

export const getKnowledgeBaseItemDownloadUrl = async (
  organizationId: string,
  itemId: string
): Promise<string> => {
  const response = await authFetch(`${API_URL}/api/knowledge-base/organizations/${organizationId}/items/${itemId}/download`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to get download URL');
  }

  const result: ApiResponse<{ url: string }> = await response.json();
  return result.data.url;
};

// Helper functions
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getFileTypeIcon = (filename: string): string => {
  const extension = filename.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'pdf':
      return '📄';
    case 'doc':
    case 'docx':
      return '📝';
    case 'xls':
    case 'xlsx':
      return '📊';
    case 'txt':
      return '📋';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return '🖼️';
    default:
      return '📁';
  }
};
