import { Schema, Types } from 'mongoose';

export const OrganizationSchema = new Schema({
  name: { type: String, required: true },
  description: { type: String },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  // Billing information
  credits: { type: Number, default: 0 }, // Paid credits
  // Monthly free credits tracking
  monthlyFreeCredits: { type: Number, default: 0 }, // Monthly allowance in minutes
  monthlyFreeCreditsUsed: { type: Number, default: 0 }, // Used free credits this month
  lastMonthlyReset: { type: Date, default: Date.now }, // Last time monthly credits were reset
  usingFreeCredits: { type: Boolean, default: true }, // Whether currently using free credits
  monthlyResetDate: { type: Number, default: 1 }, // Day of month (1-28) when credits reset
  // Auto-recharge settings
  autoRechargeEnabled: { type: Boolean, default: false },
  autoRechargeThreshold: { type: Number, default: 1.0 }, // Default $1.0 minimum credits
  autoRechargeAmount: { type: Number, default: 0 }, // 0 means use last payment amount
  // Billing configuration settings (moved from global settings)
  callPricePerMinute: { type: Number, default: 0.10 }, // Default $0.10 per minute
  minimumCreditsThreshold: { type: Number, default: 1.0 }, // Default $1.0 minimum credits
  monthlyMinutesAllowance: { type: Number, default: 0 }, // Default 0 minutes per month (disabled)
  // Stripe customer ID for the organization
  stripeCustomerId: { type: String },
  // Email notification settings
  fullName: { type: String }, // Client's full name for email personalization
  email: { type: String }, // Email address to send credit notifications to
  lastWarningEmailSent: { type: Date }, // Last time warning email was sent
  lastRunoutEmailSent: { type: Date }, // Last time runout email was sent
  // S3 Configuration for knowledge base file storage
  s3Config: {
    accessKeyId: { type: String },
    secretAccessKey: { type: String },
    region: { type: String },
    bucketName: { type: String },
    enabled: { type: Boolean, default: false }
  },
  // Admin users who can manage the organization
  adminUsers: [{ type: Types.ObjectId, ref: 'User' }],
  // Regular users who belong to the organization
  users: [{ type: Types.ObjectId, ref: 'User' }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

OrganizationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});
