"use client";

import { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  FileText,
  Link as LinkIcon,
  Type,
  Upload,
  X,
  Loader2
} from "lucide-react";
import { uploadFileToS3 } from "@/app/api/s3";
import { toast } from "sonner";

interface AddContentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (type: "document" | "url" | "text", data: any) => void;
  organizationId?: string;
  knowledgeBaseId?: string;
}

export function AddContentModal({
  isOpen,
  onClose,
  onAdd,
  organizationId,
  knowledgeBaseId
}: AddContentModalProps) {
  const [activeTab, setActiveTab] = useState("document");

  // Document upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentTitle, setDocumentTitle] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // URL state
  const [url, setUrl] = useState("");
  const [urlTitle, setUrlTitle] = useState("");

  // Text state
  const [textTitle, setTextTitle] = useState("");
  const [textContent, setTextContent] = useState("");

  const [errorMessage, setErrorMessage] = useState("");

  const allowedTypes = [".pdf", ".doc", ".docx", ".txt", ".rtf", ".xlsx", ".xls", ".csv"];

  const validateAndSetFile = (file: File) => {
    const fileExtension = "." + file.name.split('.').pop()?.toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
      setErrorMessage(`File type not supported. Allowed types: ${allowedTypes.join(', ')}`);
      return;
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      setErrorMessage("File size must be less than 50MB");
      return;
    }

    setSelectedFile(file);
    setDocumentTitle(file.name.replace(/\.[^/.]+$/, "")); // Remove extension
    setErrorMessage("");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      validateAndSetFile(e.target.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndSetFile(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = async () => {
    setErrorMessage("");
    setIsUploading(true);

    try {
      if (activeTab === "document") {
        if (!selectedFile) {
          setErrorMessage("Please select a file");
          return;
        }
        if (!documentTitle.trim()) {
          setErrorMessage("Please enter a title");
          return;
        }

        let s3Data = null;

        // Try to upload to S3 if organization ID is provided
        if (organizationId) {
          try {
            const uploadResult = await uploadFileToS3(
              organizationId,
              selectedFile,
              knowledgeBaseId
            );

            if (uploadResult.success) {
              s3Data = uploadResult.data;
              toast.success("File uploaded to S3 successfully!");
            }
          } catch (error) {
            console.warn("S3 upload failed, falling back to local storage:", error);
            toast.warning("S3 upload failed, using local storage instead");
          }
        }

        onAdd("document", {
          title: documentTitle.trim(),
          filename: selectedFile.name,
          size: selectedFile.size,
          file: selectedFile,
          s3Data: s3Data // Include S3 data if upload was successful
        });
      } else if (activeTab === "url") {
        if (!url.trim()) {
          setErrorMessage("Please enter a URL");
          return;
        }
        if (!urlTitle.trim()) {
          setErrorMessage("Please enter a title");
          return;
        }
        // Basic URL validation
        try {
          new URL(url);
        } catch {
          setErrorMessage("Please enter a valid URL");
          return;
        }
        onAdd("url", {
          title: urlTitle.trim(),
          url: url.trim()
        });
      } else if (activeTab === "text") {
        if (!textTitle.trim()) {
          setErrorMessage("Please enter a title");
          return;
        }
        if (!textContent.trim()) {
          setErrorMessage("Please enter some content");
          return;
        }
        onAdd("text", {
          title: textTitle.trim(),
          content: textContent.trim(),
          size: new Blob([textContent]).size
        });
      }

      handleClose();
    } catch (error) {
      console.error("Submit error:", error);
      setErrorMessage(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setDocumentTitle("");
    setUrl("");
    setUrlTitle("");
    setTextTitle("");
    setTextContent("");
    setErrorMessage("");
    setIsUploading(false);
    setActiveTab("document");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Add Content to Knowledge Base</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="document" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Document</span>
            </TabsTrigger>
            <TabsTrigger value="url" className="flex items-center space-x-2">
              <LinkIcon className="h-4 w-4" />
              <span>URL</span>
            </TabsTrigger>
            <TabsTrigger value="text" className="flex items-center space-x-2">
              <Type className="h-4 w-4" />
              <span>Text</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="document" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="doc-title">Title *</Label>
              <Input
                id="doc-title"
                placeholder="Enter document title..."
                value={documentTitle}
                onChange={(e) => setDocumentTitle(e.target.value)}
              />
            </div>

            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragging
                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                  : "border-gray-300 dark:border-gray-700"
              } ${
                selectedFile ? "bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-700" : ""
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept={allowedTypes.join(",")}
                onChange={handleFileChange}
              />

              {selectedFile ? (
                <div className="space-y-2">
                  <FileText className="h-10 w-10 text-green-600 mx-auto" />
                  <p className="font-medium">{selectedFile.name}</p>
                  <p className="text-sm text-gray-500">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedFile(null);
                      setDocumentTitle("");
                    }}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                </div>
              ) : (
                <>
                  <Upload className="h-10 w-10 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Drag and drop your file here
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mb-3">or</p>
                  <Button variant="outline" size="sm" type="button">
                    Browse Files
                  </Button>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-4">
                    Supported formats: {allowedTypes.join(', ')}
                  </p>
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="url" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="url-title">Title *</Label>
              <Input
                id="url-title"
                placeholder="Enter a title for this URL..."
                value={urlTitle}
                onChange={(e) => setUrlTitle(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="url-input">URL *</Label>
              <Input
                id="url-input"
                placeholder="https://example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
              />
            </div>
          </TabsContent>

          <TabsContent value="text" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="text-title">Title *</Label>
              <Input
                id="text-title"
                placeholder="Enter a title for this text..."
                value={textTitle}
                onChange={(e) => setTextTitle(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="text-content">Content *</Label>
              <Textarea
                id="text-content"
                placeholder="Enter your text content here..."
                value={textContent}
                onChange={(e) => setTextContent(e.target.value)}
                rows={6}
              />
            </div>
          </TabsContent>
        </Tabs>

        {errorMessage && (
          <p className="text-sm text-red-500">{errorMessage}</p>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isUploading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isUploading}>
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {activeTab === "document" ? "Uploading..." : "Adding..."}
              </>
            ) : (
              "Add Content"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
