{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/braincomponents/KnowledgeBaseFolderSidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { \n  Folder, \n  Plus, \n  Search, \n  MoreVertical, \n  Trash2, \n  Edit,\n  FolderOpen\n} from \"lucide-react\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\n\ntype KnowledgeBaseFolder = {\n  id: string;\n  name: string;\n  description?: string;\n  createdAt: Date;\n  itemCount: number;\n};\n\ntype KnowledgeBaseFolderSidebarProps = {\n  folders: KnowledgeBaseFolder[];\n  selectedFolderId: string | null;\n  onSelectFolder: (folderId: string) => void;\n  onCreateFolder: () => void;\n  onDeleteFolder: (folderId: string) => void;\n};\n\nexport function KnowledgeBaseFolderSidebar({\n  folders,\n  selectedFolderId,\n  onSelectFolder,\n  onCreateFolder,\n  onDeleteFolder,\n}: KnowledgeBaseFolderSidebarProps) {\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const filteredFolders = folders.filter(folder =>\n    folder.name.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Knowledge Base\n          </h2>\n          <Button\n            size=\"sm\"\n            onClick={onCreateFolder}\n            className=\"h-8 w-8 p-0\"\n          >\n            <Plus className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        {/* Search */}\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <Input\n            placeholder=\"Search folders...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"pl-10 h-9\"\n          />\n        </div>\n      </div>\n\n      {/* Folders List */}\n      <div className=\"flex-1 overflow-auto p-2\">\n        {filteredFolders.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center h-32 text-center\">\n            <Folder className=\"h-8 w-8 text-gray-400 mb-2\" />\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {searchQuery ? \"No folders found\" : \"No folders yet\"}\n            </p>\n            {!searchQuery && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onCreateFolder}\n                className=\"mt-2\"\n              >\n                <Plus className=\"h-4 w-4 mr-1\" />\n                Create Folder\n              </Button>\n            )}\n          </div>\n        ) : (\n          <div className=\"space-y-1\">\n            {filteredFolders.map((folder) => (\n              <div\n                key={folder.id}\n                className={`group relative rounded-lg p-3 cursor-pointer transition-colors ${\n                  selectedFolderId === folder.id\n                    ? \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800\"\n                    : \"hover:bg-gray-50 dark:hover:bg-gray-700/50\"\n                }`}\n                onClick={() => onSelectFolder(folder.id)}\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-start space-x-3 flex-1 min-w-0\">\n                    {selectedFolderId === folder.id ? (\n                      <FolderOpen className=\"h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0\" />\n                    ) : (\n                      <Folder className=\"h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0\" />\n                    )}\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"font-medium text-sm text-gray-900 dark:text-white truncate\">\n                        {folder.name}\n                      </h3>\n                      {folder.description && (\n                        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2\">\n                          {folder.description}\n                        </p>\n                      )}\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                          {folder.itemCount} items\n                        </span>\n                        <span className=\"text-xs text-gray-400\">\n                          {folder.createdAt.toLocaleDateString()}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\n                        onClick={(e) => e.stopPropagation()}\n                      >\n                        <MoreVertical className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\">\n                      <DropdownMenuItem>\n                        <Edit className=\"h-4 w-4 mr-2\" />\n                        Edit\n                      </DropdownMenuItem>\n                      <DropdownMenuItem\n                        className=\"text-red-600 dark:text-red-400\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          onDeleteFolder(folder.id);\n                        }}\n                      >\n                        <Trash2 className=\"h-4 w-4 mr-2\" />\n                        Delete\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAdA;;;;;;AAqCO,SAAS,2BAA2B,EACzC,OAAO,EACP,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,cAAc,EACkB;;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG5D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAE,WAAU;sCACV,cAAc,qBAAqB;;;;;;wBAErC,CAAC,6BACA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;yCAMvC,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;4BAEC,WAAW,CAAC,+DAA+D,EACzE,qBAAqB,OAAO,EAAE,GAC1B,+EACA,8CACJ;4BACF,SAAS,IAAM,eAAe,OAAO,EAAE;sCAEvC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,qBAAqB,OAAO,EAAE,iBAC7B,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;qEAEtB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,OAAO,IAAI;;;;;;oDAEb,OAAO,WAAW,kBACjB,6LAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;kEAGvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEACb,OAAO,SAAS;oEAAC;;;;;;;0EAEpB,6LAAC;gEAAK,WAAU;0EACb,OAAO,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;kDAM5C,6LAAC,+IAAA,CAAA,eAAY;;0DACX,6LAAC,+IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,CAAC,IAAM,EAAE,eAAe;8DAEjC,cAAA,6LAAC,6NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAG5B,6LAAC,+IAAA,CAAA,sBAAmB;gDAAC,OAAM;;kEACzB,6LAAC,+IAAA,CAAA,mBAAgB;;0EACf,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,6LAAC,+IAAA,CAAA,mBAAgB;wDACf,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,eAAe,OAAO,EAAE;wDAC1B;;0EAEA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;2BA1DtC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;AAuE9B;GAzIgB;KAAA", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/braincomponents/KnowledgeBaseContent.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Plus, \n  Search, \n  FileText, \n  Link as LinkIcon, \n  Type,\n  MoreVertical,\n  Trash2,\n  ExternalLink,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Loader2\n} from \"lucide-react\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\n\ntype KnowledgeBaseFolder = {\n  id: string;\n  name: string;\n  description?: string;\n  createdAt: Date;\n  itemCount: number;\n};\n\ntype KnowledgeBaseItem = {\n  id: string;\n  title: string;\n  type: \"document\" | \"url\" | \"text\";\n  content?: string;\n  url?: string;\n  filename?: string;\n  size?: number;\n  pages?: number;\n  status: \"processing\" | \"ready\" | \"error\";\n  uploadedAt: Date;\n  lastSynced?: Date;\n  folderId: string;\n};\n\ntype KnowledgeBaseContentProps = {\n  folder: KnowledgeBaseFolder | null;\n  items: KnowledgeBaseItem[];\n  searchQuery: string;\n  onSearchChange: (query: string) => void;\n  onAddContent: () => void;\n  onDeleteItem: (itemId: string) => void;\n};\n\nexport function KnowledgeBaseContent({\n  folder,\n  items,\n  searchQuery,\n  onSearchChange,\n  onAddContent,\n  onDeleteItem,\n}: KnowledgeBaseContentProps) {\n  const filteredItems = items.filter(item =>\n    item.title.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  const getItemIcon = (type: string) => {\n    switch (type) {\n      case \"document\":\n        return <FileText className=\"h-5 w-5\" />;\n      case \"url\":\n        return <LinkIcon className=\"h-5 w-5\" />;\n      case \"text\":\n        return <Type className=\"h-5 w-5\" />;\n      default:\n        return <FileText className=\"h-5 w-5\" />;\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case \"ready\":\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case \"processing\":\n        return <Loader2 className=\"h-4 w-4 text-blue-500 animate-spin\" />;\n      case \"error\":\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      default:\n        return <Clock className=\"h-4 w-4 text-gray-400\" />;\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"ready\":\n        return <Badge variant=\"default\" className=\"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">Ready</Badge>;\n      case \"processing\":\n        return <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">Processing</Badge>;\n      case \"error\":\n        return <Badge variant=\"destructive\">Error</Badge>;\n      default:\n        return <Badge variant=\"outline\">Unknown</Badge>;\n    }\n  };\n\n  const formatFileSize = (bytes?: number) => {\n    if (!bytes) return \"\";\n    const sizes = [\"B\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;\n  };\n\n  if (!folder) {\n    return (\n      <div className=\"flex items-center justify-center h-full\">\n        <div className=\"text-center\">\n          <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n            No folder selected\n          </h3>\n          <p className=\"text-gray-500 dark:text-gray-400\">\n            Select a folder from the sidebar to view its contents\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-white\">\n              {folder.name}\n            </h1>\n            <div className=\"flex items-center space-x-4 mt-2\">\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                ID: know...{folder.id.slice(-3)}\n              </span>\n              <div className=\"flex items-center space-x-1\">\n                <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  Uploaded by: {folder.createdAt.toLocaleDateString()} {folder.createdAt.toLocaleTimeString()}\n                </span>\n              </div>\n            </div>\n          </div>\n          <Button onClick={onAddContent}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Add Content\n          </Button>\n        </div>\n\n        {/* Search */}\n        <div className=\"relative max-w-md\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <Input\n            placeholder=\"Search content...\"\n            value={searchQuery}\n            onChange={(e) => onSearchChange(e.target.value)}\n            className=\"pl-10\"\n          />\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-auto p-6\">\n        {filteredItems.length === 0 ? (\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"text-center\">\n              <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                {searchQuery ? \"No content found\" : \"No content yet\"}\n              </h3>\n              <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n                {searchQuery \n                  ? \"Try adjusting your search terms\"\n                  : \"Start by adding documents, URLs, or text content to this folder\"\n                }\n              </p>\n              {!searchQuery && (\n                <Button onClick={onAddContent}>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Content\n                </Button>\n              )}\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {filteredItems.map((item) => (\n              <div\n                key={item.id}\n                className=\"group border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow bg-white dark:bg-gray-800\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-start space-x-4 flex-1\">\n                    {/* Icon */}\n                    <div className={`p-2 rounded-lg ${\n                      item.type === \"url\" ? \"bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400\" :\n                      item.type === \"text\" ? \"bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400\" :\n                      \"bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400\"\n                    }`}>\n                      {getItemIcon(item.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center space-x-2 mb-2\">\n                        <h3 className=\"font-medium text-gray-900 dark:text-white truncate\">\n                          {item.title}\n                        </h3>\n                        {getStatusBadge(item.status)}\n                      </div>\n\n                      {/* Metadata */}\n                      <div className=\"space-y-1\">\n                        {item.type === \"url\" && item.url && (\n                          <div className=\"flex items-center space-x-2\">\n                            <a\n                              href={item.url}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center\"\n                            >\n                              {item.url}\n                              <ExternalLink className=\"h-3 w-3 ml-1\" />\n                            </a>\n                          </div>\n                        )}\n                        \n                        {item.type === \"document\" && item.filename && (\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                            {item.filename}\n                          </p>\n                        )}\n\n                        <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\n                          {item.pages && (\n                            <span>{item.pages} Pages</span>\n                          )}\n                          {item.size && (\n                            <span>{formatFileSize(item.size)}</span>\n                          )}\n                          {item.lastSynced && (\n                            <span>Last synced on {item.lastSynced.toLocaleDateString()} {item.lastSynced.toLocaleTimeString()}</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Actions */}\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\n                      >\n                        <MoreVertical className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\">\n                      <DropdownMenuItem\n                        className=\"text-red-600 dark:text-red-400\"\n                        onClick={() => onDeleteItem(item.id)}\n                      >\n                        <Trash2 className=\"h-4 w-4 mr-2\" />\n                        Delete\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAnBA;;;;;;;AA0DO,SAAS,qBAAqB,EACnC,MAAM,EACN,KAAK,EACL,WAAW,EACX,cAAc,EACd,YAAY,EACZ,YAAY,EACc;IAC1B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG3D,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;g<PERSON><PERSON>,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAoE;;;;;;YAChH,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,WAAU;8BAAgE;;;;;;YAC9G,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAU;;;;;;QACpC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;IAChE;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,OAAO,IAAI;;;;;;kDAEd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAA2C;oDAC7C,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;0DAE/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;wDAAK,WAAU;;4DAA2C;4DAC3C,OAAO,SAAS,CAAC,kBAAkB;4DAAG;4DAAE,OAAO,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0CAKjG,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;0BACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CACX,cAAc,qBAAqB;;;;;;0CAEtC,6LAAC;gCAAE,WAAU;0CACV,cACG,oCACA;;;;;;4BAGL,CAAC,6BACA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;yCAOzC,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;4BAEC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAW,CAAC,eAAe,EAC9B,KAAK,IAAI,KAAK,QAAQ,6EACtB,KAAK,IAAI,KAAK,SAAS,qEACvB,gEACA;0DACC,YAAY,KAAK,IAAI;;;;;;0DAIxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,KAAK,KAAK;;;;;;4DAEZ,eAAe,KAAK,MAAM;;;;;;;kEAI7B,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI,KAAK,SAAS,KAAK,GAAG,kBAC9B,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,MAAM,KAAK,GAAG;oEACd,QAAO;oEACP,KAAI;oEACJ,WAAU;;wEAET,KAAK,GAAG;sFACT,6LAAC,yNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;4DAK7B,KAAK,IAAI,KAAK,cAAc,KAAK,QAAQ,kBACxC,6LAAC;gEAAE,WAAU;0EACV,KAAK,QAAQ;;;;;;0EAIlB,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,KAAK,kBACT,6LAAC;;4EAAM,KAAK,KAAK;4EAAC;;;;;;;oEAEnB,KAAK,IAAI,kBACR,6LAAC;kFAAM,eAAe,KAAK,IAAI;;;;;;oEAEhC,KAAK,UAAU,kBACd,6LAAC;;4EAAK;4EAAgB,KAAK,UAAU,CAAC,kBAAkB;4EAAG;4EAAE,KAAK,UAAU,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQzG,6LAAC,+IAAA,CAAA,eAAY;;0DACX,6LAAC,+IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,6LAAC,6NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAG5B,6LAAC,+IAAA,CAAA,sBAAmB;gDAAC,OAAM;0DACzB,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;oDACf,WAAU;oDACV,SAAS,IAAM,aAAa,KAAK,EAAE;;sEAEnC,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;2BA5EtC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAyF5B;KArOgB", "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gXACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sbACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1308, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/braincomponents/CreateFolderModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>ontent,\n  <PERSON><PERSON><PERSON>eader,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  DialogFooter,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Folder } from \"lucide-react\";\n\ninterface CreateFolderModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onCreate: (name: string, description?: string) => void;\n}\n\nexport function CreateFolderModal({ \n  isOpen, \n  onClose,\n  onCreate \n}: CreateFolderModalProps) {\n  const [name, setName] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [errorMessage, setErrorMessage] = useState(\"\");\n\n  const handleSubmit = () => {\n    if (!name.trim()) {\n      setErrorMessage(\"Please enter a folder name\");\n      return;\n    }\n\n    onCreate(name.trim(), description.trim() || undefined);\n    handleClose();\n  };\n\n  const handleClose = () => {\n    setName(\"\");\n    setDescription(\"\");\n    setErrorMessage(\"\");\n    onClose();\n  };\n\n  return (\n    <Dialog open={isOpen} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center space-x-2\">\n            <Folder className=\"h-5 w-5\" />\n            <span>Create Knowledge Base Folder</span>\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"folder-name\">Folder Name *</Label>\n            <Input\n              id=\"folder-name\"\n              placeholder=\"Enter folder name...\"\n              value={name}\n              onChange={(e) => {\n                setName(e.target.value);\n                setErrorMessage(\"\");\n              }}\n              onKeyDown={(e) => {\n                if (e.key === \"Enter\" && !e.shiftKey) {\n                  e.preventDefault();\n                  handleSubmit();\n                }\n              }}\n            />\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"folder-description\">Description (Optional)</Label>\n            <Textarea\n              id=\"folder-description\"\n              placeholder=\"Enter a brief description of this knowledge base...\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              rows={3}\n            />\n          </div>\n\n          {errorMessage && (\n            <p className=\"text-sm text-red-500\">{errorMessage}</p>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose}>\n            Cancel\n          </Button>\n          <Button onClick={handleSubmit}>\n            Create Folder\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;AAsBO,SAAS,kBAAkB,EAChC,MAAM,EACN,OAAO,EACP,QAAQ,EACe;;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QACnB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,gBAAgB;YAChB;QACF;QAEA,SAAS,KAAK,IAAI,IAAI,YAAY,IAAI,MAAM;QAC5C;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,eAAe;QACf,gBAAgB;QAChB;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC;wCACT,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACtB,gBAAgB;oCAClB;oCACA,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;4CACpC,EAAE,cAAc;4CAChB;wCACF;oCACF;;;;;;;;;;;;sCAIJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAqB;;;;;;8CACpC,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,MAAM;;;;;;;;;;;;wBAIT,8BACC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAIzC,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAa;;;;;;sCAGhD,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;sCAAc;;;;;;;;;;;;;;;;;;;;;;;AAOzC;GAnFgB;KAAA", "debugId": null}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2fACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1590, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/s3.ts"], "sourcesContent": ["import { authFetch } from '@/lib/authFetch';\n\nconst API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';\n\nexport interface S3Config {\n  accessKeyId: string;\n  secretAccessKey: string;\n  region: string;\n  bucketName: string;\n}\n\nexport interface S3TestResult {\n  success: boolean;\n  message: string;\n}\n\nexport interface S3UploadResult {\n  success: boolean;\n  message: string;\n  data: {\n    key: string;\n    url: string;\n    size: number;\n  };\n}\n\nexport interface S3DeleteResult {\n  success: boolean;\n  message: string;\n}\n\nexport interface S3ListResult {\n  success: boolean;\n  message: string;\n  data: Array<{\n    Key: string;\n    LastModified: string;\n    Size: number;\n    StorageClass: string;\n  }>;\n}\n\n/**\n * Test S3 connection with provided credentials\n */\nexport const testS3Connection = async (config: S3Config): Promise<S3TestResult> => {\n  const response = await authFetch(`${API_URL}/api/s3/test-connection`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(config),\n  });\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to test S3 connection');\n  }\n\n  return response.json();\n};\n\n/**\n * Upload file to organization S3 bucket\n */\nexport const uploadFileToS3 = async (\n  organizationId: string,\n  file: File,\n  knowledgeBaseId?: string\n): Promise<S3UploadResult> => {\n  const formData = new FormData();\n  formData.append('file', file);\n  \n  if (knowledgeBaseId) {\n    formData.append('knowledgeBaseId', knowledgeBaseId);\n  }\n\n  const response = await authFetch(`${API_URL}/api/s3/upload/${organizationId}`, {\n    method: 'POST',\n    body: formData,\n  });\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to upload file to S3');\n  }\n\n  return response.json();\n};\n\n/**\n * Delete file from organization S3 bucket\n */\nexport const deleteFileFromS3 = async (\n  organizationId: string,\n  fileKey: string\n): Promise<S3DeleteResult> => {\n  // URL encode the file key to handle special characters\n  const encodedKey = encodeURIComponent(fileKey);\n  \n  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/file/${encodedKey}`, {\n    method: 'DELETE',\n  });\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to delete file from S3');\n  }\n\n  return response.json();\n};\n\n/**\n * List files in organization S3 bucket\n */\nexport const listS3Files = async (organizationId: string): Promise<S3ListResult> => {\n  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/files`);\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to list S3 files');\n  }\n\n  return response.json();\n};\n\n/**\n * Get presigned URL for file download\n */\nexport const getS3PresignedUrl = async (\n  organizationId: string,\n  fileKey: string\n): Promise<{ success: boolean; message: string; data: { url: string } }> => {\n  // URL encode the file key to handle special characters\n  const encodedKey = encodeURIComponent(fileKey);\n  \n  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/presigned-url/${encodedKey}`);\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to get presigned URL');\n  }\n\n  return response.json();\n};\n\n/**\n * Helper function to format file size\n */\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\n/**\n * Helper function to get file extension\n */\nexport const getFileExtension = (filename: string): string => {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\n};\n\n/**\n * Helper function to get file type icon\n */\nexport const getFileTypeIcon = (filename: string): string => {\n  const extension = getFileExtension(filename).toLowerCase();\n  \n  switch (extension) {\n    case 'pdf':\n      return '📄';\n    case 'doc':\n    case 'docx':\n      return '📝';\n    case 'xls':\n    case 'xlsx':\n      return '📊';\n    case 'txt':\n      return '📋';\n    case 'jpg':\n    case 'jpeg':\n    case 'png':\n    case 'gif':\n      return '🖼️';\n    case 'mp4':\n    case 'avi':\n    case 'mov':\n      return '🎥';\n    case 'mp3':\n    case 'wav':\n      return '🎵';\n    case 'zip':\n    case 'rar':\n      return '📦';\n    default:\n      return '📁';\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEgB;AAFhB;;AAEA,MAAM,UAAU,6DAAsC;AA2C/C,MAAM,mBAAmB,OAAO;IACrC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,uBAAuB,CAAC,EAAE;QACpE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,iBAAiB,OAC5B,gBACA,MACA;IAEA,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IAExB,IAAI,iBAAiB;QACnB,SAAS,MAAM,CAAC,mBAAmB;IACrC;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,eAAe,EAAE,gBAAgB,EAAE;QAC7E,QAAQ;QACR,MAAM;IACR;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,mBAAmB,OAC9B,gBACA;IAEA,uDAAuD;IACvD,MAAM,aAAa,mBAAmB;IAEtC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,QAAQ,EAAE,eAAe,MAAM,EAAE,YAAY,EAAE;QACzF,QAAQ;IACV;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,QAAQ,EAAE,eAAe,MAAM,CAAC;IAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,oBAAoB,OAC/B,gBACA;IAEA,uDAAuD;IACvD,MAAM,aAAa,mBAAmB;IAEtC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,QAAQ,EAAE,eAAe,eAAe,EAAE,YAAY;IAElG,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAKO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,iBAAiB,UAAU,WAAW;IAExD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1720, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/braincomponents/AddContentModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>ontent,\n  <PERSON><PERSON><PERSON>eader,\n  Di<PERSON>Title,\n  DialogFooter,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport {\n  FileText,\n  Link as LinkIcon,\n  Type,\n  Upload,\n  X,\n  Loader2\n} from \"lucide-react\";\nimport { uploadFileToS3 } from \"@/app/api/s3\";\nimport { toast } from \"sonner\";\n\ninterface AddContentModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onAdd: (type: \"document\" | \"url\" | \"text\", data: any) => void;\n  organizationId?: string;\n  knowledgeBaseId?: string;\n}\n\nexport function AddContentModal({\n  isOpen,\n  onClose,\n  onAdd,\n  organizationId,\n  knowledgeBaseId\n}: AddContentModalProps) {\n  const [activeTab, setActiveTab] = useState(\"document\");\n\n  // Document upload state\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [documentTitle, setDocumentTitle] = useState(\"\");\n  const [isDragging, setIsDragging] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // URL state\n  const [url, setUrl] = useState(\"\");\n  const [urlTitle, setUrlTitle] = useState(\"\");\n\n  // Text state\n  const [textTitle, setTextTitle] = useState(\"\");\n  const [textContent, setTextContent] = useState(\"\");\n\n  const [errorMessage, setErrorMessage] = useState(\"\");\n\n  const allowedTypes = [\".pdf\", \".doc\", \".docx\", \".txt\", \".rtf\", \".xlsx\", \".xls\", \".csv\"];\n\n  const validateAndSetFile = (file: File) => {\n    const fileExtension = \".\" + file.name.split('.').pop()?.toLowerCase();\n\n    if (!allowedTypes.includes(fileExtension)) {\n      setErrorMessage(`File type not supported. Allowed types: ${allowedTypes.join(', ')}`);\n      return;\n    }\n\n    if (file.size > 50 * 1024 * 1024) { // 50MB limit\n      setErrorMessage(\"File size must be less than 50MB\");\n      return;\n    }\n\n    setSelectedFile(file);\n    setDocumentTitle(file.name.replace(/\\.[^/.]+$/, \"\")); // Remove extension\n    setErrorMessage(\"\");\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files && e.target.files[0]) {\n      validateAndSetFile(e.target.files[0]);\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    setIsDragging(false);\n  };\n\n  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {\n    e.preventDefault();\n    setIsDragging(false);\n\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      validateAndSetFile(e.dataTransfer.files[0]);\n    }\n  };\n\n  const handleSubmit = async () => {\n    setErrorMessage(\"\");\n    setIsUploading(true);\n\n    try {\n      if (activeTab === \"document\") {\n        if (!selectedFile) {\n          setErrorMessage(\"Please select a file\");\n          return;\n        }\n        if (!documentTitle.trim()) {\n          setErrorMessage(\"Please enter a title\");\n          return;\n        }\n\n        let s3Data = null;\n\n        // Try to upload to S3 if organization ID is provided\n        if (organizationId) {\n          try {\n            const uploadResult = await uploadFileToS3(\n              organizationId,\n              selectedFile,\n              knowledgeBaseId\n            );\n\n            if (uploadResult.success) {\n              s3Data = uploadResult.data;\n              toast.success(\"File uploaded to S3 successfully!\");\n            }\n          } catch (error) {\n            console.warn(\"S3 upload failed, falling back to local storage:\", error);\n            toast.warning(\"S3 upload failed, using local storage instead\");\n          }\n        }\n\n        onAdd(\"document\", {\n          title: documentTitle.trim(),\n          filename: selectedFile.name,\n          size: selectedFile.size,\n          file: selectedFile,\n          s3Data: s3Data // Include S3 data if upload was successful\n        });\n      } else if (activeTab === \"url\") {\n        if (!url.trim()) {\n          setErrorMessage(\"Please enter a URL\");\n          return;\n        }\n        if (!urlTitle.trim()) {\n          setErrorMessage(\"Please enter a title\");\n          return;\n        }\n        // Basic URL validation\n        try {\n          new URL(url);\n        } catch {\n          setErrorMessage(\"Please enter a valid URL\");\n          return;\n        }\n        onAdd(\"url\", {\n          title: urlTitle.trim(),\n          url: url.trim()\n        });\n      } else if (activeTab === \"text\") {\n        if (!textTitle.trim()) {\n          setErrorMessage(\"Please enter a title\");\n          return;\n        }\n        if (!textContent.trim()) {\n          setErrorMessage(\"Please enter some content\");\n          return;\n        }\n        onAdd(\"text\", {\n          title: textTitle.trim(),\n          content: textContent.trim(),\n          size: new Blob([textContent]).size\n        });\n      }\n\n      handleClose();\n    } catch (error) {\n      console.error(\"Submit error:\", error);\n      setErrorMessage(error instanceof Error ? error.message : \"An error occurred\");\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleClose = () => {\n    setSelectedFile(null);\n    setDocumentTitle(\"\");\n    setUrl(\"\");\n    setUrlTitle(\"\");\n    setTextTitle(\"\");\n    setTextContent(\"\");\n    setErrorMessage(\"\");\n    setIsUploading(false);\n    setActiveTab(\"document\");\n    onClose();\n  };\n\n  return (\n    <Dialog open={isOpen} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-lg\">\n        <DialogHeader>\n          <DialogTitle>Add Content to Knowledge Base</DialogTitle>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"document\" className=\"flex items-center space-x-2\">\n              <FileText className=\"h-4 w-4\" />\n              <span>Document</span>\n            </TabsTrigger>\n            <TabsTrigger value=\"url\" className=\"flex items-center space-x-2\">\n              <LinkIcon className=\"h-4 w-4\" />\n              <span>URL</span>\n            </TabsTrigger>\n            <TabsTrigger value=\"text\" className=\"flex items-center space-x-2\">\n              <Type className=\"h-4 w-4\" />\n              <span>Text</span>\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"document\" className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"doc-title\">Title *</Label>\n              <Input\n                id=\"doc-title\"\n                placeholder=\"Enter document title...\"\n                value={documentTitle}\n                onChange={(e) => setDocumentTitle(e.target.value)}\n              />\n            </div>\n\n            <div\n              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${\n                isDragging\n                  ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\"\n                  : \"border-gray-300 dark:border-gray-700\"\n              } ${\n                selectedFile ? \"bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-700\" : \"\"\n              }`}\n              onDragOver={handleDragOver}\n              onDragLeave={handleDragLeave}\n              onDrop={handleDrop}\n              onClick={() => fileInputRef.current?.click()}\n            >\n              <input\n                type=\"file\"\n                ref={fileInputRef}\n                className=\"hidden\"\n                accept={allowedTypes.join(\",\")}\n                onChange={handleFileChange}\n              />\n\n              {selectedFile ? (\n                <div className=\"space-y-2\">\n                  <FileText className=\"h-10 w-10 text-green-600 mx-auto\" />\n                  <p className=\"font-medium\">{selectedFile.name}</p>\n                  <p className=\"text-sm text-gray-500\">\n                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                  </p>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      setSelectedFile(null);\n                      setDocumentTitle(\"\");\n                    }}\n                  >\n                    <X className=\"h-4 w-4 mr-1\" />\n                    Remove\n                  </Button>\n                </div>\n              ) : (\n                <>\n                  <Upload className=\"h-10 w-10 text-gray-400 mx-auto mb-2\" />\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                    Drag and drop your file here\n                  </p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-500 mb-3\">or</p>\n                  <Button variant=\"outline\" size=\"sm\" type=\"button\">\n                    Browse Files\n                  </Button>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-500 mt-4\">\n                    Supported formats: {allowedTypes.join(', ')}\n                  </p>\n                </>\n              )}\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"url\" className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"url-title\">Title *</Label>\n              <Input\n                id=\"url-title\"\n                placeholder=\"Enter a title for this URL...\"\n                value={urlTitle}\n                onChange={(e) => setUrlTitle(e.target.value)}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"url-input\">URL *</Label>\n              <Input\n                id=\"url-input\"\n                placeholder=\"https://example.com\"\n                value={url}\n                onChange={(e) => setUrl(e.target.value)}\n              />\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"text\" className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"text-title\">Title *</Label>\n              <Input\n                id=\"text-title\"\n                placeholder=\"Enter a title for this text...\"\n                value={textTitle}\n                onChange={(e) => setTextTitle(e.target.value)}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"text-content\">Content *</Label>\n              <Textarea\n                id=\"text-content\"\n                placeholder=\"Enter your text content here...\"\n                value={textContent}\n                onChange={(e) => setTextContent(e.target.value)}\n                rows={6}\n              />\n            </div>\n          </TabsContent>\n        </Tabs>\n\n        {errorMessage && (\n          <p className=\"text-sm text-red-500\">{errorMessage}</p>\n        )}\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={isUploading}>\n            Cancel\n          </Button>\n          <Button onClick={handleSubmit} disabled={isUploading}>\n            {isUploading ? (\n              <>\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                {activeTab === \"document\" ? \"Uploading...\" : \"Adding...\"}\n              </>\n            ) : (\n              \"Add Content\"\n            )}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;;;AAxBA;;;;;;;;;;;AAkCO,SAAS,gBAAgB,EAC9B,MAAM,EACN,OAAO,EACP,KAAK,EACL,cAAc,EACd,eAAe,EACM;;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,YAAY;IACZ,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QAAC;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAS;QAAQ;KAAO;IAEvF,MAAM,qBAAqB,CAAC;QAC1B,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;QAExD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;YACzC,gBAAgB,CAAC,wCAAwC,EAAE,aAAa,IAAI,CAAC,OAAO;YACpF;QACF;QAEA,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,gBAAgB;YAChB;QACF;QAEA,gBAAgB;QAChB,iBAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,MAAM,mBAAmB;QACzE,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QACtC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,mBAAmB,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAChB,eAAe;QAEf,IAAI;YACF,IAAI,cAAc,YAAY;gBAC5B,IAAI,CAAC,cAAc;oBACjB,gBAAgB;oBAChB;gBACF;gBACA,IAAI,CAAC,cAAc,IAAI,IAAI;oBACzB,gBAAgB;oBAChB;gBACF;gBAEA,IAAI,SAAS;gBAEb,qDAAqD;gBACrD,IAAI,gBAAgB;oBAClB,IAAI;wBACF,MAAM,eAAe,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EACtC,gBACA,cACA;wBAGF,IAAI,aAAa,OAAO,EAAE;4BACxB,SAAS,aAAa,IAAI;4BAC1B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,IAAI,CAAC,oDAAoD;wBACjE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;gBACF;gBAEA,MAAM,YAAY;oBAChB,OAAO,cAAc,IAAI;oBACzB,UAAU,aAAa,IAAI;oBAC3B,MAAM,aAAa,IAAI;oBACvB,MAAM;oBACN,QAAQ,OAAO,2CAA2C;gBAC5D;YACF,OAAO,IAAI,cAAc,OAAO;gBAC9B,IAAI,CAAC,IAAI,IAAI,IAAI;oBACf,gBAAgB;oBAChB;gBACF;gBACA,IAAI,CAAC,SAAS,IAAI,IAAI;oBACpB,gBAAgB;oBAChB;gBACF;gBACA,uBAAuB;gBACvB,IAAI;oBACF,IAAI,IAAI;gBACV,EAAE,OAAM;oBACN,gBAAgB;oBAChB;gBACF;gBACA,MAAM,OAAO;oBACX,OAAO,SAAS,IAAI;oBACpB,KAAK,IAAI,IAAI;gBACf;YACF,OAAO,IAAI,cAAc,QAAQ;gBAC/B,IAAI,CAAC,UAAU,IAAI,IAAI;oBACrB,gBAAgB;oBAChB;gBACF;gBACA,IAAI,CAAC,YAAY,IAAI,IAAI;oBACvB,gBAAgB;oBAChB;gBACF;gBACA,MAAM,QAAQ;oBACZ,OAAO,UAAU,IAAI;oBACrB,SAAS,YAAY,IAAI;oBACzB,MAAM,IAAI,KAAK;wBAAC;qBAAY,EAAE,IAAI;gBACpC;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3D,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,cAAc;QAClB,gBAAgB;QAChB,iBAAiB;QACjB,OAAO;QACP,YAAY;QACZ,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAGf,6LAAC,mIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;;sCACrC,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAM,WAAU;;sDACjC,6LAAC,qMAAA,CAAA,OAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;;sDAClC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAIV,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAIpD,6LAAC;oCACC,WAAW,CAAC,mFAAmF,EAC7F,aACI,mDACA,uCACL,CAAC,EACA,eAAe,4EAA4E,IAC3F;oCACF,YAAY;oCACZ,aAAa;oCACb,QAAQ;oCACR,SAAS,IAAM,aAAa,OAAO,EAAE;;sDAErC,6LAAC;4CACC,MAAK;4CACL,KAAK;4CACL,WAAU;4CACV,QAAQ,aAAa,IAAI,CAAC;4CAC1B,UAAU;;;;;;wCAGX,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAE,WAAU;8DAAe,aAAa,IAAI;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;;wDACV,CAAC,aAAa,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wDAAG;;;;;;;8DAEhD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,gBAAgB;wDAChB,iBAAiB;oDACnB;;sEAEA,6LAAC,+LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;iEAKlC;;8DACE,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAE,WAAU;8DAAgD;;;;;;8DAG7D,6LAAC;oDAAE,WAAU;8DAAgD;;;;;;8DAC7D,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,MAAK;8DAAS;;;;;;8DAGlD,6LAAC;oDAAE,WAAU;;wDAAgD;wDACvC,aAAa,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;sCAOhD,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;;8CACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAI/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAO,WAAU;;8CAClC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAIhD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,MAAM;;;;;;;;;;;;;;;;;;;;;;;;gBAMb,8BACC,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;8BAGvC,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAa;;;;;;sCAGvE,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAc,UAAU;sCACtC,4BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,cAAc,aAAa,iBAAiB;;+CAG/C;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA7UgB;KAAA", "debugId": null}}, {"offset": {"line": 2379, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/config.ts"], "sourcesContent": ["// API Configuration\r\nexport const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';\r\n// Stripe Configuration\r\nexport const STRIPE_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '';\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;AACG;AAAhB,MAAM,UAAU,6DAAsC;AAEtD,MAAM,yBAAyB,mJAAkD", "debugId": null}}, {"offset": {"line": 2396, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/organizations.ts"], "sourcesContent": ["import { API_URL } from \"./config\";\r\n\r\n// Types\r\nexport interface Organization {\r\n  _id: string;\r\n  name: string;\r\n  description?: string;\r\n  status: 'active' | 'inactive' | 'suspended';\r\n  credits: number; // Paid credits\r\n  monthlyFreeCredits: number; // Monthly allowance in minutes\r\n  monthlyFreeCreditsUsed: number; // Used free credits this month\r\n  lastMonthlyReset: string; // Last time monthly credits were reset\r\n  usingFreeCredits: boolean; // Whether currently using free credits\r\n  autoRechargeEnabled: boolean;\r\n  autoRechargeThreshold: number;\r\n  autoRechargeAmount: number;\r\n  // Billing configuration settings (moved from global settings)\r\n  callPricePerMinute: number;\r\n  minimumCreditsThreshold: number;\r\n  monthlyMinutesAllowance: number;\r\n  stripeCustomerId?: string;\r\n  // Email notification settings\r\n  fullName?: string; // Client's full name for email personalization\r\n  email?: string; // Email address to send credit notifications to\r\n  lastWarningEmailSent?: string; // Last time warning email was sent\r\n  lastRunoutEmailSent?: string; // Last time runout email was sent\r\n  // S3 Configuration for knowledge base file storage\r\n  s3Config?: {\r\n    accessKeyId?: string;\r\n    secretAccessKey?: string;\r\n    region?: string;\r\n    bucketName?: string;\r\n    enabled?: boolean;\r\n  };\r\n  adminUsers: string[];\r\n  users: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface CreateOrganizationRequest {\r\n  name: string;\r\n  description?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  adminUsers?: string[];\r\n}\r\n\r\nexport interface UpdateOrganizationRequest {\r\n  name?: string;\r\n  description?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  adminUsers?: string[];\r\n  users?: string[];\r\n}\r\n\r\nexport interface UpdateOrganizationBillingRequest {\r\n  credits?: number;\r\n  autoRechargeEnabled?: boolean;\r\n  autoRechargeThreshold?: number;\r\n  autoRechargeAmount?: number;\r\n  callPricePerMinute?: number;\r\n  minimumCreditsThreshold?: number;\r\n  monthlyMinutesAllowance?: number;\r\n  monthlyResetDate?: number;\r\n}\r\n\r\nexport interface UpdateOrganizationSettingsRequest {\r\n  monthlyResetDate?: number;\r\n  fullName?: string;\r\n  email?: string;\r\n  s3Config?: {\r\n    accessKeyId?: string;\r\n    secretAccessKey?: string;\r\n    region?: string;\r\n    bucketName?: string;\r\n    enabled?: boolean;\r\n  };\r\n}\r\n\r\n// API Functions\r\nexport const getOrganizations = async (): Promise<Organization[]> => {\r\n  const response = await fetch(`${API_URL}/api/organizations`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch organizations');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const getMyOrganizations = async (): Promise<Organization[]> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/my-organizations`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch your organizations');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const getOrganization = async (id: string): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const createOrganization = async (data: CreateOrganizationRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to create organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganization = async (id: string, data: UpdateOrganizationRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganizationBilling = async (id: string, data: UpdateOrganizationBillingRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}/billing`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization billing');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganizationSettings = async (id: string, data: UpdateOrganizationSettingsRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}/settings`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization settings');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const deleteOrganization = async (id: string): Promise<{ message: string }> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'DELETE',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to delete organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const addUserToOrganization = async (organizationId: string, userId: string, isAdmin: boolean): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${organizationId}/users/${userId}`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify({ isAdmin }),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to add user to organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const removeUserFromOrganization = async (organizationId: string, userId: string): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${organizationId}/users/${userId}`, {\r\n    method: 'DELETE',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to remove user from organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAgFO,MAAM,mBAAmB;IAC9B,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,EAAE;QAC3D,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mCAAmC,CAAC,EAAE;QAC5E,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,EAAE;QAC3D,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO,IAAY;IACnD,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,4BAA4B,OAAO,IAAY;IAC1D,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,GAAG,QAAQ,CAAC,EAAE;QACzE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,6BAA6B,OAAO,IAAY;IAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,GAAG,SAAS,CAAC,EAAE;QAC1E,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,wBAAwB,OAAO,gBAAwB,QAAgB;IAClF,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,eAAe,OAAO,EAAE,QAAQ,EAAE;QAC7F,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAQ;IACjC;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,6BAA6B,OAAO,gBAAwB;IACvE,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,eAAe,OAAO,EAAE,QAAQ,EAAE;QAC7F,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 2566, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28workspace%29/brain/BrainContent.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Plus } from \"lucide-react\";\r\nimport { KnowledgeBaseFolderSidebar } from \"@/components/braincomponents/KnowledgeBaseFolderSidebar\";\r\nimport { KnowledgeBaseContent } from \"@/components/braincomponents/KnowledgeBaseContent\";\r\nimport { CreateFolderModal } from \"@/components/braincomponents/CreateFolderModal\";\r\nimport { AddContentModal } from \"@/components/braincomponents/AddContentModal\";\r\nimport { getMyOrganizations, Organization } from \"@/app/api/organizations\";\r\nimport { deleteFileFromS3 } from \"@/app/api/s3\";\r\n\r\n// Types\r\ntype KnowledgeBaseFolder = {\r\n  id: string;\r\n  name: string;\r\n  description?: string;\r\n  createdAt: Date;\r\n  itemCount: number;\r\n};\r\n\r\ntype KnowledgeBaseItem = {\r\n  id: string;\r\n  title: string;\r\n  type: \"document\" | \"url\" | \"text\";\r\n  content?: string; // For text items\r\n  url?: string; // For URL items\r\n  filename?: string; // For document items\r\n  size?: number; // For document items\r\n  pages?: number; // For document items\r\n  status: \"processing\" | \"ready\" | \"error\";\r\n  uploadedAt: Date;\r\n  lastSynced?: Date;\r\n  folderId: string;\r\n};\r\n\r\nexport default function BrainContent() {\r\n  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(\"1\");\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);\r\n\r\n  // Modal states\r\n  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);\r\n  const [isAddContentModalOpen, setIsAddContentModalOpen] = useState(false);\r\n\r\n  // Data states\r\n  const [folders, setFolders] = useState<KnowledgeBaseFolder[]>([\r\n    {\r\n      id: \"1\",\r\n      name: \"TESET\",\r\n      description: \"Test knowledge base\",\r\n      createdAt: new Date(\"2025-03-06T14:00:00\"),\r\n      itemCount: 3\r\n    },\r\n    {\r\n      id: \"2\",\r\n      name: \"orova website\",\r\n      description: \"Company website content\",\r\n      createdAt: new Date(\"2025-03-05T10:00:00\"),\r\n      itemCount: 1\r\n    }\r\n  ]);\r\n\r\n  const [items, setItems] = useState<KnowledgeBaseItem[]>([\r\n    {\r\n      id: \"1\",\r\n      title: \"osweb.solutions\",\r\n      type: \"url\",\r\n      url: \"https://osweb.solutions\",\r\n      status: \"ready\",\r\n      uploadedAt: new Date(\"2025-03-06T14:01:00\"),\r\n      lastSynced: new Date(\"2025-03-06T14:01:00\"),\r\n      pages: 31,\r\n      folderId: \"1\"\r\n    },\r\n    {\r\n      id: \"2\",\r\n      title: \"test text\",\r\n      type: \"text\",\r\n      content: \"This is a test text content...\",\r\n      status: \"ready\",\r\n      uploadedAt: new Date(\"2025-03-06T14:02:00\"),\r\n      size: 1024,\r\n      folderId: \"1\"\r\n    },\r\n    {\r\n      id: \"3\",\r\n      title: \"_8_Ai_tools_to_finish_your_work_in_1_hoursâ_1689356736.pdf\",\r\n      type: \"document\",\r\n      filename: \"_8_Ai_tools_to_finish_your_work_in_1_hoursâ_1689356736.pdf\",\r\n      status: \"ready\",\r\n      uploadedAt: new Date(\"2025-03-06T14:03:00\"),\r\n      size: 1155 * 1024,\r\n      pages: 8,\r\n      folderId: \"1\"\r\n    }\r\n  ]);\r\n\r\n  // Load organization data\r\n  useEffect(() => {\r\n    const loadOrganization = async () => {\r\n      try {\r\n        const organizations = await getMyOrganizations();\r\n        if (organizations.length > 0) {\r\n          setCurrentOrganization(organizations[0]); // Use first organization\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to load organization:\", error);\r\n      }\r\n    };\r\n\r\n    loadOrganization();\r\n  }, []);\r\n\r\n  // Handlers\r\n  const handleCreateFolder = (name: string, description?: string) => {\r\n    const newFolder: KnowledgeBaseFolder = {\r\n      id: Math.random().toString(36).substr(2, 9),\r\n      name,\r\n      description,\r\n      createdAt: new Date(),\r\n      itemCount: 0\r\n    };\r\n    setFolders([...folders, newFolder]);\r\n  };\r\n\r\n  const handleSelectFolder = (folderId: string) => {\r\n    setSelectedFolderId(folderId);\r\n  };\r\n\r\n  const handleAddContent = (type: \"document\" | \"url\" | \"text\", data: any) => {\r\n    if (!selectedFolderId) return;\r\n\r\n    const newItem: KnowledgeBaseItem = {\r\n      id: Math.random().toString(36).substr(2, 9),\r\n      title: data.title,\r\n      type,\r\n      status: data.s3Data ? \"ready\" : \"processing\", // Set to ready if S3 upload was successful\r\n      uploadedAt: new Date(),\r\n      folderId: selectedFolderId,\r\n      ...data\r\n    };\r\n\r\n    // Add S3 key if available for future deletion\r\n    if (data.s3Data) {\r\n      (newItem as any).s3Key = data.s3Data.key;\r\n      (newItem as any).s3Url = data.s3Data.url;\r\n    }\r\n\r\n    setItems([...items, newItem]);\r\n\r\n    // Update folder item count\r\n    setFolders(folders.map(folder =>\r\n      folder.id === selectedFolderId\r\n        ? { ...folder, itemCount: folder.itemCount + 1 }\r\n        : folder\r\n    ));\r\n  };\r\n\r\n  const handleDeleteItem = async (itemId: string) => {\r\n    const item = items.find(i => i.id === itemId);\r\n    if (!item) return;\r\n\r\n    // Try to delete from S3 if it's a document with S3 data\r\n    if (item.type === \"document\" && (item as any).s3Key && currentOrganization) {\r\n      try {\r\n        await deleteFileFromS3(currentOrganization._id, (item as any).s3Key);\r\n        console.log(\"File deleted from S3 successfully\");\r\n      } catch (error) {\r\n        console.error(\"Failed to delete file from S3:\", error);\r\n        // Continue with local deletion even if S3 deletion fails\r\n      }\r\n    }\r\n\r\n    setItems(items.filter(i => i.id !== itemId));\r\n\r\n    // Update folder item count\r\n    setFolders(folders.map(folder =>\r\n      folder.id === item.folderId\r\n        ? { ...folder, itemCount: folder.itemCount - 1 }\r\n        : folder\r\n    ));\r\n  };\r\n\r\n  const handleDeleteFolder = (folderId: string) => {\r\n    // Delete all items in the folder first\r\n    setItems(items.filter(item => item.folderId !== folderId));\r\n    // Delete the folder\r\n    setFolders(folders.filter(folder => folder.id !== folderId));\r\n    // Reset selection if deleted folder was selected\r\n    if (selectedFolderId === folderId) {\r\n      setSelectedFolderId(folders.length > 1 ? folders[0].id : null);\r\n    }\r\n  };\r\n\r\n  // Get items for selected folder\r\n  const selectedFolderItems = selectedFolderId\r\n    ? items.filter(item => item.folderId === selectedFolderId)\r\n    : [];\r\n\r\n  const selectedFolder = selectedFolderId\r\n    ? folders.find(f => f.id === selectedFolderId)\r\n    : null;\r\n\r\n  return (\r\n    <div className=\"flex h-[calc(100vh-4rem)]\">\r\n      {/* Sidebar */}\r\n      <div className=\"w-80 border-r bg-white dark:bg-gray-800 dark:border-gray-700\">\r\n        <KnowledgeBaseFolderSidebar\r\n          folders={folders}\r\n          selectedFolderId={selectedFolderId}\r\n          onSelectFolder={handleSelectFolder}\r\n          onCreateFolder={() => setIsCreateFolderModalOpen(true)}\r\n          onDeleteFolder={handleDeleteFolder}\r\n        />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 overflow-auto\">\r\n        <KnowledgeBaseContent\r\n          folder={selectedFolder}\r\n          items={selectedFolderItems}\r\n          searchQuery={searchQuery}\r\n          onSearchChange={setSearchQuery}\r\n          onAddContent={() => setIsAddContentModalOpen(true)}\r\n          onDeleteItem={handleDeleteItem}\r\n        />\r\n      </div>\r\n\r\n      {/* Modals */}\r\n      <CreateFolderModal\r\n        isOpen={isCreateFolderModalOpen}\r\n        onClose={() => setIsCreateFolderModalOpen(false)}\r\n        onCreate={handleCreateFolder}\r\n      />\r\n\r\n      <AddContentModal\r\n        isOpen={isAddContentModalOpen}\r\n        onClose={() => setIsAddContentModalOpen(false)}\r\n        onAdd={handleAddContent}\r\n        organizationId={currentOrganization?._id}\r\n        knowledgeBaseId={selectedFolderId || undefined}\r\n      />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAIA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;AAqCe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEpF,eAAe;IACf,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,cAAc;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;QAC5D;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW,IAAI,KAAK;YACpB,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW,IAAI,KAAK;YACpB,WAAW;QACb;KACD;IAED,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,KAAK;YACL,QAAQ;YACR,YAAY,IAAI,KAAK;YACrB,YAAY,IAAI,KAAK;YACrB,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,SAAS;YACT,QAAQ;YACR,YAAY,IAAI,KAAK;YACrB,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;YACV,QAAQ;YACR,YAAY,IAAI,KAAK;YACrB,MAAM,OAAO;YACb,OAAO;YACP,UAAU;QACZ;KACD;IAED,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;2DAAmB;oBACvB,IAAI;wBACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;wBAC7C,IAAI,cAAc,MAAM,GAAG,GAAG;4BAC5B,uBAAuB,aAAa,CAAC,EAAE,GAAG,yBAAyB;wBACrE;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,WAAW;IACX,MAAM,qBAAqB,CAAC,MAAc;QACxC,MAAM,YAAiC;YACrC,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACzC;YACA;YACA,WAAW,IAAI;YACf,WAAW;QACb;QACA,WAAW;eAAI;YAAS;SAAU;IACpC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;IACtB;IAEA,MAAM,mBAAmB,CAAC,MAAmC;QAC3D,IAAI,CAAC,kBAAkB;QAEvB,MAAM,UAA6B;YACjC,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACzC,OAAO,KAAK,KAAK;YACjB;YACA,QAAQ,KAAK,MAAM,GAAG,UAAU;YAChC,YAAY,IAAI;YAChB,UAAU;YACV,GAAG,IAAI;QACT;QAEA,8CAA8C;QAC9C,IAAI,KAAK,MAAM,EAAE;YACd,QAAgB,KAAK,GAAG,KAAK,MAAM,CAAC,GAAG;YACvC,QAAgB,KAAK,GAAG,KAAK,MAAM,CAAC,GAAG;QAC1C;QAEA,SAAS;eAAI;YAAO;SAAQ;QAE5B,2BAA2B;QAC3B,WAAW,QAAQ,GAAG,CAAC,CAAA,SACrB,OAAO,EAAE,KAAK,mBACV;gBAAE,GAAG,MAAM;gBAAE,WAAW,OAAO,SAAS,GAAG;YAAE,IAC7C;IAER;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtC,IAAI,CAAC,MAAM;QAEX,wDAAwD;QACxD,IAAI,KAAK,IAAI,KAAK,cAAc,AAAC,KAAa,KAAK,IAAI,qBAAqB;YAC1E,IAAI;gBACF,MAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,oBAAoB,GAAG,EAAE,AAAC,KAAa,KAAK;gBACnE,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,yDAAyD;YAC3D;QACF;QAEA,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEpC,2BAA2B;QAC3B,WAAW,QAAQ,GAAG,CAAC,CAAA,SACrB,OAAO,EAAE,KAAK,KAAK,QAAQ,GACvB;gBAAE,GAAG,MAAM;gBAAE,WAAW,OAAO,SAAS,GAAG;YAAE,IAC7C;IAER;IAEA,MAAM,qBAAqB,CAAC;QAC1B,uCAAuC;QACvC,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QAChD,oBAAoB;QACpB,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QAClD,iDAAiD;QACjD,IAAI,qBAAqB,UAAU;YACjC,oBAAoB,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG;QAC3D;IACF;IAEA,gCAAgC;IAChC,MAAM,sBAAsB,mBACxB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,oBACvC,EAAE;IAEN,MAAM,iBAAiB,mBACnB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,oBAC3B;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sKAAA,CAAA,6BAA0B;oBACzB,SAAS;oBACT,kBAAkB;oBAClB,gBAAgB;oBAChB,gBAAgB,IAAM,2BAA2B;oBACjD,gBAAgB;;;;;;;;;;;0BAKpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gKAAA,CAAA,uBAAoB;oBACnB,QAAQ;oBACR,OAAO;oBACP,aAAa;oBACb,gBAAgB;oBAChB,cAAc,IAAM,yBAAyB;oBAC7C,cAAc;;;;;;;;;;;0BAKlB,6LAAC,6JAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,2BAA2B;gBAC1C,UAAU;;;;;;0BAGZ,6LAAC,2JAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,yBAAyB;gBACxC,OAAO;gBACP,gBAAgB,qBAAqB;gBACrC,iBAAiB,oBAAoB;;;;;;;;;;;;AAI7C;GAjNwB;KAAA", "debugId": null}}]}