"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const organizations_controller_1 = require("./organizations.controller");
const organizations_service_1 = require("./organizations.service");
const organization_schema_1 = require("./schemas/organization.schema");
const users_module_1 = require("../users/users.module");
const user_schema_1 = require("../users/schemas/user.schema");
const credit_module_1 = require("../credit/credit.module");
const monthly_credits_service_1 = require("./monthly-credits.service");
const global_settings_module_1 = require("../global-settings/global-settings.module");
const notifications_module_1 = require("../notifications/notifications.module");
const s3_module_1 = require("../s3/s3.module");
let OrganizationsModule = class OrganizationsModule {
};
exports.OrganizationsModule = OrganizationsModule;
exports.OrganizationsModule = OrganizationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: 'Organization', schema: organization_schema_1.OrganizationSchema },
                { name: 'User', schema: user_schema_1.UserSchema },
            ]),
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            (0, common_1.forwardRef)(() => credit_module_1.CreditModule),
            global_settings_module_1.GlobalSettingsModule,
            notifications_module_1.NotificationsModule,
            s3_module_1.S3Module,
        ],
        controllers: [organizations_controller_1.OrganizationsController],
        providers: [organizations_service_1.OrganizationsService, monthly_credits_service_1.MonthlyCreditsService],
        exports: [organizations_service_1.OrganizationsService, monthly_credits_service_1.MonthlyCreditsService],
    })
], OrganizationsModule);
//# sourceMappingURL=organizations.module.js.map