import {
  Controller,
  Get,
  Post,
  Body,
  HttpStatus,
  Res,
  UseGuards,
  Param,
  Patch,
  Delete,
  Query,
} from "@nestjs/common";
import { Response } from "express";
import { ScheduledCallService } from "./scheduled-call.service";
import { JwtAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { RolesGuard } from "src/auth/guards/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { User } from "src/auth/decorators/user.decorator";
import { FullUserType } from "src/auth/interceptors/user-redaction.interceptor";
import { UsersService } from "src/users/users.service";

@Controller("scheduled-call")
export class ScheduledCallController {
  constructor(
    private readonly scheduledCallService: ScheduledCallService,
    private readonly usersService: UsersService
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  async createScheduledCall(
    @Body()
    payload: {
      agentId: string;
      contacts: any[];
      scheduledTime: string;
      region: string;
      scheduledByName: string;
    },
    @User() user: FullUserType,
    @Res() res: Response
  ) {
    try {
      // Check if user has sufficient credits
      const hasSufficientCredits = await this.usersService.hasSufficientCredits(user.userId, 1);

      if (!hasSufficientCredits) {
        return res.status(HttpStatus.PAYMENT_REQUIRED).json({
          error: "Insufficient credits. Please add funds to your account.",
        });
      }

      if (payload.scheduledByName !== "agent") {
        payload.scheduledByName = user.fullName || "User";
      }

      const scheduledCall = await this.scheduledCallService.createScheduledCall(
        payload
      );

      // Deduct credits for scheduling a call (1 credit per scheduled call)
      await this.usersService.deductCredits(user.userId, 1);

      return res.status(HttpStatus.OK).json({
        message: "Scheduled call created successfully",
        scheduledCall,
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  async getScheduledCalls(
    @Res() res: Response,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('filter') filter?: 'all' | 'today' | 'thisWeek' | 'weekend' | 'nextWeek' | 'upcoming' | 'past',
  ){
    try {
      // If limit is provided, use it; otherwise pass undefined to get all records
    const pageNum = page ? +page : undefined;
    const limitNum = limit ? +limit : undefined;

    const scheduledCalls = await this.scheduledCallService.getScheduledCalls(pageNum, limitNum, search, filter);
    return res.status(HttpStatus.OK).json(scheduledCalls);
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Get(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  async getScheduledCallById(@Param("id") id: string, @Res() res: Response) {
    try {
      const scheduledCall =
        await this.scheduledCallService.getScheduledCallById(id);
      if (!scheduledCall) {
        return res.status(HttpStatus.NOT_FOUND).json({
          error: "Scheduled call not found",
        });
      }
      return res.status(HttpStatus.OK).json(scheduledCall);
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Patch(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  async updateScheduledCall(
    @Param("id") id: string,
    @Body()
    updateData: {
      agentId?: string;
      contacts?: { Name: string; MobileNumber: string }[];
      scheduledTime?: Date;
      region?: string;
      status?: string;
    },
    @Res() res: Response
  ) {
    try {
      const updatedCall = await this.scheduledCallService.updateScheduledCall(
        id,
        updateData
      );
      if (!updatedCall) {
        return res.status(HttpStatus.NOT_FOUND).json({
          error: "Scheduled call not found",
        });
      }
      return res.status(HttpStatus.OK).json({
        message: "Scheduled call updated successfully",
        scheduledCall: updatedCall,
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  async deleteScheduledCall(@Param("id") id: string, @Res() res: Response) {
    try {
      const result = await this.scheduledCallService.deleteScheduledCall(id);
      if (!result) {
        return res.status(HttpStatus.NOT_FOUND).json({
          error: "Scheduled call not found",
        });
      }
      return res.status(HttpStatus.OK).json({
        message: "Scheduled call deleted successfully",
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Post("remove-duplicates")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'superadmin')
  async removeDuplicateCalls(
    @Body()
    payload: {
      agentId: string;
    },
    @Res() res: Response
  ) {
    try {
      const { agentId } = payload;

      const result = await this.scheduledCallService.removeDuplicateCalls(agentId);

      return res.status(HttpStatus.OK).json({
        message: `Successfully removed ${result.duplicatesRemoved} duplicate calls`,
        ...result
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Post("reschedule-campaign")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  async rescheduleCampaignCalls(
    @Body()
    payload: {
      agentId: string;
      concurrentCalls: number;
      batchIntervalMinutes: number;
      callWindow: {
        startTime: string;
        endTime: string;
        daysOfWeek: string[];
      };
    },
    @Res() res: Response
  ) {
    try {
      const {
        agentId,
        concurrentCalls,
        batchIntervalMinutes,
        callWindow
      } = payload;

      const result = await this.scheduledCallService.rescheduleCampaignCalls(
        agentId,
        concurrentCalls,
        batchIntervalMinutes,
        callWindow
      );

      return res.status(HttpStatus.OK).json({
        message: `Successfully processed ${result.totalCount} calls (${result.rescheduledCount} rescheduled, ${result.duplicatesRemoved} duplicates removed)`,
        ...result
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Delete("bulk/cancelled")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  async deleteAllCancelledCalls(@Res() res: Response) {
    try {
      const result = await this.scheduledCallService.deleteCallsByStatus('cancelled');
      return res.status(HttpStatus.OK).json({
        message: `Successfully deleted ${result.deletedCount} cancelled scheduled calls`,
        deletedCount: result.deletedCount
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Delete("bulk/failed")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  async deleteAllFailedCalls(@Res() res: Response) {
    try {
      const result = await this.scheduledCallService.deleteCallsByStatus('failed');
      return res.status(HttpStatus.OK).json({
        message: `Successfully deleted ${result.deletedCount} failed scheduled calls`,
        deletedCount: result.deletedCount
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }
}
