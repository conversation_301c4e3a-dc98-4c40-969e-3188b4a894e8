{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/APIKeyInput.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { CheckCircle, Loader2 } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\n\r\ninterface APIKeyInputProps {\r\n  label: string;\r\n  description?: string;\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  onTest?: () => Promise<boolean>;\r\n  placeholder?: string;\r\n}\r\n\r\nexport function APIKeyInput({\r\n  label,\r\n  description,\r\n  value,\r\n  onChange,\r\n  onTest,\r\n  placeholder = \"Enter API key\"\r\n}: APIKeyInputProps) {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [isTesting, setIsTesting] = useState(false);\r\n  const [testResult, setTestResult] = useState<boolean | null>(null);\r\n\r\n  const handleTest = async () => {\r\n    if (!onTest || !value.trim()) return;\r\n    \r\n    setIsTesting(true);\r\n    setTestResult(null);\r\n    \r\n    try {\r\n      const result = await onTest();\r\n      setTestResult(result);\r\n    } catch (error) {\r\n      setTestResult(false);\r\n      console.error(\"API test failed:\", error);\r\n    } finally {\r\n      setIsTesting(false);\r\n    }\r\n    \r\n    // Reset test result after 3 seconds\r\n    setTimeout(() => setTestResult(null), 3000);\r\n  };\r\n\r\n  return (\r\n    <>  \r\n    <div className=\"space-y-2\">\r\n      <div className=\"flex items-baseline justify-between\">\r\n        <Label htmlFor={label.replace(/\\s+/g, '-').toLowerCase()}>{label}</Label>\r\n        {description && (\r\n          <span className=\"text-xs text-muted-foreground\">{description}</span>\r\n        )}\r\n      </div>\r\n      <div className=\"flex gap-2\">\r\n        <div className=\"relative flex-1\">\r\n          <Input\r\n            id={label.replace(/\\s+/g, '-').toLowerCase()}\r\n            type={isVisible ? \"text\" : \"password\"}\r\n            value={value}\r\n            onChange={(e) => onChange(e.target.value)}\r\n            placeholder={placeholder}\r\n            className=\"pr-20\"\r\n          />\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className=\"absolute right-0 top-0 h-full px-3 py-0 text-xs font-normal\"\r\n            onClick={() => setIsVisible(!isVisible)}\r\n          >\r\n            {isVisible ? \"Hide\" : \"Show\"}\r\n          </Button>\r\n        </div>\r\n        {onTest && (\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleTest}\r\n            disabled={isTesting || !value.trim()}\r\n            className=\"flex items-center gap-1 whitespace-nowrap\"\r\n          >\r\n            {isTesting ? (\r\n              <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n            ) : testResult === true ? (\r\n              <CheckCircle className=\"h-3 w-3 text-green-500\" />\r\n            ) : testResult === false ? (\r\n              <span className=\"text-red-500\">Failed</span>\r\n            ) : (\r\n              \"Test\"\r\n            )}\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAiBO,SAAS,YAAY,EAC1B,KAAK,EACL,WAAW,EACX,KAAK,EACL,QAAQ,EACR,MAAM,EACN,cAAc,eAAe,EACZ;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE7D,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI;QAE9B,aAAa;QACb,cAAc;QAEd,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,cAAc;YACd,QAAQ,KAAK,CAAC,oBAAoB;QACpC,SAAU;YACR,aAAa;QACf;QAEA,oCAAoC;QACpC,WAAW,IAAM,cAAc,OAAO;IACxC;IAEA,qBACE;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAS,MAAM,OAAO,CAAC,QAAQ,KAAK,WAAW;sCAAK;;;;;;wBAC1D,6BACC,6LAAC;4BAAK,WAAU;sCAAiC;;;;;;;;;;;;8BAGrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAI,MAAM,OAAO,CAAC,QAAQ,KAAK,WAAW;oCAC1C,MAAM,YAAY,SAAS;oCAC3B,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAa;oCACb,WAAU;;;;;;8CAEZ,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,aAAa,CAAC;8CAE5B,YAAY,SAAS;;;;;;;;;;;;wBAGzB,wBACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC,MAAM,IAAI;4BAClC,WAAU;sCAET,0BACC,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;uCACjB,eAAe,qBACjB,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;uCACrB,eAAe,sBACjB,6LAAC;gCAAK,WAAU;0CAAe;;;;;uCAE/B;;;;;;;;;;;;;;;;;;;AAQd;GArFgB;KAAA", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/settingscomponents/APISettings.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useState } from \"react\";\r\nimport { APIKeyInput } from \"../APIKeyInput\";\r\n\r\nexport function APISettings() {\r\n  const [openai<PERSON>ey, setOpenaiKey] = useState(\"\");\r\n  const [claude<PERSON><PERSON>, set<PERSON>laude<PERSON><PERSON>] = useState(\"\");\r\n  const [gemini<PERSON>ey, setGeminiKey] = useState(\"\");\r\n  const [deepseek<PERSON>ey, setDeepseekKey] = useState(\"\");\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  const testOpenAI = async () => {\r\n    // Implement actual API test\r\n    await new Promise((resolve) => setTimeout(resolve, 1000));\r\n    return true;\r\n  };\r\n\r\n  const testClaude = async () => {\r\n    // Implement actual API test\r\n    await new Promise((resolve) => setTimeout(resolve, 1000));\r\n    return true;\r\n  };\r\n\r\n  const testGemini = async () => {\r\n    // Implement actual API test\r\n    await new Promise((resolve) => setTimeout(resolve, 1000));\r\n    return true;\r\n  };\r\n\r\n  const testDeepseek = async () => {\r\n    // Implement actual API test\r\n    await new Promise((resolve) => setTimeout(resolve, 1000));\r\n    return false; // Simulate a failure for demonstration\r\n  };\r\n\r\n  const handleSaveChanges = async () => {\r\n    setIsSaving(true);\r\n    // Implement API key saving\r\n    await new Promise(resolve => setTimeout(resolve, 1000));\r\n    setIsSaving(false);\r\n    // You could show a success toast here\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"space-y-4\">\r\n        <h3 className=\"text-lg font-medium\">Language Model APIs</h3>\r\n        <div className=\"grid gap-6\">\r\n          <APIKeyInput\r\n            label=\"OpenAI API Key\"\r\n            description=\"Required for GPT-3.5, GPT-4, and DALL-E models\"\r\n            value={openaiKey}\r\n            onChange={setOpenaiKey}\r\n            onTest={testOpenAI}\r\n            placeholder=\"sk-...\"\r\n          />\r\n\r\n          <APIKeyInput\r\n            label=\"Anthropic API Key\"\r\n            description=\"Required for Claude models\"\r\n            value={claudeKey}\r\n            onChange={setClaudeKey}\r\n            onTest={testClaude}\r\n            placeholder=\"sk-ant-...\"\r\n          />\r\n\r\n          <APIKeyInput\r\n            label=\"Google API Key\"\r\n            description=\"Required for Gemini models\"\r\n            value={geminiKey}\r\n            onChange={setGeminiKey}\r\n            onTest={testGemini}\r\n          />\r\n\r\n          <APIKeyInput\r\n            label=\"DeepSeek API Key\"\r\n            description=\"Required for DeepSeek models\"\r\n            value={deepseekKey}\r\n            onChange={setDeepseekKey}\r\n            onTest={testDeepseek}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-end\">\r\n        <Button \r\n          onClick={handleSaveChanges}\r\n          disabled={isSaving}\r\n          className=\"bg-primary text-primary-foreground hover:bg-primary/90\"\r\n        >\r\n          {isSaving ? \"Saving...\" : \"Save Changes\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QACnD,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QACnD,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QACnD,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QACnD,OAAO,OAAO,uCAAuC;IACvD;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,YAAY;IACZ,sCAAsC;IACxC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsB;;;;;;kCACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,QAAQ;gCACR,aAAY;;;;;;0CAGd,6LAAC,oIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,QAAQ;gCACR,aAAY;;;;;;0CAGd,6LAAC,oIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,QAAQ;;;;;;0CAGV,6LAAC,oIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,WAAW,cAAc;;;;;;;;;;;;;;;;;AAKpC;GA3FgB;KAAA", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger>) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"px-2 py-1.5 text-sm font-medium\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iuBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Switch({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\r\n  return (\r\n    <SwitchPrimitive.Root\r\n      data-slot=\"switch\"\r\n      className={cn(\r\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-5 w-9 shrink-0 items-center rounded-full border-2 border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SwitchPrimitive.Thumb\r\n        data-slot=\"switch-thumb\"\r\n        className={cn(\r\n          \"bg-background pointer-events-none block size-4 rounded-full ring-0 shadow-lg transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\r\n        )}\r\n      />\r\n    </SwitchPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+TACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,qKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV;KArBS", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/settingscomponents/GeneralSettings.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useState } from \"react\";\r\n\r\nexport function GeneralSettings() {\r\n  const { theme, setTheme } = useTheme();\r\n  const [name, setName] = useState(\"\");\r\n  const [email, setEmail] = useState(\"\");\r\n  const [language, setLanguage] = useState(\"en\");\r\n  const [notifications, setNotifications] = useState(true);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  const handleSave = async () => {\r\n    setIsSaving(true);\r\n    // Mock API call\r\n    await new Promise(resolve => setTimeout(resolve, 800));\r\n    setIsSaving(false);\r\n    // You could show a success toast here\r\n  };\r\n\r\n  return (\r\n    <>\r\n    <div className=\"space-y-6\">\r\n      {/* Profile Section */}\r\n      <div className=\"space-y-4\">\r\n        <h3 className=\"text-lg font-medium\">Profile Information</h3>\r\n        <div className=\"grid gap-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"name\">Name</Label>\r\n            <Input\r\n              id=\"name\"\r\n              value={name}\r\n              onChange={(e) => setName(e.target.value)}\r\n              placeholder=\"Your name\"\r\n            />\r\n          </div>\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"email\">Email</Label>\r\n            <Input\r\n              id=\"email\"\r\n              type=\"email\"\r\n              value={email}\r\n              onChange={(e) => setEmail(e.target.value)}\r\n              placeholder=\"<EMAIL>\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Preferences Section */}\r\n      <div className=\"space-y-4\">\r\n        <h3 className=\"text-lg font-medium\">Preferences</h3>\r\n        <div className=\"grid gap-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label>Theme</Label>\r\n            <Select value={theme} onValueChange={setTheme}>\r\n              <SelectTrigger>\r\n                <SelectValue placeholder=\"Select theme\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"light\">Light</SelectItem>\r\n                <SelectItem value=\"dark\">Dark</SelectItem>\r\n                <SelectItem value=\"system\">System</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div className=\"space-y-2\">\r\n            <Label>Language</Label>\r\n            <Select value={language} onValueChange={setLanguage}>\r\n              <SelectTrigger>\r\n                <SelectValue placeholder=\"Select language\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"en\">English</SelectItem>\r\n                <SelectItem value=\"es\">Español</SelectItem>\r\n                <SelectItem value=\"fr\">Français</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"space-y-0.5\">\r\n              <Label>Notifications</Label>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Receive notifications about your agents and tasks\r\n              </p>\r\n            </div>\r\n            <Switch\r\n              checked={notifications}\r\n              onCheckedChange={setNotifications}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-end\">\r\n        <Button \r\n          onClick={handleSave} \r\n          disabled={isSaving}\r\n          className=\"bg-primary text-primary-foreground hover:bg-primary/90\"\r\n        >\r\n          {isSaving ? \"Saving...\" : \"Save Changes\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;;;AAdA;;;;;;;;AAgBO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB,YAAY;QACZ,gBAAgB;QAChB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,YAAY;IACZ,sCAAsC;IACxC;IAEA,qBACE;kBACA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACvC,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAO,eAAe;;8DACnC,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;8CAIjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAU,eAAe;;8DACtC,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAK;;;;;;sEACvB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAK;;;;;;sEACvB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAI7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAI/C,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAMzB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,WAAW,cAAc;;;;;;;;;;;;;;;;;;AAMpC;GAtGgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row gap-2\",\r\n        month: \"flex flex-col gap-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center w-full\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"flex items-center gap-1\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-x-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: cn(\r\n          \"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md\",\r\n          props.mode === \"range\"\r\n            ? \"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md\"\r\n            : \"[&:has([aria-selected])]:rounded-md\"\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"size-8 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_start:\r\n          \"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_range_end:\r\n          \"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Calendar }\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AASA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACoC;IACvC,qBACE,6LAAC,iKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YAC<PERSON>,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAE5D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;QAE/D;QACC,GAAG,KAAK;;;;;;AAGf;KA/DS", "debugId": null}}, {"offset": {"line": 1143, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 1224, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/settingscomponents/LogsSettings.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n\"use client\";\r\n\r\nimport React, { useEffect, useState, useRef, useCallback } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\nimport { format } from \"date-fns\";\r\nimport { CalendarIcon, Search, X, Filter, RefreshCw } from \"lucide-react\";\r\n\r\ninterface LogEntry {\r\n  timestamp: string;\r\n  level: string;\r\n  message: string;\r\n  trace?: string;\r\n  _id: string;\r\n}\r\n\r\ninterface LogsResponse {\r\n  logs: LogEntry[];\r\n  total: number;\r\n  page: number;\r\n  totalPages: number;\r\n}\r\n\r\nexport default function LogsSettings() {\r\n  const [logs, setLogs] = useState<LogEntry[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string>(\"\");\r\n  const [searchQuery, setSearchQuery] = useState<string>(\"\");\r\n  const [levelFilter, setLevelFilter] = useState<string>(\"all\");\r\n  const [startDate, setStartDate] = useState<Date | undefined>(undefined);\r\n  const [endDate, setEndDate] = useState<Date | undefined>(undefined);\r\n  const [page, setPage] = useState<number>(1);\r\n  const [hasMore, setHasMore] = useState<boolean>(true);\r\n  const [totalLogs, setTotalLogs] = useState<number>(0);\r\n  const [totalPages, setTotalPages] = useState<number>(0);\r\n  const [isFiltering, setIsFiltering] = useState<boolean>(false);\r\n  const observer = useRef<IntersectionObserver | null>(null);\r\n  const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n\r\n  const fetchLogs = async (pageNum = 1, reset = false) => {\r\n    try {\r\n      setIsFiltering(true);\r\n      const token = localStorage.getItem(\"access_token\");\r\n      if (!token) {\r\n        setError(\"No access token available\");\r\n        setLoading(false);\r\n        setIsFiltering(false);\r\n        return;\r\n      }\r\n\r\n      // Build query parameters\r\n      const params = new URLSearchParams();\r\n      params.append('page', pageNum.toString());\r\n      params.append('limit', '20');\r\n\r\n      if (levelFilter && levelFilter !== 'all') {\r\n        params.append('level', levelFilter);\r\n      }\r\n\r\n      if (searchQuery.trim()) {\r\n        params.append('search', searchQuery.trim());\r\n      }\r\n\r\n      if (startDate) {\r\n        params.append('startDate', startDate.toISOString().split('T')[0]);\r\n      }\r\n\r\n      if (endDate) {\r\n        params.append('endDate', endDate.toISOString().split('T')[0]);\r\n      }\r\n\r\n      const response = await fetch(`${API_BASE_URL}/api/logs?${params.toString()}`, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch logs\");\r\n      }\r\n\r\n      const data: LogsResponse = await response.json();\r\n\r\n      if (reset) {\r\n        setLogs(data.logs);\r\n      } else {\r\n        setLogs(prev => [...prev, ...data.logs]);\r\n      }\r\n\r\n      setTotalLogs(data.total);\r\n      setTotalPages(data.totalPages);\r\n      setHasMore(pageNum < data.totalPages);\r\n    } catch (err) {\r\n      console.error(\"Error fetching logs:\", err);\r\n      setError(\"Failed to fetch logs. Please try again.\");\r\n    } finally {\r\n      setLoading(false);\r\n      setIsFiltering(false);\r\n    }\r\n  };\r\n\r\n  const handleSearch = () => {\r\n    setPage(1);\r\n    fetchLogs(1, true);\r\n  };\r\n\r\n  const resetFilters = () => {\r\n    setSearchQuery(\"\");\r\n    setLevelFilter(\"all\");\r\n    setStartDate(undefined);\r\n    setEndDate(undefined);\r\n    setPage(1);\r\n    fetchLogs(1, true);\r\n  };\r\n\r\n  const cleanupOldLogs = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const token = localStorage.getItem(\"access_token\");\r\n      if (!token) {\r\n        setError(\"No access token available\");\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      const response = await fetch(`${API_BASE_URL}/api/logs/cleanup`, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to cleanup logs\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      alert(`Successfully deleted ${data.deletedCount} logs older than 4 days`);\r\n\r\n      // Refresh logs after cleanup\r\n      setPage(1);\r\n      fetchLogs(1, true);\r\n    } catch (err) {\r\n      console.error(\"Error cleaning up logs:\", err);\r\n      setError(\"Failed to cleanup logs. Please try again.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Setup intersection observer for infinite scrolling\r\n  const lastLogElementRef = useCallback((node: HTMLTableRowElement) => {\r\n    if (loading) return;\r\n\r\n    if (observer.current) observer.current.disconnect();\r\n\r\n    observer.current = new IntersectionObserver(entries => {\r\n      if (entries[0].isIntersecting && hasMore && !isFiltering) {\r\n        setPage(prevPage => prevPage + 1);\r\n      }\r\n    });\r\n\r\n    if (node) observer.current.observe(node);\r\n  }, [loading, hasMore, isFiltering]);\r\n\r\n  useEffect(() => {\r\n    fetchLogs(1, true);\r\n  }, []);\r\n\r\n  // Fetch more logs when page changes\r\n  useEffect(() => {\r\n    if (page > 1) {\r\n      fetchLogs(page);\r\n    }\r\n  }, [page]);\r\n\r\n  // Determine text color based on log level\r\n  const getLevelColor = (level: string) => {\r\n    switch (level.toLowerCase()) {\r\n      case \"error\":\r\n        return \"text-red-500\";\r\n      case \"warn\":\r\n        return \"text-yellow-500\";\r\n      case \"info\":\r\n        return \"text-green-500\";\r\n      default:\r\n        return \"text-gray-900\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h2 className=\"text-xl font-bold\">Logs</h2>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          onClick={cleanupOldLogs}\r\n          disabled={loading}\r\n        >\r\n          <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n          Clean Old Logs\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n        <div className=\"relative\">\r\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500\" />\r\n          <Input\r\n            placeholder=\"Search logs...\"\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n            className=\"pl-8\"\r\n            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}\r\n          />\r\n        </div>\r\n\r\n        <Select value={levelFilter || \"all\"} onValueChange={setLevelFilter}>\r\n          <SelectTrigger>\r\n            <SelectValue placeholder=\"Filter by level\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Levels</SelectItem>\r\n            <SelectItem value=\"INFO\">Info</SelectItem>\r\n            <SelectItem value=\"WARN\">Warning</SelectItem>\r\n            <SelectItem value=\"ERROR\">Error</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Popover>\r\n          <PopoverTrigger asChild>\r\n            <Button variant=\"outline\" className=\"justify-start text-left font-normal\">\r\n              <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n              {startDate ? format(startDate, 'PPP') : 'Start date'}\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n            <Calendar\r\n              mode=\"single\"\r\n              selected={startDate}\r\n              onSelect={setStartDate}\r\n              initialFocus\r\n            />\r\n          </PopoverContent>\r\n        </Popover>\r\n\r\n        <Popover>\r\n          <PopoverTrigger asChild>\r\n            <Button variant=\"outline\" className=\"justify-start text-left font-normal\">\r\n              <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n              {endDate ? format(endDate, 'PPP') : 'End date'}\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n            <Calendar\r\n              mode=\"single\"\r\n              selected={endDate}\r\n              onSelect={setEndDate}\r\n              initialFocus\r\n            />\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n\r\n      <div className=\"flex gap-2\">\r\n        <Button onClick={handleSearch} disabled={loading || isFiltering}>\r\n          <Filter className=\"h-4 w-4 mr-2\" />\r\n          Apply Filters\r\n        </Button>\r\n        <Button variant=\"outline\" onClick={resetFilters} disabled={loading || isFiltering}>\r\n          <X className=\"h-4 w-4 mr-2\" />\r\n          Reset\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Log count summary */}\r\n      {!loading && logs.length > 0 && (\r\n        <div className=\"text-sm text-gray-500\">\r\n          Showing {logs.length} of {totalLogs} logs {levelFilter && levelFilter !== 'all' && `(filtered by ${levelFilter})`}\r\n          {totalPages > 1 && ` - Page ${page} of ${totalPages}`}\r\n        </div>\r\n      )}\r\n\r\n      {/* Logs Table */}\r\n      {loading && page === 1 ? (\r\n        <div className=\"flex justify-center items-center h-40\">\r\n          <p>Loading logs...</p>\r\n        </div>\r\n      ) : error ? (\r\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\r\n          <p>Error: {error}</p>\r\n        </div>\r\n      ) : logs.length === 0 ? (\r\n        <div className=\"bg-gray-50 border border-gray-200 text-gray-700 px-4 py-8 rounded text-center\">\r\n          <p>No logs found.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-x-auto border rounded-md\">\r\n          <table className=\"min-w-full divide-y divide-gray-200\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Timestamp\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Level\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Message\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Trace\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-200\">\r\n              {logs.map((log, index) => (\r\n                <tr\r\n                  key={log._id || index}\r\n                  ref={index === logs.length - 5 ? lastLogElementRef : null}\r\n                  className=\"hover:bg-gray-50 transition-colors\"\r\n                >\r\n                  <td className=\"px-4 py-3 text-sm text-gray-900\">\r\n                    {new Date(log.timestamp).toLocaleString()}\r\n                  </td>\r\n                  <td className={`px-4 py-3 text-sm ${getLevelColor(log.level)}`}>\r\n                    <span className=\"px-2 py-1 rounded-full text-xs font-medium\">\r\n                      {log.level}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"px-4 py-3 text-sm text-gray-900 max-w-md truncate\">\r\n                    {log.message}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 text-sm text-gray-900 max-w-xs truncate\">\r\n                    {log.trace || \"-\"}\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n\r\n          {/* Loading indicator for infinite scroll */}\r\n          {(loading && page > 1) && (\r\n            <div className=\"py-4 text-center text-gray-500\">\r\n              Loading more logs...\r\n            </div>\r\n          )}\r\n\r\n          {/* End of results message */}\r\n          {!hasMore && logs.length > 0 && (\r\n            <div className=\"py-4 text-center text-gray-500\">\r\n              End of logs\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;AAyCvB;;AAtCvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AA0Be,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA+B;IACrD,MAAM,eAAe,6DAAsC;IAE3D,MAAM,YAAY,OAAO,UAAU,CAAC,EAAE,QAAQ,KAAK;QACjD,IAAI;YACF,eAAe;YACf,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,SAAS;gBACT,WAAW;gBACX,eAAe;gBACf;YACF;YAEA,yBAAyB;YACzB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,QAAQ;YACtC,OAAO,MAAM,CAAC,SAAS;YAEvB,IAAI,eAAe,gBAAgB,OAAO;gBACxC,OAAO,MAAM,CAAC,SAAS;YACzB;YAEA,IAAI,YAAY,IAAI,IAAI;gBACtB,OAAO,MAAM,CAAC,UAAU,YAAY,IAAI;YAC1C;YAEA,IAAI,WAAW;gBACb,OAAO,MAAM,CAAC,aAAa,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAClE;YAEA,IAAI,SAAS;gBACX,OAAO,MAAM,CAAC,WAAW,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC9D;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,OAAO,QAAQ,IAAI,EAAE;gBAC5E,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAqB,MAAM,SAAS,IAAI;YAE9C,IAAI,OAAO;gBACT,QAAQ,KAAK,IAAI;YACnB,OAAO;gBACL,QAAQ,CAAA,OAAQ;2BAAI;2BAAS,KAAK,IAAI;qBAAC;YACzC;YAEA,aAAa,KAAK,KAAK;YACvB,cAAc,KAAK,UAAU;YAC7B,WAAW,UAAU,KAAK,UAAU;QACtC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,WAAW;YACX,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;QACR,UAAU,GAAG;IACf;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,eAAe;QACf,aAAa;QACb,WAAW;QACX,QAAQ;QACR,UAAU,GAAG;IACf;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC,EAAE;gBAC/D,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,CAAC,qBAAqB,EAAE,KAAK,YAAY,CAAC,uBAAuB,CAAC;YAExE,6BAA6B;YAC7B,QAAQ;YACR,UAAU,GAAG;QACf,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qDAAqD;IACrD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACrC,IAAI,SAAS;YAEb,IAAI,SAAS,OAAO,EAAE,SAAS,OAAO,CAAC,UAAU;YAEjD,SAAS,OAAO,GAAG,IAAI;+DAAqB,CAAA;oBAC1C,IAAI,OAAO,CAAC,EAAE,CAAC,cAAc,IAAI,WAAW,CAAC,aAAa;wBACxD;2EAAQ,CAAA,WAAY,WAAW;;oBACjC;gBACF;;YAEA,IAAI,MAAM,SAAS,OAAO,CAAC,OAAO,CAAC;QACrC;sDAAG;QAAC;QAAS;QAAS;KAAY;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,UAAU,GAAG;QACf;iCAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,OAAO,GAAG;gBACZ,UAAU;YACZ;QACF;iCAAG;QAAC;KAAK;IAET,0CAA0C;IAC1C,MAAM,gBAAgB,CAAC;QACrB,OAAQ,MAAM,WAAW;YACvB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoB;;;;;;kCAClC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;gCACV,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;kCAI3C,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO,eAAe;wBAAO,eAAe;;0CAClD,6LAAC,qIAAA,CAAA,gBAAa;0CACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAO;;;;;;kDACzB,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAO;;;;;;kDACzB,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;kCAI9B,6LAAC,sIAAA,CAAA,UAAO;;0CACN,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,6LAAC,iNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,YAAY,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,SAAS;;;;;;;;;;;;0CAG5C,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;gCAAa,OAAM;0CAC3C,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oCACP,MAAK;oCACL,UAAU;oCACV,UAAU;oCACV,YAAY;;;;;;;;;;;;;;;;;kCAKlB,6LAAC,sIAAA,CAAA,UAAO;;0CACN,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,6LAAC,iNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,UAAU,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS;;;;;;;;;;;;0CAGxC,6LAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;gCAAa,OAAM;0CAC3C,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oCACP,MAAK;oCACL,UAAU;oCACV,UAAU;oCACV,YAAY;;;;;;;;;;;;;;;;;;;;;;;0BAMpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAc,UAAU,WAAW;;0CAClD,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGrC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAc,UAAU,WAAW;;0CACpE,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMjC,CAAC,WAAW,KAAK,MAAM,GAAG,mBACzB,6LAAC;gBAAI,WAAU;;oBAAwB;oBAC5B,KAAK,MAAM;oBAAC;oBAAK;oBAAU;oBAAO,eAAe,gBAAgB,SAAS,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;oBAChH,aAAa,KAAK,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE,YAAY;;;;;;;YAKxD,WAAW,SAAS,kBACnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAE;;;;;;;;;;uBAEH,sBACF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;wBAAE;wBAAQ;;;;;;;;;;;uBAEX,KAAK,MAAM,KAAK,kBAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAE;;;;;;;;;;qCAGL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,6LAAC;gCAAM,WAAU;0CACd,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;wCAEC,KAAK,UAAU,KAAK,MAAM,GAAG,IAAI,oBAAoB;wCACrD,WAAU;;0DAEV,6LAAC;gDAAG,WAAU;0DACX,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;0DAEzC,6LAAC;gDAAG,WAAW,CAAC,kBAAkB,EAAE,cAAc,IAAI,KAAK,GAAG;0DAC5D,cAAA,6LAAC;oDAAK,WAAU;8DACb,IAAI,KAAK;;;;;;;;;;;0DAGd,6LAAC;gDAAG,WAAU;0DACX,IAAI,OAAO;;;;;;0DAEd,6LAAC;gDAAG,WAAU;0DACX,IAAI,KAAK,IAAI;;;;;;;uCAhBX,IAAI,GAAG,IAAI;;;;;;;;;;;;;;;;oBAwBtB,WAAW,OAAO,mBAClB,6LAAC;wBAAI,WAAU;kCAAiC;;;;;;oBAMjD,CAAC,WAAW,KAAK,MAAM,GAAG,mBACzB,6LAAC;wBAAI,WAAU;kCAAiC;;;;;;;;;;;;;;;;;;AAQ5D;GA/UwB;KAAA", "debugId": null}}, {"offset": {"line": 1914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/settingscomponents/VoiceSettings.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useState } from \"react\";\r\nimport { APIKeyInput } from \"../APIKeyInput\";\r\n\r\nexport function VoiceSettings() {\r\n  const [elevenlabsKey, setElevenlabsKey] = useState(\"\");\r\n  const [deepgramKey, setDeepgramKey] = useState(\"\");\r\n  const [assemblyai<PERSON><PERSON>, setAssemblyaiKey] = useState(\"\");\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  const testElevenLabs = async () => {\r\n    // Implement actual API test\r\n    await new Promise((resolve) => setTimeout(resolve, 1000));\r\n    return true;\r\n  };\r\n\r\n  const testDeepgram = async () => {\r\n    // Implement actual API test\r\n    await new Promise((resolve) => setTimeout(resolve, 1000));\r\n    return true;\r\n  };\r\n\r\n  const testAssemblyAI = async () => {\r\n    // Implement actual API test\r\n    await new Promise((resolve) => setTimeout(resolve, 1000));\r\n    return true;\r\n  };\r\n\r\n  const handleSaveChanges = async () => {\r\n    setIsSaving(true);\r\n    // Implement API key saving\r\n    await new Promise(resolve => setTimeout(resolve, 1000));\r\n    setIsSaving(false);\r\n    // You could show a success toast here\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"space-y-4\">\r\n        <h3 className=\"text-lg font-medium\">Voice APIs</h3>\r\n        <div className=\"grid gap-6\">\r\n          <APIKeyInput\r\n            label=\"ElevenLabs API Key\"\r\n            description=\"Required for high-quality text-to-speech\"\r\n            value={elevenlabsKey}\r\n            onChange={setElevenlabsKey}\r\n            onTest={testElevenLabs}\r\n          />\r\n\r\n          <APIKeyInput\r\n            label=\"Deepgram API Key\"\r\n            description=\"Required for real-time speech recognition\"\r\n            value={deepgramKey}\r\n            onChange={setDeepgramKey}\r\n            onTest={testDeepgram}\r\n          />\r\n\r\n          <APIKeyInput\r\n            label=\"AssemblyAI API Key\"\r\n            description=\"Required for advanced speech recognition and analysis\"\r\n            value={assemblyaiKey}\r\n            onChange={setAssemblyaiKey}\r\n            onTest={testAssemblyAI}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-end\">\r\n        <Button \r\n          onClick={handleSaveChanges}\r\n          disabled={isSaving}\r\n          className=\"bg-primary text-primary-foreground hover:bg-primary/90\"\r\n        >\r\n          {isSaving ? \"Saving...\" : \"Save Changes\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,iBAAiB;QACrB,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QACnD,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QACnD,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QACnD,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,YAAY;IACZ,sCAAsC;IACxC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsB;;;;;;kCACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,QAAQ;;;;;;0CAGV,6LAAC,oIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,QAAQ;;;;;;0CAGV,6LAAC,oIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,WAAW,cAAc;;;;;;;;;;;;;;;;;AAKpC;GA1EgB;KAAA", "debugId": null}}, {"offset": {"line": 2054, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border \",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\"flex flex-col gap-1.5 px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 2155, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 2230, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/config.ts"], "sourcesContent": ["// API Configuration\r\nexport const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';\r\n// Stripe Configuration\r\nexport const STRIPE_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '';\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;AACG;AAAhB,MAAM,UAAU,6DAAsC;AAEtD,MAAM,yBAAyB,mJAAkD", "debugId": null}}, {"offset": {"line": 2247, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/organizations.ts"], "sourcesContent": ["import { API_URL } from \"./config\";\r\n\r\n// Types\r\nexport interface Organization {\r\n  _id: string;\r\n  name: string;\r\n  description?: string;\r\n  status: 'active' | 'inactive' | 'suspended';\r\n  credits: number; // Paid credits\r\n  monthlyFreeCredits: number; // Monthly allowance in minutes\r\n  monthlyFreeCreditsUsed: number; // Used free credits this month\r\n  lastMonthlyReset: string; // Last time monthly credits were reset\r\n  usingFreeCredits: boolean; // Whether currently using free credits\r\n  autoRechargeEnabled: boolean;\r\n  autoRechargeThreshold: number;\r\n  autoRechargeAmount: number;\r\n  // Billing configuration settings (moved from global settings)\r\n  callPricePerMinute: number;\r\n  minimumCreditsThreshold: number;\r\n  monthlyMinutesAllowance: number;\r\n  stripeCustomerId?: string;\r\n  // Email notification settings\r\n  fullName?: string; // Client's full name for email personalization\r\n  email?: string; // Email address to send credit notifications to\r\n  lastWarningEmailSent?: string; // Last time warning email was sent\r\n  lastRunoutEmailSent?: string; // Last time runout email was sent\r\n  // S3 Configuration for knowledge base file storage\r\n  s3Config?: {\r\n    accessKeyId?: string;\r\n    secretAccessKey?: string;\r\n    region?: string;\r\n    bucketName?: string;\r\n    enabled?: boolean;\r\n  };\r\n  adminUsers: string[];\r\n  users: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface CreateOrganizationRequest {\r\n  name: string;\r\n  description?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  adminUsers?: string[];\r\n}\r\n\r\nexport interface UpdateOrganizationRequest {\r\n  name?: string;\r\n  description?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  adminUsers?: string[];\r\n  users?: string[];\r\n}\r\n\r\nexport interface UpdateOrganizationBillingRequest {\r\n  credits?: number;\r\n  autoRechargeEnabled?: boolean;\r\n  autoRechargeThreshold?: number;\r\n  autoRechargeAmount?: number;\r\n  callPricePerMinute?: number;\r\n  minimumCreditsThreshold?: number;\r\n  monthlyMinutesAllowance?: number;\r\n  monthlyResetDate?: number;\r\n}\r\n\r\nexport interface UpdateOrganizationSettingsRequest {\r\n  monthlyResetDate?: number;\r\n  fullName?: string;\r\n  email?: string;\r\n  s3Config?: {\r\n    accessKeyId?: string;\r\n    secretAccessKey?: string;\r\n    region?: string;\r\n    bucketName?: string;\r\n    enabled?: boolean;\r\n  };\r\n}\r\n\r\n// API Functions\r\nexport const getOrganizations = async (): Promise<Organization[]> => {\r\n  const response = await fetch(`${API_URL}/api/organizations`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch organizations');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const getMyOrganizations = async (): Promise<Organization[]> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/my-organizations`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch your organizations');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const getOrganization = async (id: string): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const createOrganization = async (data: CreateOrganizationRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to create organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganization = async (id: string, data: UpdateOrganizationRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganizationBilling = async (id: string, data: UpdateOrganizationBillingRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}/billing`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization billing');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganizationSettings = async (id: string, data: UpdateOrganizationSettingsRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}/settings`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization settings');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const deleteOrganization = async (id: string): Promise<{ message: string }> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'DELETE',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to delete organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const addUserToOrganization = async (organizationId: string, userId: string, isAdmin: boolean): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${organizationId}/users/${userId}`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify({ isAdmin }),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to add user to organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const removeUserFromOrganization = async (organizationId: string, userId: string): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${organizationId}/users/${userId}`, {\r\n    method: 'DELETE',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to remove user from organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAgFO,MAAM,mBAAmB;IAC9B,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,EAAE;QAC3D,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mCAAmC,CAAC,EAAE;QAC5E,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,EAAE;QAC3D,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO,IAAY;IACnD,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,4BAA4B,OAAO,IAAY;IAC1D,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,GAAG,QAAQ,CAAC,EAAE;QACzE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,6BAA6B,OAAO,IAAY;IAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,GAAG,SAAS,CAAC,EAAE;QAC1E,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,wBAAwB,OAAO,gBAAwB,QAAgB;IAClF,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,eAAe,OAAO,EAAE,QAAQ,EAAE;QAC7F,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAQ;IACjC;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,6BAA6B,OAAO,gBAAwB;IACvE,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,eAAe,OAAO,EAAE,QAAQ,EAAE;QAC7F,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 2417, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/s3.ts"], "sourcesContent": ["import { authFetch } from '@/lib/authFetch';\n\nconst API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';\n\nexport interface S3Config {\n  accessKeyId: string;\n  secretAccessKey: string;\n  region: string;\n  bucketName: string;\n}\n\nexport interface S3TestResult {\n  success: boolean;\n  message: string;\n}\n\nexport interface S3UploadResult {\n  success: boolean;\n  message: string;\n  data: {\n    key: string;\n    url: string;\n    size: number;\n  };\n}\n\nexport interface S3DeleteResult {\n  success: boolean;\n  message: string;\n}\n\nexport interface S3ListResult {\n  success: boolean;\n  message: string;\n  data: Array<{\n    Key: string;\n    LastModified: string;\n    Size: number;\n    StorageClass: string;\n  }>;\n}\n\n/**\n * Test S3 connection with provided credentials\n */\nexport const testS3Connection = async (config: S3Config): Promise<S3TestResult> => {\n  const response = await authFetch(`${API_URL}/api/s3/test-connection`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(config),\n  });\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to test S3 connection');\n  }\n\n  return response.json();\n};\n\n/**\n * Upload file to organization S3 bucket\n */\nexport const uploadFileToS3 = async (\n  organizationId: string,\n  file: File,\n  knowledgeBaseId?: string\n): Promise<S3UploadResult> => {\n  const formData = new FormData();\n  formData.append('file', file);\n  \n  if (knowledgeBaseId) {\n    formData.append('knowledgeBaseId', knowledgeBaseId);\n  }\n\n  const response = await authFetch(`${API_URL}/api/s3/upload/${organizationId}`, {\n    method: 'POST',\n    body: formData,\n  });\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to upload file to S3');\n  }\n\n  return response.json();\n};\n\n/**\n * Delete file from organization S3 bucket\n */\nexport const deleteFileFromS3 = async (\n  organizationId: string,\n  fileKey: string\n): Promise<S3DeleteResult> => {\n  // URL encode the file key to handle special characters\n  const encodedKey = encodeURIComponent(fileKey);\n  \n  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/file/${encodedKey}`, {\n    method: 'DELETE',\n  });\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to delete file from S3');\n  }\n\n  return response.json();\n};\n\n/**\n * List files in organization S3 bucket\n */\nexport const listS3Files = async (organizationId: string): Promise<S3ListResult> => {\n  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/files`);\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to list S3 files');\n  }\n\n  return response.json();\n};\n\n/**\n * Get presigned URL for file download\n */\nexport const getS3PresignedUrl = async (\n  organizationId: string,\n  fileKey: string\n): Promise<{ success: boolean; message: string; data: { url: string } }> => {\n  // URL encode the file key to handle special characters\n  const encodedKey = encodeURIComponent(fileKey);\n  \n  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/presigned-url/${encodedKey}`);\n\n  if (!response.ok) {\n    const error = await response.json();\n    throw new Error(error.message || 'Failed to get presigned URL');\n  }\n\n  return response.json();\n};\n\n/**\n * Helper function to format file size\n */\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\n/**\n * Helper function to get file extension\n */\nexport const getFileExtension = (filename: string): string => {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\n};\n\n/**\n * Helper function to get file type icon\n */\nexport const getFileTypeIcon = (filename: string): string => {\n  const extension = getFileExtension(filename).toLowerCase();\n  \n  switch (extension) {\n    case 'pdf':\n      return '📄';\n    case 'doc':\n    case 'docx':\n      return '📝';\n    case 'xls':\n    case 'xlsx':\n      return '📊';\n    case 'txt':\n      return '📋';\n    case 'jpg':\n    case 'jpeg':\n    case 'png':\n    case 'gif':\n      return '🖼️';\n    case 'mp4':\n    case 'avi':\n    case 'mov':\n      return '🎥';\n    case 'mp3':\n    case 'wav':\n      return '🎵';\n    case 'zip':\n    case 'rar':\n      return '📦';\n    default:\n      return '📁';\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEgB;AAFhB;;AAEA,MAAM,UAAU,6DAAsC;AA2C/C,MAAM,mBAAmB,OAAO;IACrC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,uBAAuB,CAAC,EAAE;QACpE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,iBAAiB,OAC5B,gBACA,MACA;IAEA,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IAExB,IAAI,iBAAiB;QACnB,SAAS,MAAM,CAAC,mBAAmB;IACrC;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,eAAe,EAAE,gBAAgB,EAAE;QAC7E,QAAQ;QACR,MAAM;IACR;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,mBAAmB,OAC9B,gBACA;IAEA,uDAAuD;IACvD,MAAM,aAAa,mBAAmB;IAEtC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,QAAQ,EAAE,eAAe,MAAM,EAAE,YAAY,EAAE;QACzF,QAAQ;IACV;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,QAAQ,EAAE,eAAe,MAAM,CAAC;IAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,oBAAoB,OAC/B,gBACA;IAEA,uDAAuD;IACvD,MAAM,aAAa,mBAAmB;IAEtC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,QAAQ,QAAQ,EAAE,eAAe,eAAe,EAAE,YAAY;IAElG,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAKO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,iBAAiB,UAAU,WAAW;IAExD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 2547, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/settingscomponents/S3Settings.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\nimport { Loader2, Eye, EyeOff, Database, CheckCircle, AlertCircle } from \"lucide-react\";\nimport { toast } from \"sonner\";\nimport { updateOrganizationSettings, Organization } from \"@/app/api/organizations\";\nimport { testS3Connection } from \"@/app/api/s3\";\n\ninterface S3SettingsProps {\n  organization: Organization | null;\n  organizations?: Organization[];\n  onUpdate?: (organization: Organization) => void;\n  onOrganizationChange?: (organization: Organization) => void;\n}\n\nexport function S3Settings({ organization, organizations, onUpdate, onOrganizationChange }: S3SettingsProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [showSecretKey, setShowSecretKey] = useState(false);\n  const [testingConnection, setTestingConnection] = useState(false);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    enabled: false,\n    accessKeyId: \"\",\n    secretAccessKey: \"\",\n    region: \"us-east-1\",\n    bucketName: \"\",\n  });\n\n  // Load organization S3 config\n  useEffect(() => {\n    if (organization?.s3Config) {\n      setFormData({\n        enabled: organization.s3Config.enabled || false,\n        accessKeyId: organization.s3Config.accessKeyId || \"\",\n        secretAccessKey: organization.s3Config.secretAccessKey || \"\",\n        region: organization.s3Config.region || \"us-east-1\",\n        bucketName: organization.s3Config.bucketName || \"\",\n      });\n    }\n  }, [organization]);\n\n  const handleInputChange = (field: string, value: string | boolean) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSave = async () => {\n    if (!organization) {\n      toast.error(\"No organization selected\");\n      return;\n    }\n\n    // Validation\n    if (formData.enabled) {\n      if (!formData.accessKeyId.trim()) {\n        toast.error(\"Access Key ID is required when S3 is enabled\");\n        return;\n      }\n      if (!formData.secretAccessKey.trim()) {\n        toast.error(\"Secret Access Key is required when S3 is enabled\");\n        return;\n      }\n      if (!formData.region.trim()) {\n        toast.error(\"Region is required when S3 is enabled\");\n        return;\n      }\n      if (!formData.bucketName.trim()) {\n        toast.error(\"Bucket Name is required when S3 is enabled\");\n        return;\n      }\n    }\n\n    setIsLoading(true);\n    try {\n      const updatedOrg = await updateOrganizationSettings(organization._id, {\n        s3Config: formData\n      });\n\n      toast.success(\"S3 settings saved successfully\");\n      onUpdate?.(updatedOrg);\n    } catch (error) {\n      console.error(\"Failed to save S3 settings:\", error);\n      toast.error(error instanceof Error ? error.message : \"Failed to save S3 settings\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const testConnection = async () => {\n    if (!formData.accessKeyId || !formData.secretAccessKey || !formData.region || !formData.bucketName) {\n      toast.error(\"Please fill in all S3 credentials before testing\");\n      return;\n    }\n\n    setTestingConnection(true);\n    try {\n      const result = await testS3Connection({\n        accessKeyId: formData.accessKeyId,\n        secretAccessKey: formData.secretAccessKey,\n        region: formData.region,\n        bucketName: formData.bucketName,\n      });\n\n      if (result.success) {\n        toast.success(result.message);\n      } else {\n        toast.error(result.message);\n      }\n    } catch (error) {\n      console.error(\"S3 connection test failed:\", error);\n      toast.error(error instanceof Error ? error.message : \"S3 connection test failed. Please check your credentials.\");\n    } finally {\n      setTestingConnection(false);\n    }\n  };\n\n  const awsRegions = [\n    { value: \"us-east-1\", label: \"US East (N. Virginia)\" },\n    { value: \"us-east-2\", label: \"US East (Ohio)\" },\n    { value: \"us-west-1\", label: \"US West (N. California)\" },\n    { value: \"us-west-2\", label: \"US West (Oregon)\" },\n    { value: \"eu-west-1\", label: \"Europe (Ireland)\" },\n    { value: \"eu-west-2\", label: \"Europe (London)\" },\n    { value: \"eu-west-3\", label: \"Europe (Paris)\" },\n    { value: \"eu-central-1\", label: \"Europe (Frankfurt)\" },\n    { value: \"ap-southeast-1\", label: \"Asia Pacific (Singapore)\" },\n    { value: \"ap-southeast-2\", label: \"Asia Pacific (Sydney)\" },\n    { value: \"ap-northeast-1\", label: \"Asia Pacific (Tokyo)\" },\n    { value: \"ap-south-1\", label: \"Asia Pacific (Mumbai)\" },\n  ];\n\n  if (!organization) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center\">\n          <Database className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n            No Organization Selected\n          </h3>\n          <p className=\"text-gray-500 dark:text-gray-400\">\n            Please select an organization to configure S3 settings\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Organization Selector */}\n      {organizations && organizations.length > 1 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Select Organization</CardTitle>\n            <CardDescription>\n              Choose which organization to configure S3 settings for\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"organization-select\">Organization</Label>\n              <select\n                id=\"organization-select\"\n                value={organization?._id || \"\"}\n                onChange={(e) => {\n                  const selectedOrg = organizations.find(org => org._id === e.target.value);\n                  if (selectedOrg && onOrganizationChange) {\n                    onOrganizationChange(selectedOrg);\n                  }\n                }}\n                className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\n              >\n                {organizations.map((org) => (\n                  <option key={org._id} value={org._id}>\n                    {org.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Database className=\"h-5 w-5\" />\n            <span>S3 Storage Configuration</span>\n          </CardTitle>\n          <CardDescription>\n            Configure AWS S3 storage for knowledge base files. Each organization can have its own S3 bucket for secure file storage.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Enable S3 Toggle */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-0.5\">\n              <Label className=\"text-base\">Enable S3 Storage</Label>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                Enable AWS S3 storage for this organization's knowledge base files\n              </p>\n            </div>\n            <Switch\n              checked={formData.enabled}\n              onCheckedChange={(checked) => handleInputChange(\"enabled\", checked)}\n            />\n          </div>\n\n          {formData.enabled && (\n            <>\n              <Alert>\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription>\n                  Make sure your AWS credentials have the necessary permissions for S3 operations (s3:GetObject, s3:PutObject, s3:DeleteObject, s3:ListBucket).\n                </AlertDescription>\n              </Alert>\n\n              {/* AWS Credentials */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"accessKeyId\">Access Key ID *</Label>\n                  <Input\n                    id=\"accessKeyId\"\n                    type=\"text\"\n                    placeholder=\"AKIAIOSFODNN7EXAMPLE\"\n                    value={formData.accessKeyId}\n                    onChange={(e) => handleInputChange(\"accessKeyId\", e.target.value)}\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"secretAccessKey\">Secret Access Key *</Label>\n                  <div className=\"relative\">\n                    <Input\n                      id=\"secretAccessKey\"\n                      type={showSecretKey ? \"text\" : \"password\"}\n                      placeholder=\"wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY\"\n                      value={formData.secretAccessKey}\n                      onChange={(e) => handleInputChange(\"secretAccessKey\", e.target.value)}\n                      className=\"pr-10\"\n                    />\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\n                      onClick={() => setShowSecretKey(!showSecretKey)}\n                    >\n                      {showSecretKey ? (\n                        <EyeOff className=\"h-4 w-4\" />\n                      ) : (\n                        <Eye className=\"h-4 w-4\" />\n                      )}\n                    </Button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"region\">AWS Region *</Label>\n                  <select\n                    id=\"region\"\n                    value={formData.region}\n                    onChange={(e) => handleInputChange(\"region\", e.target.value)}\n                    className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\n                  >\n                    {awsRegions.map((region) => (\n                      <option key={region.value} value={region.value}>\n                        {region.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"bucketName\">Bucket Name *</Label>\n                  <Input\n                    id=\"bucketName\"\n                    type=\"text\"\n                    placeholder=\"my-organization-knowledge-base\"\n                    value={formData.bucketName}\n                    onChange={(e) => handleInputChange(\"bucketName\", e.target.value)}\n                  />\n                </div>\n              </div>\n\n              {/* Test Connection */}\n              <div className=\"flex items-center space-x-4\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={testConnection}\n                  disabled={testingConnection || !formData.accessKeyId || !formData.secretAccessKey}\n                >\n                  {testingConnection ? (\n                    <>\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Testing Connection...\n                    </>\n                  ) : (\n                    <>\n                      <CheckCircle className=\"h-4 w-4 mr-2\" />\n                      Test Connection\n                    </>\n                  )}\n                </Button>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  Test your S3 credentials and bucket access\n                </p>\n              </div>\n            </>\n          )}\n\n          {/* Save Button */}\n          <div className=\"flex justify-end pt-4 border-t\">\n            <Button onClick={handleSave} disabled={isLoading}>\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                  Saving...\n                </>\n              ) : (\n                \"Save S3 Settings\"\n              )}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Information Card */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-sm\">How it works</CardTitle>\n        </CardHeader>\n        <CardContent className=\"text-sm text-gray-600 dark:text-gray-400 space-y-2\">\n          <p>• Files uploaded to the knowledge base will be stored in your configured S3 bucket</p>\n          <p>• Each organization uses its own S3 configuration for data isolation</p>\n          <p>• Files are organized in folders by knowledge base ID within your bucket</p>\n          <p>• Make sure your bucket has appropriate CORS settings for web uploads</p>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAqBO,SAAS,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,oBAAoB,EAAmB;;IACzG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,QAAQ;QACR,YAAY;IACd;IAEA,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,cAAc,UAAU;gBAC1B,YAAY;oBACV,SAAS,aAAa,QAAQ,CAAC,OAAO,IAAI;oBAC1C,aAAa,aAAa,QAAQ,CAAC,WAAW,IAAI;oBAClD,iBAAiB,aAAa,QAAQ,CAAC,eAAe,IAAI;oBAC1D,QAAQ,aAAa,QAAQ,CAAC,MAAM,IAAI;oBACxC,YAAY,aAAa,QAAQ,CAAC,UAAU,IAAI;gBAClD;YACF;QACF;+BAAG;QAAC;KAAa;IAEjB,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,cAAc;YACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI,SAAS,OAAO,EAAE;YACpB,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;gBAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,IAAI,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI;gBACpC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI;gBAC3B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,IAAI;gBAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,aAAa,MAAM,CAAA,GAAA,qIAAA,CAAA,6BAA0B,AAAD,EAAE,aAAa,GAAG,EAAE;gBACpE,UAAU;YACZ;YAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,eAAe,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,UAAU,EAAE;YAClG,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qBAAqB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE;gBACpC,aAAa,SAAS,WAAW;gBACjC,iBAAiB,SAAS,eAAe;gBACzC,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;YACjC;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;YAC9B,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,OAAO;YAAa,OAAO;QAAwB;QACrD;YAAE,OAAO;YAAa,OAAO;QAAiB;QAC9C;YAAE,OAAO;YAAa,OAAO;QAA0B;QACvD;YAAE,OAAO;YAAa,OAAO;QAAmB;QAChD;YAAE,OAAO;YAAa,OAAO;QAAmB;QAChD;YAAE,OAAO;YAAa,OAAO;QAAkB;QAC/C;YAAE,OAAO;YAAa,OAAO;QAAiB;QAC9C;YAAE,OAAO;YAAgB,OAAO;QAAqB;QACrD;YAAE,OAAO;YAAkB,OAAO;QAA2B;QAC7D;YAAE,OAAO;YAAkB,OAAO;QAAwB;QAC1D;YAAE,OAAO;YAAkB,OAAO;QAAuB;QACzD;YAAE,OAAO;YAAc,OAAO;QAAwB;KACvD;IAED,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,iBAAiB,cAAc,MAAM,GAAG,mBACvC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;0CAC/B,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAsB;;;;;;8CACrC,6LAAC;oCACC,IAAG;oCACH,OAAO,cAAc,OAAO;oCAC5B,UAAU,CAAC;wCACT,MAAM,cAAc,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,EAAE,MAAM,CAAC,KAAK;wCACxE,IAAI,eAAe,sBAAsB;4CACvC,qBAAqB;wCACvB;oCACF;oCACA,WAAU;8CAET,cAAc,GAAG,CAAC,CAAC,oBAClB,6LAAC;4CAAqB,OAAO,IAAI,GAAG;sDACjC,IAAI,IAAI;2CADE,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAY;;;;;;0DAC7B,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAI1D,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,SAAS,OAAO;wCACzB,iBAAiB,CAAC,UAAY,kBAAkB,WAAW;;;;;;;;;;;;4BAI9D,SAAS,OAAO,kBACf;;kDACE,6LAAC,oIAAA,CAAA,QAAK;;0DACJ,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC,oIAAA,CAAA,mBAAgB;0DAAC;;;;;;;;;;;;kDAMpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAIpE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAM,gBAAgB,SAAS;gEAC/B,aAAY;gEACZ,OAAO,SAAS,eAAe;gEAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACpE,WAAU;;;;;;0EAEZ,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,iBAAiB,CAAC;0EAEhC,8BACC,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAElB,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAS;;;;;;kEACxB,6LAAC;wDACC,IAAG;wDACH,OAAO,SAAS,MAAM;wDACtB,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;wDAC3D,WAAU;kEAET,WAAW,GAAG,CAAC,CAAC,uBACf,6LAAC;gEAA0B,OAAO,OAAO,KAAK;0EAC3C,OAAO,KAAK;+DADF,OAAO,KAAK;;;;;;;;;;;;;;;;0DAO/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;kDAMrE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;gDACT,UAAU,qBAAqB,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,eAAe;0DAEhF,kCACC;;sEACE,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA8B;;iFAInD;;sEACE,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;0DAK9C,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;0CAQ9D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAY,UAAU;8CACpC,0BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKb;GA5UgB;KAAA", "debugId": null}}, {"offset": {"line": 3300, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2fACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3376, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28workspace%29/settings/SettingsContent.tsx"], "sourcesContent": ["\r\n\"use client\";\r\n\r\nimport { APISettings } from \"@/components/settingscomponents/APISettings\";\r\nimport { GeneralSettings } from \"@/components/settingscomponents/GeneralSettings\";\r\nimport LogsSettings from \"@/components/settingscomponents/LogsSettings\";\r\nimport { VoiceSettings } from \"@/components/settingscomponents/VoiceSettings\";\r\nimport { S3Settings } from \"@/components/settingscomponents/S3Settings\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { authFetch } from \"@/lib/authFetch\";\r\nimport { getMyOrganizations, Organization } from \"@/app/api/organizations\";\r\nimport { useState, useEffect } from \"react\";\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n\r\ninterface UserInfo {\r\n  userId: string;\r\n  email: string;\r\n  role: string;\r\n}\r\n\r\nexport default function SettingsContent() {\r\n  const [activeTab, setActiveTab] = useState(\"general\");\r\n  const [user, setUser] = useState<UserInfo | null>(null);\r\n  const [organizations, setOrganizations] = useState<Organization[]>([]);\r\n  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);\r\n\r\n  useEffect(() => {\r\n    async function fetchUserData() {\r\n      // Use authFetch instead of regular fetch\r\n      const response = await authFetch(`${API_BASE_URL}/api/auth/me`);\r\n      const userData = await response.json();\r\n      setUser(userData);\r\n    }\r\n\r\n    async function fetchOrganizations() {\r\n      try {\r\n        const orgs = await getMyOrganizations();\r\n        setOrganizations(orgs);\r\n        if (orgs.length > 0) {\r\n          setSelectedOrganization(orgs[0]); // Select first organization by default\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to fetch organizations:\", error);\r\n      }\r\n    }\r\n\r\n    fetchUserData();\r\n    fetchOrganizations();\r\n  }, []);\r\n\r\n  const handleOrganizationUpdate = (updatedOrg: Organization) => {\r\n    setSelectedOrganization(updatedOrg);\r\n    setOrganizations(orgs =>\r\n      orgs.map(org => org._id === updatedOrg._id ? updatedOrg : org)\r\n    );\r\n  };\r\n\r\n  const showLogs = user?.role === \"superadmin\";\r\n\r\n  // Determine grid columns based on visible tabs\r\n  let tabsGridCols = \"grid-cols-4\"; // Default: general, api, voice, s3\r\n  if (showLogs) {\r\n    tabsGridCols = \"grid-cols-5\"; // Add logs tab\r\n  }\r\n\r\n  return (\r\n    <div className=\"container py-6 px-4 md:px-6\">\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"text-2xl\">Settings</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Tabs\r\n            value={activeTab}\r\n            onValueChange={setActiveTab}\r\n            className=\"w-full\"\r\n          >\r\n            <TabsList className={`grid w-full ${tabsGridCols} mb-8`}>\r\n              <TabsTrigger value=\"general\">General</TabsTrigger>\r\n              <TabsTrigger value=\"api\">API Keys</TabsTrigger>\r\n              <TabsTrigger value=\"voice\">Voice Settings</TabsTrigger>\r\n              <TabsTrigger value=\"s3\">S3 Storage</TabsTrigger>\r\n              {showLogs && <TabsTrigger value=\"logs\">Logs</TabsTrigger>}\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"general\">\r\n              <GeneralSettings />\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"api\">\r\n              <APISettings />\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"voice\">\r\n              <VoiceSettings />\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"s3\">\r\n              <S3Settings\r\n                organization={selectedOrganization}\r\n                organizations={organizations}\r\n                onUpdate={handleOrganizationUpdate}\r\n                onOrganizationChange={setSelectedOrganization}\r\n              />\r\n            </TabsContent>\r\n\r\n            {showLogs && (\r\n              <TabsContent value=\"logs\">\r\n                <LogsSettings />\r\n              </TabsContent>\r\n            )}\r\n          </Tabs>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAcqB;;AAXrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAaA,MAAM,eAAe,6DAAsC;AAQ5C,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEtF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,eAAe;gBACb,yCAAyC;gBACzC,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD,EAAE,GAAG,aAAa,YAAY,CAAC;gBAC9D,MAAM,WAAW,MAAM,SAAS,IAAI;gBACpC,QAAQ;YACV;YAEA,eAAe;gBACb,IAAI;oBACF,MAAM,OAAO,MAAM,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;oBACpC,iBAAiB;oBACjB,IAAI,KAAK,MAAM,GAAG,GAAG;wBACnB,wBAAwB,IAAI,CAAC,EAAE,GAAG,uCAAuC;oBAC3E;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAClD;YACF;YAEA;YACA;QACF;oCAAG,EAAE;IAEL,MAAM,2BAA2B,CAAC;QAChC,wBAAwB;QACxB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,WAAW,GAAG,GAAG,aAAa;IAE9D;IAEA,MAAM,WAAW,MAAM,SAAS;IAEhC,+CAA+C;IAC/C,IAAI,eAAe,eAAe,mCAAmC;IACrE,IAAI,UAAU;QACZ,eAAe,eAAe,eAAe;IAC/C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;;;;;;8BAElC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBACH,OAAO;wBACP,eAAe;wBACf,WAAU;;0CAEV,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAW,CAAC,YAAY,EAAE,aAAa,KAAK,CAAC;;kDACrD,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAU;;;;;;kDAC7B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAM;;;;;;kDACzB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAQ;;;;;;kDAC3B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAK;;;;;;oCACvB,0BAAY,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAO;;;;;;;;;;;;0CAGzC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CACjB,cAAA,6LAAC,8JAAA,CAAA,kBAAe;;;;;;;;;;0CAGlB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CACjB,cAAA,6LAAC,0JAAA,CAAA,cAAW;;;;;;;;;;0CAGd,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CACjB,cAAA,6LAAC,4JAAA,CAAA,gBAAa;;;;;;;;;;0CAGhB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CACjB,cAAA,6LAAC,yJAAA,CAAA,aAAU;oCACT,cAAc;oCACd,eAAe;oCACf,UAAU;oCACV,sBAAsB;;;;;;;;;;;4BAIzB,0BACC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CACjB,cAAA,6LAAC,2JAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7B;GAhGwB;KAAA", "debugId": null}}]}