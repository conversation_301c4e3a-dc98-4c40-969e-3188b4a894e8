{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/assets/img/OROVA-PURPLE.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 724, height: 118, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAYAAADjAO9DAAAAIElEQVR42mNIjam5aWsRX22oFwbC/UBcCcSlQFwOYgMAuCsKjLMWulwAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 1 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,gIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkJ,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/assets/img/OROVA-WHITE.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 732, height: 124, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAYAAADjAO9DAAAAIklEQVR42mM4uvfUnc8fv1T9+/evFIi7gLgciAuBuBgkBgDAoxsmTQBGxAAAAABJRU5ErkJggg==\", blurWidth: 8, blurHeight: 1 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAsJ,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatCurrency(amount: number): string {\r\n  return new Intl.NumberFormat('en-US', {\r\n    style: 'currency',\r\n    currency: 'USD',\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2,\r\n  }).format(amount);\r\n}\r\n\r\nexport function formatPhoneNumber(phoneNumber: string): string {\r\n  // Assuming US phone format +1XXXXXXXXXX\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n\r\n  // Check if it's a valid US number\r\n  if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+${cleaned.substring(0, 1)} (${cleaned.substring(1, 4)}) ${cleaned.substring(4, 7)}-${cleaned.substring(7, 11)}`;\r\n  } else if (cleaned.length === 10) {\r\n    return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6, 10)}`;\r\n  }\r\n\r\n  // Return formatted or as-is if can't format\r\n  return phoneNumber;\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,kBAAkB,WAAmB;IACnD,wCAAwC;IACxC,MAAM,UAAU,YAAY,OAAO,CAAC,OAAO;IAE3C,kCAAkC;IAClC,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,MAAM;QACpD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,KAAK;IAC1H,OAAO,IAAI,QAAQ,MAAM,KAAK,IAAI;QAChC,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,KAAK;IACjG;IAEA,4CAA4C;IAC5C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gpBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28auth%29/actions/auth.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\"use server\";\r\nimport { cookies } from \"next/headers\";\r\nimport { registerSchema, loginSchema } from \"@/lib/validations/authSchema\";\r\n\r\n\r\n\r\nexport type ActionResult = {\r\n  success: boolean;\r\n  message: string;\r\n  fieldErrors?: Record<string, string[]>;\r\n  redirect?: string;\r\n  tokens?: {\r\n    access_token: string;\r\n    refresh_token: string;\r\n  };\r\n};\r\n \r\nexport type UserInfo = {\r\n  fullName: string;\r\n  userId: string;\r\n  email: string;\r\n  role: string;\r\n};\r\n \r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n \r\n \r\nexport async function registerUser(formData: FormData): Promise<ActionResult> {\r\n    // Extract data from form\r\n    const rawData = {\r\n      fullName: formData.get(\"fullName\"),\r\n      email: formData.get(\"email\"),\r\n      password: formData.get(\"password\"),\r\n    };\r\n \r\n    // Validate with Zod\r\n    const validationResult = registerSchema.safeParse(rawData);\r\n   \r\n    if (!validationResult.success) {\r\n      // Return validation errors\r\n      return {\r\n        success: false,\r\n        message: \"Validation failed\",\r\n        fieldErrors: validationResult.error.flatten().fieldErrors,\r\n      };\r\n    }\r\n \r\n    // Validation passed, destructure the validated data\r\n    const { fullName, email, password } = validationResult.data;\r\n \r\n    try {\r\n      // Make API request to registration endpoint\r\n      const response = await fetch(`${API_BASE_URL}/api/users/register`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          fullName,\r\n          email,\r\n          password,\r\n        }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      // First check if response is ok\r\n      if (!response.ok) {\r\n        try {\r\n          // Try to parse as JSON for error details\r\n          const errorData = await response.json();\r\n          return {\r\n            success: false,\r\n            message: errorData.message || \"Registration failed. Please try again.\",\r\n          };\r\n        } catch {\r\n          // If not JSON, get text or use statusText\r\n          const errorText = await response.text().catch(() => response.statusText);\r\n          return {\r\n            success: false,\r\n            message: errorText || \"Registration failed. Please try again.\",\r\n          };\r\n        }\r\n      }\r\n \r\n      // For successful responses, get the text content\r\n      const successText = await response.text();\r\n     \r\n      return {\r\n        success: true,\r\n        message: successText || \"User registered successfully! Awaiting admin approval.\"\r\n      };\r\n \r\n    } catch (error) {\r\n      console.error(\"Registration error:\", error);\r\n      return {\r\n        success: false,\r\n        message: \"An unexpected error occurred. Please try again later.\"\r\n      };\r\n    }\r\n  }\r\n \r\nexport async function loginUser(formData: FormData): Promise<ActionResult> {\r\n    const rawData = {\r\n      email: formData.get(\"email\"),\r\n      password: formData.get(\"password\"),\r\n    };\r\n \r\n    // Validate with Zod\r\n    const validationResult = loginSchema.safeParse(rawData);\r\n   \r\n    if (!validationResult.success) {\r\n      // Return validation errors\r\n      return {\r\n        success: false,\r\n        message: \"Validation failed\",\r\n        fieldErrors: validationResult.error.flatten().fieldErrors,\r\n      };\r\n    }\r\n \r\n    // Validation passed, destructure the validated data\r\n    const { email, password } = validationResult.data;\r\n \r\n    try {\r\n      // Make API request to login endpoint\r\n      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          email,\r\n          password,\r\n        }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      // Check if the response is OK\r\n      if (!response.ok) {\r\n        try {\r\n          const errorData = await response.json();\r\n          return {\r\n            success: false,\r\n            message: errorData.message || \"Login failed. Please check your credentials.\",\r\n          };\r\n        } catch {\r\n          return {\r\n            success: false,\r\n            message: response.statusText || \"Login failed. Please try again.\",\r\n          };\r\n        }\r\n      }\r\n \r\n      // Parse the successful response to get tokens\r\n      try {\r\n        const data = await response.json();\r\n       \r\n        if (data.access_token && data.refresh_token) {\r\n          // Return tokens in the response rather than setting cookies\r\n          return {\r\n            success: true,\r\n            message: \"Login successful!\",\r\n            redirect: \"/dashboard\",\r\n            tokens: {\r\n              access_token: data.access_token,\r\n              refresh_token: data.refresh_token\r\n            }\r\n          };\r\n        } else {\r\n          return {\r\n            success: false,\r\n            message: \"Invalid response from server. Missing authentication tokens.\",\r\n          };\r\n        }\r\n      } catch (jsonError) {\r\n        return {\r\n          success: false,\r\n          message: \"Failed to process login response.\",\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Login error:\", error);\r\n      return {\r\n        success: false,\r\n        message: \"An unexpected error occurred. Please try again later.\"\r\n      };\r\n    }\r\n  }\r\n \r\n  // Add a refresh token function for later use\r\n  export async function refreshAccessToken(): Promise<{\r\n    success: boolean;\r\n    newAccessToken?: string;\r\n  }> {\r\n   \r\n    // Get the refresh token from cookies\r\n    const refreshToken = (await cookies()).get(\"refresh_token\")?.value;\r\n \r\n   \r\n    if (!refreshToken) {\r\n      // Without a refresh token, we can't refresh the session\r\n      return { success: false };\r\n    }\r\n \r\n    try {\r\n      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"Authorization\": `Bearer ${refreshToken}`,\r\n        },\r\n        body: JSON.stringify({ refreshToken }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      if (!response.ok) {\r\n        // If the refresh token is invalid or expired, user needs to log in again\r\n        return { success: false };\r\n      }\r\n \r\n      const data = await response.json();\r\n     \r\n      if (data.access_token) {\r\n        // Update the access token in cookies\r\n        (await\r\n          // Update the access token in cookies\r\n          cookies()).set(\"access_token\", data.access_token, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          path: \"/\",\r\n          sameSite: \"lax\",\r\n          maxAge: 60 * 60, // 1 hour\r\n        });\r\n \r\n        return {\r\n          success: true,\r\n          newAccessToken: data.access_token,\r\n        };\r\n      }\r\n     \r\n      return { success: false };\r\n    } catch (error) {\r\n      console.error(\"Token refresh error:\", error);\r\n      return { success: false };\r\n    }\r\n  }\r\n \r\n// Function to get current user information\r\nexport async function getCurrentUser(): Promise<{\r\n  success: boolean;\r\n  user?: UserInfo;\r\n  error?: string;\r\n}> {\r\n  let accessToken;\r\n \r\n  if (typeof window !== 'undefined') {\r\n    // We're in a browser\r\n    accessToken = localStorage.getItem('access_token');\r\n  } else {\r\n    // We're on the server\r\n    accessToken = (await cookies()).get(\"access_token\")?.value;\r\n  }\r\n \r\n  if (!accessToken) {\r\n    console.log(\"No access token found\");\r\n    return {\r\n      success: false,\r\n      error: \"Not authenticated\"\r\n    };\r\n  }\r\n \r\n  try {\r\n \r\n   \r\n    const response = await fetch(`${API_BASE_URL}/api/auth/me`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Authorization\": `Bearer ${accessToken}`,\r\n      },\r\n      cache: \"no-store\",\r\n    });\r\n \r\n \r\n    if (!response.ok) {\r\n      // If response is 401 or 403, try to refresh the token\r\n      if (response.status === 401 || response.status === 403) {\r\n        // Attempt to refresh the token\r\n        const refreshResult = await refreshAccessToken();\r\n       \r\n        if (refreshResult.success) {\r\n          // Retry with new token\r\n          const retryResponse = await fetch(`${API_BASE_URL}/api/auth/me`, {\r\n            method: \"GET\",\r\n            headers: {\r\n              \"Authorization\": `Bearer ${refreshResult.newAccessToken}`,\r\n            },\r\n            cache: \"no-store\",\r\n          });\r\n \r\n          if (retryResponse.ok) {\r\n            const userData = await retryResponse.json();\r\n           \r\n            // Check if we got actual user data back (not empty object)\r\n            if (userData && userData.userId && userData.email) {\r\n              return {\r\n                success: true,\r\n                user: userData\r\n              };\r\n            } else {\r\n              return {\r\n                success: false,\r\n                error: \"Account not authorized or pending approval\"\r\n              };\r\n            }\r\n          }\r\n        }\r\n       \r\n        // If refresh failed or retry failed\r\n        return {\r\n          success: false,\r\n          error: \"Not authorized\"\r\n        };\r\n      }\r\n     \r\n      // For other errors\r\n      return {\r\n        success: false,\r\n        error: `Error: ${response.status}`\r\n      };\r\n    }\r\n \r\n    // If first attempt was successful\r\n    const userData = await response.json();\r\n    // This confirms the user has proper approval, not just valid tokens\r\n    if (userData && userData.userId && userData.email) {\r\n      return {\r\n        success: true,\r\n        user: userData\r\n      };\r\n    } else {\r\n      // User has valid tokens but no data - likely pending approval\r\n      return {\r\n        success: false,\r\n        error: \"Account not authorized or pending approval\"\r\n      };\r\n    }\r\n \r\n  } catch (error) {\r\n    console.error(\"Error fetching user data:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An error occurred while fetching user data\"\r\n    };\r\n  }\r\n}\r\n \r\nexport async function logoutUser(): Promise<ActionResult> {\r\n  try {\r\n    // Clear the cookies\r\n    const cookieStore = await cookies();\r\n    cookieStore.delete(\"access_token\");\r\n    cookieStore.delete(\"refresh_token\");\r\n   \r\n    return {\r\n      success: true,\r\n      message: \"Logged out successfully\",\r\n      redirect: \"/\"\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Logout error:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"An error occurred during logout\"\r\n    };\r\n  }\r\n}"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;;;;IA4B9B;IA0EA;IAwFE;IA0DF;IA4GA", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ProfileMenu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { LogOut, Settings, User } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useState } from \"react\";\r\nimport { logoutUser } from \"@/app/(auth)/actions/auth\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ntype ProfileMenuProps = {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n    fullName: string;\r\n  };\r\n};\r\n\r\nexport function ProfileMenu({ user }: ProfileMenuProps) {\r\n\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const router = useRouter();\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      setIsLoggingOut(true);\r\n      const result = await logoutUser();\r\n\r\n      // Always clear localStorage on logout\r\n      localStorage.removeItem('access_token');\r\n      localStorage.removeItem('refresh_token');\r\n      localStorage.removeItem('user_data');\r\n\r\n      if (result.success) {\r\n        // Navigate to login page after successful logout\r\n        router.push(\"/login\");\r\n      } else {\r\n        console.error(\"Logout failed:\", result.message);\r\n        // Still redirect to login\r\n        router.push(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error during logout:\", error);\r\n      // Clear localStorage as fallback\r\n      localStorage.removeItem('access_token');\r\n      localStorage.removeItem('refresh_token');\r\n      localStorage.removeItem('user_data');\r\n      router.push(\"/login\");\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"ghost\" className=\"relative h-9 w-9 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700\">\r\n          <Avatar className=\"h-9 w-9\">\r\n            <AvatarImage src={user.avatar} alt={user.name} />\r\n            <AvatarFallback>{user.name[0].toUpperCase()} </AvatarFallback>\r\n          </Avatar>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"w-56 z-600\" align=\"end\" forceMount>\r\n        <DropdownMenuLabel className=\"font-normal\">\r\n          <div className=\"flex flex-col space-y-1\">\r\n            <p className=\"text-sm font-medium leading-none dark:text-gray-100\">{user.fullName}</p>\r\n            <p className=\"text-xs leading-none text-gray-500 dark:text-gray-400\">{user.email}</p>\r\n          </div>\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator />\r\n        <DropdownMenuGroup>\r\n        <Link href=\"/profile\">\r\n          <DropdownMenuItem className=\"cursor-pointer\">\r\n            <User className=\"mr-2 h-4 w-4\" />\r\n            <span>Profile</span>\r\n          </DropdownMenuItem>\r\n        </Link>\r\n          <Link href=\"/settings\">\r\n          <DropdownMenuItem className=\"cursor-pointer\">\r\n            <Settings className=\"mr-2 h-4 w-4\" />\r\n            <span>Settings</span>\r\n          </DropdownMenuItem>\r\n        </Link>\r\n        </DropdownMenuGroup>\r\n        <DropdownMenuSeparator />\r\n        <DropdownMenuItem\r\n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\r\n          onClick={handleLogout}\r\n          disabled={isLoggingOut}\r\n        >\r\n          <LogOut className=\"mr-2 h-4 w-4\" />\r\n          <span>{isLoggingOut ? \"Logging out...\" : \"Logout\"}</span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AASA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;AA4BO,SAAS,YAAY,EAAE,IAAI,EAAoB;IAEpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,gBAAgB;YAChB,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD;YAE9B,sCAAsC;YACtC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YAExB,IAAI,OAAO,OAAO,EAAE;gBAClB,iDAAiD;gBACjD,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,QAAQ,KAAK,CAAC,kBAAkB,OAAO,OAAO;gBAC9C,0BAA0B;gBAC1B,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,iCAAiC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,WAAU;8BAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,kIAAA,CAAA,cAAW;gCAAC,KAAK,KAAK,MAAM;gCAAE,KAAK,KAAK,IAAI;;;;;;0CAC7C,8OAAC,kIAAA,CAAA,iBAAc;;oCAAE,KAAK,IAAI,CAAC,EAAE,CAAC,WAAW;oCAAG;;;;;;;;;;;;;;;;;;;;;;;0BAIlD,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAa,OAAM;gBAAM,UAAU;;kCAChE,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAuD,KAAK,QAAQ;;;;;;8CACjF,8OAAC;oCAAE,WAAU;8CAAyD,KAAK,KAAK;;;;;;;;;;;;;;;;;kCAGpF,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,oBAAiB;;0CAClB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGR,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACX,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAIV,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;wBACT,UAAU;;0CAEV,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;AAKnD", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/animations/FadeIn.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { ReactNode } from \"react\";\r\n\r\ninterface FadeInProps {\r\n  children: ReactNode;\r\n  duration?: number;\r\n  delay?: number;\r\n  className?: string;\r\n  direction?: \"up\" | \"down\" | \"left\" | \"right\" | \"none\";\r\n  distance?: number;\r\n  once?: boolean;\r\n  viewOffset?: number;\r\n}\r\n\r\nexport default function FadeIn({\r\n  children,\r\n  duration = 0.5,\r\n  delay = 0,\r\n  direction = \"up\",\r\n  distance = 30,\r\n  className = \"\",\r\n  once = true,\r\n  viewOffset = 0.1,\r\n}: FadeInProps) {\r\n  let initialY = 0;\r\n  let initialX = 0;\r\n\r\n  // Set initial position based on direction\r\n  if (direction === \"up\") initialY = distance;\r\n  if (direction === \"down\") initialY = -distance;\r\n  if (direction === \"left\") initialX = distance;\r\n  if (direction === \"right\") initialX = -distance;\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{\r\n        y: initialY,\r\n        x: initialX,\r\n        opacity: 0,\r\n      }}\r\n      whileInView={{\r\n        y: 0,\r\n        x: 0,\r\n        opacity: 1,\r\n      }}\r\n      transition={{\r\n        duration: duration,\r\n        delay: delay,\r\n        ease: \"easeOut\",\r\n      }}\r\n      viewport={{\r\n        once,\r\n        amount: viewOffset,\r\n      }}\r\n      className={className}\r\n    >\r\n      {children}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBe,SAAS,OAAO,EAC7B,QAAQ,EACR,WAAW,GAAG,EACd,QAAQ,CAAC,EACT,YAAY,IAAI,EAChB,WAAW,EAAE,EACb,YAAY,EAAE,EACd,OAAO,IAAI,EACX,aAAa,GAAG,EACJ;IACZ,IAAI,WAAW;IACf,IAAI,WAAW;IAEf,0CAA0C;IAC1C,IAAI,cAAc,MAAM,WAAW;IACnC,IAAI,cAAc,QAAQ,WAAW,CAAC;IACtC,IAAI,cAAc,QAAQ,WAAW;IACrC,IAAI,cAAc,SAAS,WAAW,CAAC;IAEvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,GAAG;YACH,GAAG;YACH,SAAS;QACX;QACA,aAAa;YACX,GAAG;YACH,GAAG;YACH,SAAS;QACX;QACA,YAAY;YACV,UAAU;YACV,OAAO;YACP,MAAM;QACR;QACA,UAAU;YACR;YACA,QAAQ;QACV;QACA,WAAW;kBAEV;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/lib/auth-client.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { authFetch } from \"./authFetch\";\r\n\r\nexport type UserInfo = {\r\n  fullName: string;\r\n  userId: string;\r\n  email: string;\r\n  role: string;\r\n};\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n\r\n// Client-side version of getCurrentUser\r\nexport async function getCurrentUser(): Promise<{\r\n  success: boolean;\r\n  user?: UserInfo;\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Use the authFetch utility instead of regular fetch\r\n    const response = await authFetch(`${API_BASE_URL}/api/auth/me`, {\r\n      method: \"GET\"\r\n    });\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        error: `Error: ${response.status}`\r\n      };\r\n    }\r\n\r\n    const userData = await response.json();\r\n\r\n    // More flexible check - look for any ID field\r\n    const userId = userData.userId || userData._id || userData.id;\r\n    const email = userData.email;\r\n\r\n    if (userId && email) {\r\n      // Normalize the user object to match expected structure\r\n      return {\r\n        success: true,\r\n        user: {\r\n          fullName: userData.fullName || email.split('@')[0],\r\n          userId: userId,\r\n          email: email,\r\n          role: userData.role || \"user\"\r\n        }\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Invalid user data received\"\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching user data:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An error occurred while fetching user data\"\r\n    };\r\n  }\r\n}\r\n\r\nexport function setupTokenRefresh(intervalMinutes = 10): () => void {\r\n  if (typeof window === 'undefined') return () => {};\r\n\r\n  const intervalId = setInterval(async () => {\r\n    // Only refresh if we're logged in\r\n    const accessToken = localStorage.getItem('access_token');\r\n    if (accessToken) {\r\n      try {\r\n        await refreshAccessToken();\r\n      } catch (error) {\r\n        console.error('Background token refresh failed:', error);\r\n      }\r\n    }\r\n  }, intervalMinutes * 60 * 1000);\r\n\r\n  // Return cleanup function\r\n  return () => clearInterval(intervalId);\r\n}\r\n\r\n// Client-side version of refreshAccessToken\r\nexport async function refreshAccessToken(): Promise<{\r\n  success: boolean;\r\n  newAccessToken?: string;\r\n}> {\r\n  const refreshToken = localStorage.getItem('refresh_token');\r\n\r\n  if (!refreshToken) {\r\n    return { success: false };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ refreshToken }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      return { success: false };\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (data.access_token) {\r\n      localStorage.setItem('access_token', data.access_token);\r\n      return {\r\n        success: true,\r\n        newAccessToken: data.access_token,\r\n      };\r\n    }\r\n\r\n    return { success: false };\r\n  } catch (error) {\r\n    console.error(\"Token refresh error:\", error);\r\n    return { success: false };\r\n  }\r\n}\r\n\r\n// Client-side logout function\r\nexport function logoutClient(): void {\r\n  localStorage.removeItem('access_token');\r\n  localStorage.removeItem('refresh_token');\r\n  localStorage.removeItem('user_data');\r\n}"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;AAWA,MAAM,eAAe,6DAAsC;AAGpD,eAAe;IAKpB,IAAI;QACF,qDAAqD;QACrD,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,GAAG,aAAa,YAAY,CAAC,EAAE;YAC9D,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,OAAO,EAAE,SAAS,MAAM,EAAE;YACpC;QACF;QAEA,MAAM,WAAW,MAAM,SAAS,IAAI;QAEpC,8CAA8C;QAC9C,MAAM,SAAS,SAAS,MAAM,IAAI,SAAS,GAAG,IAAI,SAAS,EAAE;QAC7D,MAAM,QAAQ,SAAS,KAAK;QAE5B,IAAI,UAAU,OAAO;YACnB,wDAAwD;YACxD,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,UAAU,SAAS,QAAQ,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;oBAClD,QAAQ;oBACR,OAAO;oBACP,MAAM,SAAS,IAAI,IAAI;gBACzB;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEO,SAAS,kBAAkB,kBAAkB,EAAE;IACpD,wCAAmC,OAAO,KAAO;;IAEjD,MAAM;AAcR;AAGO,eAAe;IAIpB,MAAM,eAAe,aAAa,OAAO,CAAC;IAE1C,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;QAAM;IAC1B;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC,EAAE;YAC/D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAa;QACtC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBAAE,SAAS;YAAM;QAC1B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,YAAY,EAAE;YACrB,aAAa,OAAO,CAAC,gBAAgB,KAAK,YAAY;YACtD,OAAO;gBACL,SAAS;gBACT,gBAAgB,KAAK,YAAY;YACnC;QACF;QAEA,OAAO;YAAE,SAAS;QAAM;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YAAE,SAAS;QAAM;IAC1B;AACF;AAGO,SAAS;IACd,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/lib/authFetch.ts"], "sourcesContent": ["// Add this new function to your auth-client.ts file\r\n\r\nimport { refreshAccessToken } from \"./auth-client\";\r\n\r\n/**\r\n * Authenticated fetch utility that handles token refresh automatically\r\n * @param url The URL to fetch\r\n * @param options Fetch options\r\n * @returns Response from the fetch call\r\n */\r\nexport async function authFetch(url: string, options: RequestInit = {}): Promise<Response> {\r\n    let accessToken = localStorage.getItem('access_token');\r\n    \r\n    // If no access token is available, try to refresh\r\n    if (!accessToken) {\r\n      const refreshResult = await refreshAccessToken();\r\n      if (!refreshResult.success) {\r\n        throw new Error('No authentication token available');\r\n      }\r\n      accessToken = refreshResult.newAccessToken as string;\r\n    }\r\n  \r\n    // Add authorization header if not already present\r\n    const headers = new Headers(options.headers || {});\r\n    if (!headers.has('Authorization')) {\r\n      headers.set('Authorization', `Bearer ${accessToken}`);\r\n    }\r\n  \r\n    // Make the request with the current token\r\n    const response = await fetch(url, {\r\n      ...options,\r\n      headers\r\n    });\r\n  \r\n    // If unauthorized or forbidden, try to refresh the token and retry the request\r\n    if (response.status === 401 || response.status === 403) {\r\n      console.log('Token expired, attempting refresh...');\r\n      const refreshResult = await refreshAccessToken();\r\n      \r\n      if (!refreshResult.success) {\r\n        console.error('Token refresh failed');\r\n        // Handle auth failure - redirect to login or show message\r\n        if (typeof window !== 'undefined') {\r\n          // Only redirect in browser\r\n          window.location.href = '/login';\r\n        }\r\n        throw new Error('Authentication failed');\r\n      }\r\n      \r\n      // Retry the request with the new token\r\n      console.log('Token refreshed, retrying request...');\r\n      const newHeaders = new Headers(options.headers || {});\r\n      newHeaders.set('Authorization', `Bearer ${refreshResult.newAccessToken as string}`);\r\n      \r\n      return fetch(url, {\r\n        ...options,\r\n        headers: newHeaders\r\n      });\r\n    }\r\n    \r\n    return response;\r\n  }"], "names": [], "mappings": "AAAA,oDAAoD;;;;AAEpD;;AAQO,eAAe,UAAU,GAAW,EAAE,UAAuB,CAAC,CAAC;IAClE,IAAI,cAAc,aAAa,OAAO,CAAC;IAEvC,kDAAkD;IAClD,IAAI,CAAC,aAAa;QAChB,MAAM,gBAAgB,MAAM,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD;QAC7C,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,MAAM,IAAI,MAAM;QAClB;QACA,cAAc,cAAc,cAAc;IAC5C;IAEA,kDAAkD;IAClD,MAAM,UAAU,IAAI,QAAQ,QAAQ,OAAO,IAAI,CAAC;IAChD,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB;QACjC,QAAQ,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,aAAa;IACtD;IAEA,0CAA0C;IAC1C,MAAM,WAAW,MAAM,MAAM,KAAK;QAChC,GAAG,OAAO;QACV;IACF;IAEA,+EAA+E;IAC/E,IAAI,SAAS,MAAM,KAAK,OAAO,SAAS,MAAM,KAAK,KAAK;QACtD,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,MAAM,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD;QAE7C,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,QAAQ,KAAK,CAAC;YACd,0DAA0D;YAC1D,uCAAmC;;YAGnC;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,uCAAuC;QACvC,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,IAAI,QAAQ,QAAQ,OAAO,IAAI,CAAC;QACnD,WAAW,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,cAAc,EAAY;QAElF,OAAO,MAAM,KAAK;YAChB,GAAG,OAAO;YACV,SAAS;QACX;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/animations/ScaleIn.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { ReactNode } from \"react\";\r\n\r\ninterface ScaleInProps {\r\n  children: ReactNode;\r\n  duration?: number;\r\n  delay?: number;\r\n  className?: string;\r\n  initialScale?: number;\r\n  once?: boolean;\r\n  viewOffset?: number;\r\n}\r\n\r\nexport default function ScaleIn({\r\n  children,\r\n  duration = 0.5,\r\n  delay = 0,\r\n  initialScale = 0.9,\r\n  className = \"\",\r\n  once = true,\r\n  viewOffset = 0.1,\r\n}: ScaleInProps) {\r\n  return (\r\n    <motion.div\r\n      initial={{\r\n        scale: initialScale,\r\n        opacity: 0,\r\n      }}\r\n      whileInView={{\r\n        scale: 1,\r\n        opacity: 1,\r\n      }}\r\n      transition={{\r\n        duration: duration,\r\n        delay: delay,\r\n        ease: \"easeOut\",\r\n      }}\r\n      viewport={{ \r\n        once, \r\n        amount: viewOffset \r\n      }}\r\n      className={className}\r\n    >\r\n      {children}\r\n    </motion.div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAee,SAAS,QAAQ,EAC9B,QAAQ,EACR,WAAW,GAAG,EACd,QAAQ,CAAC,EACT,eAAe,GAAG,EAClB,YAAY,EAAE,EACd,OAAO,IAAI,EACX,aAAa,GAAG,EACH;IACb,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,aAAa;YACX,OAAO;YACP,SAAS;QACX;QACA,YAAY;YACV,UAAU;YACV,OAAO;YACP,MAAM;QACR;QACA,UAAU;YACR;YACA,QAAQ;QACV;QACA,WAAW;kBAEV;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 993, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/users.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n\r\nexport type User = {\r\n  _id: string;\r\n  email: string;\r\n  fullName: string;\r\n  isApproved: boolean;\r\n  organizationId?: string;\r\n  credits?: number;\r\n  autoRechargeEnabled?: boolean;\r\n  autoRechargeThreshold?: number;\r\n  autoRechargeAmount?: number;\r\n  createdAt: string;\r\n  role?: string;\r\n};\r\n\r\nexport type NewUser = {\r\n  fullName: string;\r\n  email: string;\r\n  password: string;\r\n};\r\n\r\nexport type UserUpdateData = {\r\n  fullName?: string;\r\n  email?: string;\r\n  password?: string;\r\n  isApproved?: boolean;\r\n  credits?: number;\r\n  organizationId?: string;\r\n};\r\n\r\nexport type AutoRechargeSettings = {\r\n  autoRechargeEnabled: boolean;\r\n  autoRechargeThreshold: number;\r\n  autoRechargeAmount: number;\r\n};\r\n\r\n/**\r\n * Fetch all users\r\n */\r\nexport async function fetchUsers(): Promise<User[]> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users`, {\r\n      headers: {\r\n        \"Authorization\": `Bearer ${token}`\r\n      }\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to fetch users');\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error('Error fetching users:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get all users (simplified function for components)\r\n */\r\nexport const getUsers = async (): Promise<User[]> => {\r\n  const token = localStorage.getItem('access_token');\r\n\r\n  if (!token) {\r\n    throw new Error(\"No access token available\");\r\n  }\r\n\r\n  const response = await fetch(`${API_BASE_URL}/api/users`, {\r\n    headers: {\r\n      Authorization: `Bearer ${token}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch users');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\n/**\r\n * Create a new user\r\n */\r\nexport async function addUser(userData: NewUser): Promise<void> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/register`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify(userData),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(errorText || 'Failed to add user');\r\n    }\r\n\r\n    return;\r\n  } catch (error) {\r\n    console.error('Error adding user:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Update existing user\r\n */\r\nexport async function updateUser(userId: string, updateData: UserUpdateData): Promise<User> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`\r\n      },\r\n      body: JSON.stringify(updateData),\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to update user');\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error updating user:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Delete a user\r\n */\r\nexport async function deleteUser(userId: string): Promise<void> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {\r\n      method: 'DELETE',\r\n      headers: {\r\n        'Authorization': `Bearer ${token}`\r\n      }\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to delete user');\r\n  } catch (error) {\r\n    console.error('Error deleting user:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Approve a user\r\n */\r\nexport async function approveUser(userId: string): Promise<User> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/approve/${userId}`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        'Authorization': `Bearer ${token}`\r\n      }\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to approve user');\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error approving user:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Revoke user access\r\n */\r\nexport async function revokeUserAccess(userId: string): Promise<User> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`\r\n      },\r\n      body: JSON.stringify({ isApproved: false }),\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to revoke user access');\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error revoking user access:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * User credits response interface\r\n */\r\nexport interface UserCreditsResponse {\r\n  // Legacy fields for backward compatibility\r\n  credits: number;\r\n  minutes: number;\r\n  // New monthly credits fields\r\n  freeCreditsRemaining: number;\r\n  paidCredits: number;\r\n  totalAvailable: number;\r\n  usingFreeCredits: boolean;\r\n  freeMinutesRemaining: number;\r\n  paidMinutes: number;\r\n  totalMinutesAvailable: number;\r\n  // Common fields\r\n  callPricePerMinute: number;\r\n  monthlyResetDate: number;\r\n  monthlyAllowance: number;\r\n  minimumCreditsThreshold: number;\r\n}\r\n\r\n/**\r\n * Get current user credits\r\n */\r\nexport async function getUserCredits(): Promise<UserCreditsResponse> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/me/credits`, {\r\n      headers: {\r\n        \"Authorization\": `Bearer ${token}`\r\n      }\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to fetch user credits');\r\n    const data = await response.json();\r\n    return {\r\n      // Legacy fields for backward compatibility\r\n      credits: data.credits || 0,\r\n      minutes: data.minutes || 0,\r\n      // New monthly credits fields\r\n      freeCreditsRemaining: data.freeCreditsRemaining || 0,\r\n      paidCredits: data.paidCredits || 0,\r\n      totalAvailable: data.totalAvailable || 0,\r\n      usingFreeCredits: data.usingFreeCredits || false,\r\n      freeMinutesRemaining: data.freeMinutesRemaining || 0,\r\n      paidMinutes: data.paidMinutes || 0,\r\n      totalMinutesAvailable: data.totalMinutesAvailable || 0,\r\n      // Common fields\r\n      callPricePerMinute: data.callPricePerMinute || 0.1,\r\n      monthlyResetDate: data.monthlyResetDate || 1,\r\n      monthlyAllowance: data.monthlyAllowance || 0,\r\n      minimumCreditsThreshold: data.minimumCreditsThreshold || 1.0\r\n    };\r\n  } catch (error) {\r\n    console.error('Error fetching user credits:', error);\r\n    return {\r\n      // Legacy fields for backward compatibility\r\n      credits: 0,\r\n      minutes: 0,\r\n      // New monthly credits fields\r\n      freeCreditsRemaining: 0,\r\n      paidCredits: 0,\r\n      totalAvailable: 0,\r\n      usingFreeCredits: false,\r\n      freeMinutesRemaining: 0,\r\n      paidMinutes: 0,\r\n      totalMinutesAvailable: 0,\r\n      // Common fields\r\n      callPricePerMinute: 0.1,\r\n      monthlyResetDate: 1,\r\n      monthlyAllowance: 0,\r\n      minimumCreditsThreshold: 1.0\r\n    }; // Return defaults if there's an error\r\n  }\r\n}\r\n\r\n/**\r\n * Update user credits\r\n */\r\nexport async function updateUserCredits(amount: number): Promise<number> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/me/credits`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`\r\n      },\r\n      body: JSON.stringify({ amount }),\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to update user credits');\r\n\r\n    const data = await response.json();\r\n    return data.credits;\r\n  } catch (error) {\r\n    console.error('Error updating user credits:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get auto-recharge settings\r\n */\r\nexport async function getAutoRechargeSettings(): Promise<AutoRechargeSettings> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/me/auto-recharge`, {\r\n      headers: {\r\n        \"Authorization\": `Bearer ${token}`\r\n      }\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to fetch auto-recharge settings');\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error fetching auto-recharge settings:', error);\r\n    // Return default settings if there's an error\r\n    return {\r\n      autoRechargeEnabled: false,\r\n      autoRechargeThreshold: 1.0,\r\n      autoRechargeAmount: 0\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update auto-recharge settings\r\n */\r\nexport async function updateAutoRechargeSettings(settings: Partial<AutoRechargeSettings>): Promise<AutoRechargeSettings> {\r\n  try {\r\n    // Get token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n\r\n    if (!token) {\r\n      console.error(\"No access token available\");\r\n      throw new Error(\"No access token available\");\r\n    }\r\n\r\n    const response = await fetch(`${API_BASE_URL}/api/users/me/auto-recharge`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`\r\n      },\r\n      body: JSON.stringify(settings),\r\n    });\r\n\r\n    if (!response.ok) throw new Error('Failed to update auto-recharge settings');\r\n\r\n    const data = await response.json();\r\n    return data.settings;\r\n  } catch (error) {\r\n    console.error('Error updating auto-recharge settings:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,MAAM,eAAe,6DAAsC;AAwCpD,eAAe;IACpB,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACxD,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAClC,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAKO,MAAM,WAAW;IACtB,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;QACxD,SAAS;YACP,eAAe,CAAC,OAAO,EAAE,OAAO;QAClC;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,eAAe,QAAQ,QAAiB;IAC7C,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,mBAAmB,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,aAAa;QAC/B;QAEA;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM;IACR;AACF;AAKO,eAAe,WAAW,MAAc,EAAE,UAA0B;IACzE,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,QAAQ,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAKO,eAAe,WAAW,MAAc;IAC7C,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,QAAQ,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAKO,eAAe,YAAY,MAAc;IAC9C,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,mBAAmB,EAAE,QAAQ,EAAE;YAC1E,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAKO,eAAe,iBAAiB,MAAc;IACnD,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,QAAQ,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,YAAY;YAAM;QAC3C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AA2BO,eAAe;IACpB,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,CAAC,EAAE;YACnE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAClC,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;YACL,2CAA2C;YAC3C,SAAS,KAAK,OAAO,IAAI;YACzB,SAAS,KAAK,OAAO,IAAI;YACzB,6BAA6B;YAC7B,sBAAsB,KAAK,oBAAoB,IAAI;YACnD,aAAa,KAAK,WAAW,IAAI;YACjC,gBAAgB,KAAK,cAAc,IAAI;YACvC,kBAAkB,KAAK,gBAAgB,IAAI;YAC3C,sBAAsB,KAAK,oBAAoB,IAAI;YACnD,aAAa,KAAK,WAAW,IAAI;YACjC,uBAAuB,KAAK,qBAAqB,IAAI;YACrD,gBAAgB;YAChB,oBAAoB,KAAK,kBAAkB,IAAI;YAC/C,kBAAkB,KAAK,gBAAgB,IAAI;YAC3C,kBAAkB,KAAK,gBAAgB,IAAI;YAC3C,yBAAyB,KAAK,uBAAuB,IAAI;QAC3D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,2CAA2C;YAC3C,SAAS;YACT,SAAS;YACT,6BAA6B;YAC7B,sBAAsB;YACtB,aAAa;YACb,gBAAgB;YAChB,kBAAkB;YAClB,sBAAsB;YACtB,aAAa;YACb,uBAAuB;YACvB,gBAAgB;YAChB,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,yBAAyB;QAC3B,GAAG,sCAAsC;IAC3C;AACF;AAKO,eAAe,kBAAkB,MAAc;IACpD,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAO;QAChC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO;IACrB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAKO,eAAe;IACpB,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,2BAA2B,CAAC,EAAE;YACzE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAClC,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,8CAA8C;QAC9C,OAAO;YACL,qBAAqB;YACrB,uBAAuB;YACvB,oBAAoB;QACtB;IACF;AACF;AAKO,eAAe,2BAA2B,QAAuC;IACtF,IAAI;QACF,8BAA8B;QAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,2BAA2B,CAAC,EAAE;YACzE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,QAAQ;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/hooks/useWebSocket.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\n\"use client\";\r\n\r\nimport { useEffect, useState, useRef, useCallback } from 'react';\r\nimport { io, Socket } from 'socket.io-client';\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';\r\n\r\nexport function useWebSocket(namespace: string = '') {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const socketRef = useRef<Socket | null>(null);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    // Create socket connection\r\n    try {\r\n      const socketUrl = `${API_BASE_URL}${namespace ? `/${namespace}` : ''}`;\r\n      console.log(`Connecting to WebSocket at: ${socketUrl}`);\r\n\r\n      // Disconnect existing socket if any\r\n      if (socketRef.current) {\r\n        console.log('Disconnecting existing socket before creating a new one');\r\n        socketRef.current.disconnect();\r\n      }\r\n\r\n      const socket = io(socketUrl, {\r\n        transports: ['websocket', 'polling'], // Add polling as fallback\r\n        autoConnect: true,\r\n        reconnection: true,\r\n        reconnectionAttempts: 10,\r\n        reconnectionDelay: 1000,\r\n        timeout: 20000,\r\n        forceNew: true, // Force a new connection\r\n        path: '/socket.io/',\r\n      });\r\n\r\n      // Set up event listeners\r\n      socket.on('connect', () => {\r\n        console.log(`WebSocket connected to ${namespace || 'default'} namespace`);\r\n        setIsConnected(true);\r\n        setError(null);\r\n      });\r\n\r\n      socket.on('disconnect', (reason) => {\r\n        console.log(`WebSocket disconnected from ${namespace || 'default'} namespace: ${reason}`);\r\n        setIsConnected(false);\r\n      });\r\n\r\n      socket.on('connect_error', (err) => {\r\n        console.error(`WebSocket connection error: ${err.message}`);\r\n        setError(`Connection error: ${err.message}`);\r\n\r\n        // Try to switch to polling if websocket fails\r\n        try {\r\n          if (socket.io?.opts?.transports?.[0] === 'websocket') {\r\n            console.log('Switching to polling transport due to connection error');\r\n            socket.io.opts.transports = ['polling', 'websocket'];\r\n          }\r\n        } catch (e) {\r\n          console.error('Error switching transports:', e);\r\n        }\r\n\r\n        // Try to reconnect manually after a delay\r\n        setTimeout(() => {\r\n          console.log('Attempting to reconnect manually...');\r\n          socket.connect();\r\n        }, 3000);\r\n      });\r\n\r\n      // Add a ping/pong mechanism to keep the connection alive\r\n      const pingInterval = setInterval(() => {\r\n        if (socket.connected) {\r\n          console.log('Sending ping to server');\r\n          socket.emit('ping', {}, (response: any) => {\r\n            console.log('Received pong from server:', response);\r\n          });\r\n        }\r\n      }, 30000); // Every 30 seconds\r\n\r\n      // Store socket in ref\r\n      socketRef.current = socket;\r\n\r\n      // Clean up on unmount\r\n      return () => {\r\n        clearInterval(pingInterval);\r\n        if (socket) {\r\n          console.log(`Disconnecting from WebSocket at: ${socketUrl}`);\r\n          socket.disconnect();\r\n          socketRef.current = null;\r\n        }\r\n      };\r\n    } catch (error) {\r\n      console.error('Error setting up WebSocket connection:', error);\r\n      // setError(`WebSocket setup error: ${error.message}`);\r\n      return () => {};\r\n    }\r\n  }, [namespace]);\r\n\r\n  // Function to emit events\r\n  const emit = useCallback((event: string, data: any, callback?: (response: any) => void) => {\r\n    if (socketRef.current && isConnected) {\r\n      if (callback) {\r\n        socketRef.current.emit(event, data, callback);\r\n      } else {\r\n        socketRef.current.emit(event, data);\r\n      }\r\n      return true;\r\n    }\r\n    return false;\r\n  }, [isConnected]);\r\n\r\n  // Function to subscribe to events\r\n  const on = useCallback((event: string, callback: (...args: any[]) => void) => {\r\n    if (socketRef.current) {\r\n      socketRef.current.on(event, callback);\r\n      return () => {\r\n        socketRef.current?.off(event, callback);\r\n      };\r\n    }\r\n    return () => {};\r\n  }, []);\r\n\r\n  // Function to register user with socket\r\n  const registerUser = useCallback((userId: string) => {\r\n    if (!userId) {\r\n      console.error('Cannot register user: userId is empty');\r\n      return false;\r\n    }\r\n\r\n    if (socketRef.current && isConnected) {\r\n      console.log(`Registering user ${userId} with socket ${socketRef.current.id}`);\r\n      socketRef.current.emit('registerUser', { userId }, (response: any) => {\r\n        if (response && response.success) {\r\n          console.log(`User ${userId} successfully registered with socket: ${response.message}`);\r\n        } else {\r\n          console.error(`Failed to register user ${userId} with socket:`, response);\r\n        }\r\n      });\r\n      return true;\r\n    } else {\r\n      console.warn(`Cannot register user ${userId}: socket is ${socketRef.current ? 'created' : 'not created'} and connection is ${isConnected ? 'active' : 'inactive'}`);\r\n\r\n      // If socket exists but not connected, try to connect\r\n      if (socketRef.current && !isConnected) {\r\n        console.log('Attempting to connect socket before registering user');\r\n        socketRef.current.connect();\r\n\r\n        // Schedule another registration attempt after connection\r\n        setTimeout(() => {\r\n          if (socketRef.current && socketRef.current.connected) {\r\n            console.log(`Retrying registration for user ${userId} after connection`);\r\n            socketRef.current.emit('registerUser', { userId });\r\n          }\r\n        }, 1000);\r\n      }\r\n\r\n      return false;\r\n    }\r\n  }, [isConnected]);\r\n\r\n  return { isConnected, error, emit, on, registerUser, socket: socketRef.current };\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;AAGrD;AACA;AAAA;AAHA;;;AAKA,MAAM,eAAe,6DAAsC;AAEpD,SAAS,aAAa,YAAoB,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2BAA2B;QAC3B,IAAI;YACF,MAAM,YAAY,GAAG,eAAe,YAAY,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;YACtE,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,WAAW;YAEtD,oCAAoC;YACpC,IAAI,UAAU,OAAO,EAAE;gBACrB,QAAQ,GAAG,CAAC;gBACZ,UAAU,OAAO,CAAC,UAAU;YAC9B;YAEA,MAAM,SAAS,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,WAAW;gBAC3B,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,aAAa;gBACb,cAAc;gBACd,sBAAsB;gBACtB,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;YACR;YAEA,yBAAyB;YACzB,OAAO,EAAE,CAAC,WAAW;gBACnB,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,aAAa,UAAU,UAAU,CAAC;gBACxE,eAAe;gBACf,SAAS;YACX;YAEA,OAAO,EAAE,CAAC,cAAc,CAAC;gBACvB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,aAAa,UAAU,YAAY,EAAE,QAAQ;gBACxF,eAAe;YACjB;YAEA,OAAO,EAAE,CAAC,iBAAiB,CAAC;gBAC1B,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,IAAI,OAAO,EAAE;gBAC1D,SAAS,CAAC,kBAAkB,EAAE,IAAI,OAAO,EAAE;gBAE3C,8CAA8C;gBAC9C,IAAI;oBACF,IAAI,OAAO,EAAE,EAAE,MAAM,YAAY,CAAC,EAAE,KAAK,aAAa;wBACpD,QAAQ,GAAG,CAAC;wBACZ,OAAO,EAAE,CAAC,IAAI,CAAC,UAAU,GAAG;4BAAC;4BAAW;yBAAY;oBACtD;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;gBAEA,0CAA0C;gBAC1C,WAAW;oBACT,QAAQ,GAAG,CAAC;oBACZ,OAAO,OAAO;gBAChB,GAAG;YACL;YAEA,yDAAyD;YACzD,MAAM,eAAe,YAAY;gBAC/B,IAAI,OAAO,SAAS,EAAE;oBACpB,QAAQ,GAAG,CAAC;oBACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;wBACvB,QAAQ,GAAG,CAAC,8BAA8B;oBAC5C;gBACF;YACF,GAAG,QAAQ,mBAAmB;YAE9B,sBAAsB;YACtB,UAAU,OAAO,GAAG;YAEpB,sBAAsB;YACtB,OAAO;gBACL,cAAc;gBACd,IAAI,QAAQ;oBACV,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,WAAW;oBAC3D,OAAO,UAAU;oBACjB,UAAU,OAAO,GAAG;gBACtB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,uDAAuD;YACvD,OAAO,KAAO;QAChB;IACF,GAAG;QAAC;KAAU;IAEd,0BAA0B;IAC1B,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe,MAAW;QAClD,IAAI,UAAU,OAAO,IAAI,aAAa;YACpC,IAAI,UAAU;gBACZ,UAAU,OAAO,CAAC,IAAI,CAAC,OAAO,MAAM;YACtC,OAAO;gBACL,UAAU,OAAO,CAAC,IAAI,CAAC,OAAO;YAChC;YACA,OAAO;QACT;QACA,OAAO;IACT,GAAG;QAAC;KAAY;IAEhB,kCAAkC;IAClC,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QACrC,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,EAAE,CAAC,OAAO;YAC5B,OAAO;gBACL,UAAU,OAAO,EAAE,IAAI,OAAO;YAChC;QACF;QACA,OAAO,KAAO;IAChB,GAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,CAAC,QAAQ;YACX,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QAEA,IAAI,UAAU,OAAO,IAAI,aAAa;YACpC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,aAAa,EAAE,UAAU,OAAO,CAAC,EAAE,EAAE;YAC5E,UAAU,OAAO,CAAC,IAAI,CAAC,gBAAgB;gBAAE;YAAO,GAAG,CAAC;gBAClD,IAAI,YAAY,SAAS,OAAO,EAAE;oBAChC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,sCAAsC,EAAE,SAAS,OAAO,EAAE;gBACvF,OAAO;oBACL,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,OAAO,aAAa,CAAC,EAAE;gBAClE;YACF;YACA,OAAO;QACT,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,qBAAqB,EAAE,OAAO,YAAY,EAAE,UAAU,OAAO,GAAG,YAAY,cAAc,mBAAmB,EAAE,cAAc,WAAW,YAAY;YAElK,qDAAqD;YACrD,IAAI,UAAU,OAAO,IAAI,CAAC,aAAa;gBACrC,QAAQ,GAAG,CAAC;gBACZ,UAAU,OAAO,CAAC,OAAO;gBAEzB,yDAAyD;gBACzD,WAAW;oBACT,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,SAAS,EAAE;wBACpD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,OAAO,iBAAiB,CAAC;wBACvE,UAAU,OAAO,CAAC,IAAI,CAAC,gBAAgB;4BAAE;wBAAO;oBAClD;gBACF,GAAG;YACL;YAEA,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QAAE;QAAa;QAAO;QAAM;QAAI;QAAc,QAAQ,UAAU,OAAO;IAAC;AACjF", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/contexts/CreditContext.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from \"react\";\r\nimport { getUserCredits, UserCreditsResponse } from \"@/app/api/users\";\r\nimport { useWebSocket } from \"@/hooks/useWebSocket\";\r\nimport { toast } from \"sonner\";\r\n\r\n// Type for cached credit data with timestamp\r\ninterface CachedCreditData extends UserCreditsResponse {\r\n  timestamp: number;\r\n}\r\n\r\ninterface CreditContextType {\r\n  // Legacy fields for backward compatibility\r\n  credits: number;\r\n  minutes: number;\r\n  // New monthly credits fields\r\n  freeCreditsRemaining: number;\r\n  paidCredits: number;\r\n  totalAvailable: number;\r\n  usingFreeCredits: boolean;\r\n  freeMinutesRemaining: number;\r\n  paidMinutes: number;\r\n  totalMinutesAvailable: number;\r\n  // Common fields\r\n  callPricePerMinute: number;\r\n  monthlyAllowance: number;\r\n  hasSufficientCredits: boolean;\r\n  isLoading: boolean;\r\n  refreshCredits: () => Promise<void>;\r\n  creditThreshold: number;\r\n  organizationCreditThreshold: number;\r\n  effectiveThreshold: number;\r\n  // Connection state\r\n  isConnected: boolean;\r\n  hasValidData: boolean;\r\n  lastSuccessfulFetch: number | null;\r\n}\r\n\r\nconst CreditContext = createContext<CreditContextType | undefined>(undefined);\r\n\r\ninterface CreditProviderProps {\r\n  children: ReactNode;\r\n  creditThreshold?: number;\r\n}\r\n\r\nexport function CreditProvider({\r\n  children,\r\n  creditThreshold = 1\r\n}: CreditProviderProps) {\r\n  // Legacy fields for backward compatibility\r\n  const [credits, setCredits] = useState<number>(0);\r\n  const [minutes, setMinutes] = useState<number>(0);\r\n  // New monthly credits fields\r\n  const [freeCreditsRemaining, setFreeCreditsRemaining] = useState<number>(0);\r\n  const [paidCredits, setPaidCredits] = useState<number>(0);\r\n  const [totalAvailable, setTotalAvailable] = useState<number>(0);\r\n  const [usingFreeCredits, setUsingFreeCredits] = useState<boolean>(false);\r\n  const [freeMinutesRemaining, setFreeMinutesRemaining] = useState<number>(0);\r\n  const [paidMinutes, setPaidMinutes] = useState<number>(0);\r\n  const [totalMinutesAvailable, setTotalMinutesAvailable] = useState<number>(0);\r\n  // Common fields\r\n  const [callPricePerMinute, setCallPricePerMinute] = useState<number>(0.1);\r\n  const [monthlyAllowance, setMonthlyAllowance] = useState<number>(0);\r\n  const [organizationCreditThreshold, setOrganizationCreditThreshold] = useState<number>(1.0);\r\n  const [isLoading, setIsLoading] = useState<boolean>(true);\r\n  const [userId, setUserId] = useState<string | null>(null);\r\n  const [organizationId, setOrganizationId] = useState<string | null>(null);\r\n\r\n  // Connection and data validity state\r\n  const [hasValidData, setHasValidData] = useState<boolean>(false);\r\n  const [lastSuccessfulFetch, setLastSuccessfulFetch] = useState<number | null>(null);\r\n  const [consecutiveFailures, setConsecutiveFailures] = useState<number>(0);\r\n\r\n  // Initialize WebSocket connection to the credits namespace\r\n  const { isConnected, on, registerUser } = useWebSocket('credits');\r\n\r\n  // Persistence helpers\r\n  const saveCreditDataToStorage = (data: UserCreditsResponse) => {\r\n    try {\r\n      const creditData = {\r\n        ...data,\r\n        timestamp: Date.now()\r\n      };\r\n      localStorage.setItem('lastKnownCredits', JSON.stringify(creditData));\r\n    } catch (error) {\r\n      console.warn('Failed to save credit data to localStorage:', error);\r\n    }\r\n  };\r\n\r\n  const loadCreditDataFromStorage = (): CachedCreditData | null => {\r\n    try {\r\n      const stored = localStorage.getItem('lastKnownCredits');\r\n      if (stored) {\r\n        const data = JSON.parse(stored);\r\n        // Only use stored data if it's less than 5 minutes old\r\n        if (Date.now() - data.timestamp < 5 * 60 * 1000) {\r\n          return data;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.warn('Failed to load credit data from localStorage:', error);\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const fetchCredits = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await getUserCredits();\r\n\r\n      // Update new monthly credits fields\r\n      setFreeCreditsRemaining(response.freeCreditsRemaining || 0);\r\n      setPaidCredits(response.paidCredits || 0);\r\n      setTotalAvailable(response.totalAvailable || 0);\r\n      setUsingFreeCredits(response.usingFreeCredits || false);\r\n      setFreeMinutesRemaining(response.freeMinutesRemaining || 0);\r\n      setPaidMinutes(response.paidMinutes || 0);\r\n      setTotalMinutesAvailable(response.totalMinutesAvailable || 0);\r\n\r\n      // Update legacy fields for backward compatibility\r\n      setCredits(response.credits || response.totalAvailable || 0);\r\n      setMinutes(response.minutes || response.totalMinutesAvailable || 0);\r\n      setCallPricePerMinute(response.callPricePerMinute || 0.1);\r\n      setMonthlyAllowance(response.monthlyAllowance || 0);\r\n      setOrganizationCreditThreshold(response.minimumCreditsThreshold || 1.0);\r\n\r\n      // Mark as successful fetch\r\n      setHasValidData(true);\r\n      setLastSuccessfulFetch(Date.now());\r\n      setConsecutiveFailures(0);\r\n\r\n      // Save to localStorage for persistence\r\n      saveCreditDataToStorage(response);\r\n\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\"Error fetching user credits:\", error);\r\n      setConsecutiveFailures(prev => prev + 1);\r\n\r\n      // If we have recent cached data and this is a temporary failure, use cached data\r\n      const cachedData = loadCreditDataFromStorage();\r\n      if (cachedData && consecutiveFailures < 3) {\r\n        console.log('Using cached credit data due to fetch failure');\r\n\r\n        // Update with cached data instead of zeros\r\n        setFreeCreditsRemaining(cachedData.freeCreditsRemaining || 0);\r\n        setPaidCredits(cachedData.paidCredits || 0);\r\n        setTotalAvailable(cachedData.totalAvailable || 0);\r\n        setUsingFreeCredits(cachedData.usingFreeCredits || false);\r\n        setFreeMinutesRemaining(cachedData.freeMinutesRemaining || 0);\r\n        setPaidMinutes(cachedData.paidMinutes || 0);\r\n        setTotalMinutesAvailable(cachedData.totalMinutesAvailable || 0);\r\n        setCredits(cachedData.credits || cachedData.totalAvailable || 0);\r\n        setMinutes(cachedData.minutes || cachedData.totalMinutesAvailable || 0);\r\n        setCallPricePerMinute(cachedData.callPricePerMinute || 0.1);\r\n        setMonthlyAllowance(cachedData.monthlyAllowance || 0);\r\n        setOrganizationCreditThreshold(cachedData.minimumCreditsThreshold || 1.0);\r\n\r\n        return cachedData;\r\n      }\r\n\r\n      // Only return zeros if we have no cached data and multiple failures\r\n      return {\r\n        credits: 0,\r\n        minutes: 0,\r\n        callPricePerMinute: 0.1,\r\n        freeCreditsRemaining: 0,\r\n        paidCredits: 0,\r\n        totalAvailable: 0,\r\n        usingFreeCredits: false,\r\n        freeMinutesRemaining: 0,\r\n        paidMinutes: 0,\r\n        totalMinutesAvailable: 0,\r\n        monthlyAllowance: 0\r\n      };\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Get user ID and organization ID from localStorage on mount\r\n  useEffect(() => {\r\n    try {\r\n      const userData = localStorage.getItem('user_data');\r\n      if (userData) {\r\n        const parsedData = JSON.parse(userData);\r\n        const userId = parsedData._id || null;\r\n        const organizationId = parsedData.organizationId || null;\r\n\r\n        // console.log('Retrieved user data from localStorage:', {\r\n        //   userId,\r\n        //   organizationId,\r\n        //   rawData: parsedData\r\n        // });\r\n\r\n        setUserId(userId);\r\n        setOrganizationId(organizationId);\r\n\r\n        // If we don't have an organization ID, try to fetch it from the API\r\n        if (!organizationId && userId) {\r\n          console.log('No organization ID found in localStorage, will try to fetch from API');\r\n          fetchCredits().then(() => {\r\n            console.log('Fetched credits and possibly updated organization ID');\r\n          });\r\n        }\r\n      } else {\r\n        console.warn('No user data found in localStorage');\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting user data from localStorage:\", error);\r\n    }\r\n  }, []);\r\n\r\n  // Register user with WebSocket when connected and userId is available\r\n  useEffect(() => {\r\n    if (isConnected && userId) {\r\n      console.log('Registering user with WebSocket:', +9*9);\r\n      registerUser(userId);\r\n    }\r\n  }, [isConnected, userId, registerUser]);\r\n\r\n  // Set up WebSocket listeners for credit updates\r\n  useEffect(() => {\r\n    if (!isConnected) {\r\n      console.log('WebSocket not connected, skipping credit update listeners setup');\r\n      return;\r\n    }\r\n\r\n    console.log('Setting up credit update listeners, organizationId:', organizationId);\r\n\r\n    // Listen for credit updates for this user\r\n    const unsubscribeCreditUpdate = on('creditUpdate', (data: { credits: number }) => {\r\n      console.log('Received credit update via WebSocket:', data);\r\n      setCredits(data.credits);\r\n      // Calculate minutes based on current callPricePerMinute\r\n      if (callPricePerMinute > 0) {\r\n        setMinutes(data.credits / callPricePerMinute);\r\n      }\r\n      toast.info(`Your credits have been updated`);\r\n    });\r\n\r\n    // Listen for organization credit updates\r\n    const unsubscribeOrgUpdate = on('organizationCreditUpdate', (data: { organizationId: string, credits: number }) => {\r\n      console.log('Received organization credit update via WebSocket:', data);\r\n      console.log('Current organizationId:', organizationId);\r\n      console.log('Received organizationId:', data.organizationId);\r\n\r\n      // Only update if it's for our organization\r\n      // Convert both IDs to strings for comparison to avoid type mismatches\r\n      const ourOrgId = organizationId?.toString();\r\n      const receivedOrgId = data.organizationId?.toString();\r\n\r\n      console.log('Comparing organization IDs:', {\r\n        ourOrgId,\r\n        receivedOrgId,\r\n        match: ourOrgId === receivedOrgId\r\n      });\r\n\r\n      if (ourOrgId && receivedOrgId && ourOrgId === receivedOrgId) {\r\n        console.log('Organization ID matched, updating credits to:', data.credits);\r\n        setCredits(data.credits);\r\n        // Calculate minutes based on current callPricePerMinute\r\n        if (callPricePerMinute > 0) {\r\n          setMinutes(data.credits / callPricePerMinute);\r\n        }\r\n        toast.info(`Your organization credits have been updated`);\r\n      } else {\r\n        console.log('Organization ID did not match or is missing, not updating credits');\r\n        console.log('Our organization ID:', ourOrgId);\r\n        console.log('Received organization ID:', receivedOrgId);\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      console.log('Cleaning up credit update listeners');\r\n      unsubscribeCreditUpdate();\r\n      unsubscribeOrgUpdate();\r\n    };\r\n  }, [isConnected, on, organizationId]);\r\n\r\n  // Load cached data on mount, then fetch fresh data\r\n  useEffect(() => {\r\n    // Try to load cached data first for immediate display\r\n    const cachedData = loadCreditDataFromStorage();\r\n    if (cachedData) {\r\n      console.log('Loading cached credit data on mount');\r\n      setFreeCreditsRemaining(cachedData.freeCreditsRemaining || 0);\r\n      setPaidCredits(cachedData.paidCredits || 0);\r\n      setTotalAvailable(cachedData.totalAvailable || 0);\r\n      setUsingFreeCredits(cachedData.usingFreeCredits || false);\r\n      setFreeMinutesRemaining(cachedData.freeMinutesRemaining || 0);\r\n      setPaidMinutes(cachedData.paidMinutes || 0);\r\n      setTotalMinutesAvailable(cachedData.totalMinutesAvailable || 0);\r\n      setCredits(cachedData.credits || cachedData.totalAvailable || 0);\r\n      setMinutes(cachedData.minutes || cachedData.totalMinutesAvailable || 0);\r\n      setCallPricePerMinute(cachedData.callPricePerMinute || 0.1);\r\n      setMonthlyAllowance(cachedData.monthlyAllowance || 0);\r\n      setOrganizationCreditThreshold(cachedData.minimumCreditsThreshold || 1.0);\r\n      setHasValidData(true);\r\n      setLastSuccessfulFetch(cachedData.timestamp);\r\n    }\r\n\r\n    // Initial fetch\r\n    fetchCredits();\r\n\r\n    // Set up polling interval (as a fallback)\r\n    const intervalId = setInterval(() => {\r\n      fetchCredits();\r\n    }, 30000); // 30 seconds\r\n\r\n    // Clean up interval on unmount\r\n    return () => clearInterval(intervalId);\r\n  }, [callPricePerMinute]);\r\n\r\n  const refreshCredits = async () => {\r\n    await fetchCredits();\r\n  };\r\n\r\n  // Use the higher of the organization threshold or the prop threshold\r\n  const effectiveThreshold = Math.max(creditThreshold, organizationCreditThreshold);\r\n  const hasSufficientCredits = totalAvailable >= effectiveThreshold;\r\n\r\n  const value = {\r\n    // Legacy fields for backward compatibility\r\n    credits,\r\n    minutes,\r\n    // New monthly credits fields\r\n    freeCreditsRemaining,\r\n    paidCredits,\r\n    totalAvailable,\r\n    usingFreeCredits,\r\n    freeMinutesRemaining,\r\n    paidMinutes,\r\n    totalMinutesAvailable,\r\n    // Common fields\r\n    callPricePerMinute,\r\n    monthlyAllowance,\r\n    hasSufficientCredits,\r\n    isLoading,\r\n    refreshCredits,\r\n    creditThreshold,\r\n    organizationCreditThreshold,\r\n    effectiveThreshold,\r\n    // Connection state\r\n    isConnected,\r\n    hasValidData,\r\n    lastSuccessfulFetch\r\n  };\r\n\r\n  return (\r\n    <CreditContext.Provider value={value}>\r\n      {children}\r\n    </CreditContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useCredits() {\r\n  const context = useContext(CreditContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useCredits must be used within a CreditProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;AAG9C;AACA;AACA;AACA;AALA;;;;;;AAuCA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAO5D,SAAS,eAAe,EAC7B,QAAQ,EACR,kBAAkB,CAAC,EACC;IACpB,2CAA2C;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,6BAA6B;IAC7B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3E,gBAAgB;IAChB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,qCAAqC;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEvE,2DAA2D;IAC3D,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IAEvD,sBAAsB;IACtB,MAAM,0BAA0B,CAAC;QAC/B,IAAI;YACF,MAAM,aAAa;gBACjB,GAAG,IAAI;gBACP,WAAW,KAAK,GAAG;YACrB;YACA,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,+CAA+C;QAC9D;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,uDAAuD;gBACvD,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,IAAI,KAAK,MAAM;oBAC/C,OAAO;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,iDAAiD;QAChE;QACA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD;YAEpC,oCAAoC;YACpC,wBAAwB,SAAS,oBAAoB,IAAI;YACzD,eAAe,SAAS,WAAW,IAAI;YACvC,kBAAkB,SAAS,cAAc,IAAI;YAC7C,oBAAoB,SAAS,gBAAgB,IAAI;YACjD,wBAAwB,SAAS,oBAAoB,IAAI;YACzD,eAAe,SAAS,WAAW,IAAI;YACvC,yBAAyB,SAAS,qBAAqB,IAAI;YAE3D,kDAAkD;YAClD,WAAW,SAAS,OAAO,IAAI,SAAS,cAAc,IAAI;YAC1D,WAAW,SAAS,OAAO,IAAI,SAAS,qBAAqB,IAAI;YACjE,sBAAsB,SAAS,kBAAkB,IAAI;YACrD,oBAAoB,SAAS,gBAAgB,IAAI;YACjD,+BAA+B,SAAS,uBAAuB,IAAI;YAEnE,2BAA2B;YAC3B,gBAAgB;YAChB,uBAAuB,KAAK,GAAG;YAC/B,uBAAuB;YAEvB,uCAAuC;YACvC,wBAAwB;YAExB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,uBAAuB,CAAA,OAAQ,OAAO;YAEtC,iFAAiF;YACjF,MAAM,aAAa;YACnB,IAAI,cAAc,sBAAsB,GAAG;gBACzC,QAAQ,GAAG,CAAC;gBAEZ,2CAA2C;gBAC3C,wBAAwB,WAAW,oBAAoB,IAAI;gBAC3D,eAAe,WAAW,WAAW,IAAI;gBACzC,kBAAkB,WAAW,cAAc,IAAI;gBAC/C,oBAAoB,WAAW,gBAAgB,IAAI;gBACnD,wBAAwB,WAAW,oBAAoB,IAAI;gBAC3D,eAAe,WAAW,WAAW,IAAI;gBACzC,yBAAyB,WAAW,qBAAqB,IAAI;gBAC7D,WAAW,WAAW,OAAO,IAAI,WAAW,cAAc,IAAI;gBAC9D,WAAW,WAAW,OAAO,IAAI,WAAW,qBAAqB,IAAI;gBACrE,sBAAsB,WAAW,kBAAkB,IAAI;gBACvD,oBAAoB,WAAW,gBAAgB,IAAI;gBACnD,+BAA+B,WAAW,uBAAuB,IAAI;gBAErE,OAAO;YACT;YAEA,oEAAoE;YACpE,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,oBAAoB;gBACpB,sBAAsB;gBACtB,aAAa;gBACb,gBAAgB;gBAChB,kBAAkB;gBAClB,sBAAsB;gBACtB,aAAa;gBACb,uBAAuB;gBACvB,kBAAkB;YACpB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,UAAU;gBACZ,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,MAAM,SAAS,WAAW,GAAG,IAAI;gBACjC,MAAM,iBAAiB,WAAW,cAAc,IAAI;gBAEpD,0DAA0D;gBAC1D,YAAY;gBACZ,oBAAoB;gBACpB,wBAAwB;gBACxB,MAAM;gBAEN,UAAU;gBACV,kBAAkB;gBAElB,oEAAoE;gBACpE,IAAI,CAAC,kBAAkB,QAAQ;oBAC7B,QAAQ,GAAG,CAAC;oBACZ,eAAe,IAAI,CAAC;wBAClB,QAAQ,GAAG,CAAC;oBACd;gBACF;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;QAC9D;IACF,GAAG,EAAE;IAEL,sEAAsE;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,QAAQ;YACzB,QAAQ,GAAG,CAAC,oCAAoC,CAAC,IAAE;YACnD,aAAa;QACf;IACF,GAAG;QAAC;QAAa;QAAQ;KAAa;IAEtC,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;YAChB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC,uDAAuD;QAEnE,0CAA0C;QAC1C,MAAM,0BAA0B,GAAG,gBAAgB,CAAC;YAClD,QAAQ,GAAG,CAAC,yCAAyC;YACrD,WAAW,KAAK,OAAO;YACvB,wDAAwD;YACxD,IAAI,qBAAqB,GAAG;gBAC1B,WAAW,KAAK,OAAO,GAAG;YAC5B;YACA,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC;QAC7C;QAEA,yCAAyC;QACzC,MAAM,uBAAuB,GAAG,4BAA4B,CAAC;YAC3D,QAAQ,GAAG,CAAC,sDAAsD;YAClE,QAAQ,GAAG,CAAC,2BAA2B;YACvC,QAAQ,GAAG,CAAC,4BAA4B,KAAK,cAAc;YAE3D,2CAA2C;YAC3C,sEAAsE;YACtE,MAAM,WAAW,gBAAgB;YACjC,MAAM,gBAAgB,KAAK,cAAc,EAAE;YAE3C,QAAQ,GAAG,CAAC,+BAA+B;gBACzC;gBACA;gBACA,OAAO,aAAa;YACtB;YAEA,IAAI,YAAY,iBAAiB,aAAa,eAAe;gBAC3D,QAAQ,GAAG,CAAC,iDAAiD,KAAK,OAAO;gBACzE,WAAW,KAAK,OAAO;gBACvB,wDAAwD;gBACxD,IAAI,qBAAqB,GAAG;oBAC1B,WAAW,KAAK,OAAO,GAAG;gBAC5B;gBACA,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,2CAA2C,CAAC;YAC1D,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,wBAAwB;gBACpC,QAAQ,GAAG,CAAC,6BAA6B;YAC3C;QACF;QAEA,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ;YACA;QACF;IACF,GAAG;QAAC;QAAa;QAAI;KAAe;IAEpC,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sDAAsD;QACtD,MAAM,aAAa;QACnB,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC;YACZ,wBAAwB,WAAW,oBAAoB,IAAI;YAC3D,eAAe,WAAW,WAAW,IAAI;YACzC,kBAAkB,WAAW,cAAc,IAAI;YAC/C,oBAAoB,WAAW,gBAAgB,IAAI;YACnD,wBAAwB,WAAW,oBAAoB,IAAI;YAC3D,eAAe,WAAW,WAAW,IAAI;YACzC,yBAAyB,WAAW,qBAAqB,IAAI;YAC7D,WAAW,WAAW,OAAO,IAAI,WAAW,cAAc,IAAI;YAC9D,WAAW,WAAW,OAAO,IAAI,WAAW,qBAAqB,IAAI;YACrE,sBAAsB,WAAW,kBAAkB,IAAI;YACvD,oBAAoB,WAAW,gBAAgB,IAAI;YACnD,+BAA+B,WAAW,uBAAuB,IAAI;YACrE,gBAAgB;YAChB,uBAAuB,WAAW,SAAS;QAC7C;QAEA,gBAAgB;QAChB;QAEA,0CAA0C;QAC1C,MAAM,aAAa,YAAY;YAC7B;QACF,GAAG,QAAQ,aAAa;QAExB,+BAA+B;QAC/B,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAmB;IAEvB,MAAM,iBAAiB;QACrB,MAAM;IACR;IAEA,qEAAqE;IACrE,MAAM,qBAAqB,KAAK,GAAG,CAAC,iBAAiB;IACrD,MAAM,uBAAuB,kBAAkB;IAE/C,MAAM,QAAQ;QACZ,2CAA2C;QAC3C;QACA;QACA,6BAA6B;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,mBAAmB;QACnB;QACA;QACA;IACF;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1902, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/CreditDisplay.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { useCredits } from \"@/contexts/CreditContext\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Link from \"next/link\";\r\nimport ScaleIn from \"@/animations/ScaleIn\";\r\n\r\nexport function CreditDisplay() {\r\n  const {\r\n    totalMinutesAvailable,\r\n    monthlyAllowance,\r\n    isLoading,\r\n    totalAvailable,\r\n    organizationCreditThreshold,\r\n    isConnected,\r\n    hasValidData,\r\n    lastSuccessfulFetch\r\n  } = useCredits();\r\n\r\n  // Determine if credits are low (only if we have valid data)\r\n  const isLowCredit = hasValidData && totalAvailable < organizationCreditThreshold;\r\n  const isWarningCredit = hasValidData && totalAvailable < (organizationCreditThreshold * 2) && totalAvailable >= organizationCreditThreshold;\r\n\r\n  // Check if data is stale\r\n  const isDataStale = lastSuccessfulFetch && (Date.now() - lastSuccessfulFetch > 2 * 60 * 1000); // 2 minutes\r\n  const showConnectionWarning = !isConnected || isDataStale;\r\n\r\n  return (\r\n    <ScaleIn delay={0.2}>\r\n      <div className=\"mx-1 my-3 transition-all duration-800 ease-in-out delay-1000\">\r\n        <div className={`bg-white dark:bg-gray-800 border rounded-lg p-4 transform transition-all duration-500 ease-in-out ${\r\n          isLowCredit\r\n            ? 'border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/10'\r\n            : isWarningCredit\r\n              ? 'border-yellow-300 dark:border-yellow-700 bg-white dark:bg-yellow-900/10'\r\n              : 'border-gray-200 dark:border-gray-700'\r\n        }`}>\r\n          {/* Header */}\r\n          <div className=\"flex justify-between items-center mb-3\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <h3 className=\"text-xs font-medium\">Credits</h3>\r\n              {showConnectionWarning && (\r\n                <div className=\"w-2 h-2 rounded-full bg-orange-400 animate-pulse\" title=\"Connection issue - data may be outdated\" />\r\n              )}\r\n            </div>\r\n            {(isLowCredit || isWarningCredit) && (\r\n              <p className={` text-xs font-medium ${\r\n                isLowCredit ? 'text-red-500' : 'text-yellow-500'\r\n              }`} >\r\n                {\r\n                isLowCredit ? 'Low Credits' : 'Warning'\r\n              }\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div className=\"mb-3\">\r\n            <div className=\"flex items-center justify-between mb-1\">\r\n              <span className={`text-xs ${\r\n                isLowCredit\r\n                  ? 'text-red-600 dark:text-red-400'\r\n                  : isWarningCredit\r\n                    ? 'text-yellow-600 dark:text-yellow-400'\r\n                    : 'text-green-600 dark:text-green-400'\r\n              }`}>\r\n                Minutes Remaining\r\n              </span>\r\n              <span className={`text-sm font-semibold ${\r\n                isLowCredit\r\n                  ? 'text-red-600 dark:text-red-400'\r\n                  : isWarningCredit\r\n                    ? 'text-yellow-600 dark:text-yellow-400'\r\n                    : 'text-green-600 dark:text-green-400'\r\n              }`}>\r\n                {isLoading ? \"...\" : `${totalMinutesAvailable.toFixed(0)} min`}\r\n              </span>\r\n            </div>\r\n            <div className=\"h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden\">\r\n              <div\r\n                className={`h-full rounded-full ${\r\n                  isLowCredit\r\n                    ? 'bg-red-500'\r\n                    : isWarningCredit\r\n                      ? 'bg-yellow-500'\r\n                      : 'bg-green-500'\r\n                }`}\r\n                style={{\r\n                  width: `${Math.min(\r\n                    100,\r\n                    (totalMinutesAvailable / Math.max(monthlyAllowance, 1)) *\r\n                      100\r\n                  )}%`,\r\n                }}\r\n              ></div>\r\n            </div>\r\n          </div>\r\n\r\n          <Link href=\"/billing\" className=\"block\">\r\n            <Button\r\n              variant=\"default\"\r\n              className=\"w-full bg-black hover:bg-gray-800 dark:bg-gray-900 dark:hover:bg-gray-800 text-white text-xs py-1 h-8\"\r\n            >\r\n              Recharge\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </ScaleIn>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQO,SAAS;IACd,MAAM,EACJ,qBAAqB,EACrB,gBAAgB,EAChB,SAAS,EACT,cAAc,EACd,2BAA2B,EAC3B,WAAW,EACX,YAAY,EACZ,mBAAmB,EACpB,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAEb,4DAA4D;IAC5D,MAAM,cAAc,gBAAgB,iBAAiB;IACrD,MAAM,kBAAkB,gBAAgB,iBAAkB,8BAA8B,KAAM,kBAAkB;IAEhH,yBAAyB;IACzB,MAAM,cAAc,uBAAwB,KAAK,GAAG,KAAK,sBAAsB,IAAI,KAAK,MAAO,YAAY;IAC3G,MAAM,wBAAwB,CAAC,eAAe;IAE9C,qBACE,8OAAC,6HAAA,CAAA,UAAO;QAAC,OAAO;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,kGAAkG,EACjH,cACI,oEACA,kBACE,4EACA,wCACN;;kCAEA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;oCACnC,uCACC,8OAAC;wCAAI,WAAU;wCAAmD,OAAM;;;;;;;;;;;;4BAG3E,CAAC,eAAe,eAAe,mBAC9B,8OAAC;gCAAE,WAAW,CAAC,qBAAqB,EAClC,cAAc,iBAAiB,mBAC/B;0CAEA,cAAc,gBAAgB;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAC,QAAQ,EACxB,cACI,mCACA,kBACE,yCACA,sCACN;kDAAE;;;;;;kDAGJ,8OAAC;wCAAK,WAAW,CAAC,sBAAsB,EACtC,cACI,mCACA,kBACE,yCACA,sCACN;kDACC,YAAY,QAAQ,GAAG,sBAAsB,OAAO,CAAC,GAAG,IAAI,CAAC;;;;;;;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAW,CAAC,oBAAoB,EAC9B,cACI,eACA,kBACE,kBACA,gBACN;oCACF,OAAO;wCACL,OAAO,GAAG,KAAK,GAAG,CAChB,KACA,AAAC,wBAAwB,KAAK,GAAG,CAAC,kBAAkB,KAClD,KACF,CAAC,CAAC;oCACN;;;;;;;;;;;;;;;;;kCAKN,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAW,WAAU;kCAC9B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28workspace%29/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { useTheme } from \"next-themes\";\r\nimport {LayoutDashboard,Bot,Settings,ScrollText,Brain,MessageSquare,Sun,Moon,Loader2,ChartLine,Users,Calendar,Building2,Wallet} from \"lucide-react\";\r\nimport LogoBlack from \"@/assets/img/OROVA-PURPLE.png\";\r\nimport LogoWhite from \"@/assets/img/OROVA-WHITE.png\";\r\nimport { ProfileMenu } from \"@/components/ProfileMenu\";\r\nimport FadeIn from \"@/animations/FadeIn\";\r\nimport { UserInfo } from \"../(auth)/actions/auth\";\r\nimport { authFetch } from \"@/lib/authFetch\";\r\nimport { setupTokenRefresh } from \"@/lib/auth-client\";\r\nimport ScaleIn from \"@/animations/ScaleIn\";\r\nimport { CreditProvider } from \"@/contexts/CreditContext\";\r\nimport { CreditDisplay } from \"@/components/CreditDisplay\";\r\n// import { LanguageSwitcher } from \"@/components/LanguageSwitcher\";\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n\r\nexport default function WorkspaceLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const { theme, setTheme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n  const [settingsExpanded, setSettingsExpanded] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const [sidebarOpen, setSidebarOpen] = useState(true);\r\n\r\n  // Add state for user data\r\n  const [user, setUser] = useState<UserInfo | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Fetch user data when component mounts\r\n  useEffect(() => {\r\n    async function fetchUserData() {\r\n      try {\r\n        // Use authFetch instead of regular fetch\r\n        const response = await authFetch(`${API_BASE_URL}/api/auth/me`);\r\n\r\n        if (!response.ok) {\r\n          router.push(\"/login\");\r\n          return;\r\n        }\r\n\r\n        const userData = await response.json();\r\n\r\n        // More flexible check - look for any ID field and email\r\n        const fullName = userData.fullName || userData.name;\r\n        const userId = userData.userId || userData._id || userData.id;\r\n        const email = userData.email;\r\n        const organizationId = userData.organizationId;\r\n\r\n        if (userId && email) {\r\n          // Normalize user data structure\r\n          const normalizedUserData = {\r\n            fullName: fullName || email.split(\"@\")[0],\r\n            userId: userId,\r\n            _id: userId, // Add _id for compatibility with CreditContext\r\n            email: email,\r\n            role: userData.role || \"user\",\r\n            organizationId: organizationId || null,\r\n          };\r\n\r\n          // Store the complete user data in localStorage for other components to use\r\n          localStorage.setItem('user_data', JSON.stringify(normalizedUserData));\r\n\r\n          setUser(normalizedUserData);\r\n        } else {\r\n          router.push(\"/login\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching user data:\", error);\r\n        router.push(\"/login\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n\r\n    fetchUserData();\r\n  }, [router]);\r\n\r\n  useEffect(() => {\r\n    // Set up automatic token refresh every 50 minutes\r\n    const cleanupTokenRefresh = setupTokenRefresh(50);\r\n\r\n    // Cleanup function when component unmounts\r\n    return cleanupTokenRefresh;\r\n  }, []);\r\n\r\n  // No need to fetch credits here anymore, it's handled by CreditContext\r\n\r\n  // Prevent hydration mismatch\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (window.innerWidth < 768) {\r\n        setSidebarOpen(false);\r\n      } else {\r\n        setSidebarOpen(true);\r\n      }\r\n    };\r\n\r\n    // Set initial state based on screen size\r\n    handleResize();\r\n\r\n    // Add event listener\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  const toggleSidebar = () => {\r\n    setSidebarOpen(!sidebarOpen);\r\n  };\r\n\r\n  const handleLinkClick = () => {\r\n    // Only close the sidebar on mobile devices\r\n    if (window.innerWidth < 768) {\r\n      setSidebarOpen(false);\r\n    }\r\n  };\r\n\r\n  // Format user data for the ProfileMenu component\r\n  const userData = user\r\n    ? {\r\n        fullName: user.fullName || user.email.split(\"@\")[0], // Ensure fullName is always a string\r\n        name: user.email.split(\"@\")[0], // Use part of email as name if no name provided\r\n        email: user.email,\r\n        avatar: \"\",\r\n        role: user.role,\r\n      }\r\n    : {\r\n        fullName: \"Loading...\", // Add fullName to match the expected type\r\n        name: \"Loading...\",\r\n        email: \"\",\r\n        avatar: \"\",\r\n        role: \"\",\r\n      };\r\n\r\n  // Navigation sections with grouped links\r\n  const navigationSections = [\r\n    {\r\n      title: \"Overview\",\r\n      links: [\r\n        { name: \"Dashboard\", href: \"/dashboard\", icon: LayoutDashboard },\r\n        { name: \"Agents\", href: \"/agents\", icon: Bot },\r\n        { name: \"Campaigns\", href: \"/campaign\", icon: ChartLine },\r\n        { name: \"History\", href: \"/history\", icon: ScrollText },\r\n        { name: \"Schedule\", href: \"/schedule\", icon: Calendar },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Resources\",\r\n      links: [\r\n        { name: \"Brain\", href: \"/brain\", icon: Brain },\r\n        { name: \"Contacts\", href: \"/contacts\", icon: MessageSquare },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // Configuration section moved to bottom\r\n  const configSection = {\r\n    title: \"Configuration\",\r\n    links: [\r\n      // Settings now becomes a parent with sublinks\r\n      {\r\n        name: \"Settings\",\r\n        href: \"/settings\",\r\n        icon: Settings,\r\n        isParent: true,\r\n        subLinks: [\r\n          { name: \"General\", href: \"/settings\", icon: Settings },\r\n          { name: \"Users\", href: \"/users\", icon: Users },\r\n          { name: \"Billing\", href: \"/billing\", icon: Wallet },\r\n          // { name: \"Voices\", href: \"/voices\", icon: Mic },\r\n          // { name: \"Phone Number\", href: \"/phonenumber\", icon: Phone },\r\n          // { name: \"Integration\", href: \"/integration\", icon: Plug },\r\n          // Only show Organizations link for superadmins\r\n          ...(user?.role === 'superadmin' ? [{ name: \"Workspaces\", href: \"/workspaces\", icon: Building2 }] : []),\r\n        ],\r\n      },\r\n    ],\r\n  };\r\n\r\n  const toggleTheme = () => {\r\n    setTheme(theme === \"dark\" ? \"light\" : \"dark\");\r\n  };\r\n\r\n  // Show loading state until mounted and user data is fetched\r\n  if (!mounted || loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"flex flex-col items-center space-y-4\">\r\n          <Loader2 className=\"h-12 w-12 animate-spin text-primary\" />\r\n          <p className=\"text-lg font-medium\">Loading...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n      <CreditProvider creditThreshold={1}>\r\n        <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\r\n        {/* Sidebar */}\r\n        <div\r\n          className={`fixed inset-y-0 left-0 z-550 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-500 ease-in-out ${\r\n            sidebarOpen\r\n              ? \"w-64 translate-x-0\"\r\n              : \"w-16 md:translate-x-0 -translate-x-full\"\r\n          }`}\r\n        >\r\n          <div className=\"flex-shrink-0\">\r\n            {/* Logo */}\r\n            <div className=\"flex items-center justify-start h-16 px-4 border-b border-gray-200 dark:border-gray-700\">\r\n              <div\r\n                className={`flex items-center  ${\r\n                  sidebarOpen ? \"gap-6 ml-3\" : \"justify-center w-full\"\r\n                }`}\r\n              >\r\n                <Link href=\"/\">\r\n                  {sidebarOpen && (\r\n                    <ScaleIn delay={0.2}>\r\n                      <Image\r\n                        src={theme === \"dark\" ? LogoWhite : LogoBlack}\r\n                        alt=\"Orova AI\"\r\n                        className=\"h-5 w-auto\"\r\n                      />\r\n                    </ScaleIn>\r\n                  )}\r\n\r\n                  {!sidebarOpen && (\r\n                    <ScaleIn delay={0.1}>\r\n                      <div className=\"h-8 w-8 rounded-full bg-purple-600 flex items-center justify-center text-white font-bold\">\r\n                        O\r\n                      </div>\r\n                    </ScaleIn>\r\n                  )}\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Scrollable navigation area */}\r\n          <div className=\"flex-grow overflow-hidden\">\r\n            {/* Navigation with Sections */}\r\n            <nav className={`px-4 py-4 space-y-6 ${!sidebarOpen && \"px-2\"}`}>\r\n              {navigationSections.map((section, index) => (\r\n                <div key={index}>\r\n                  {sidebarOpen && (\r\n                    <h3 className=\"text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold mb-2 px-3\">\r\n                      {section.title}\r\n                    </h3>\r\n                  )}\r\n                  <div className=\"space-y-1\">\r\n                    {section.links.map((item) => {\r\n                      const Icon = item.icon;\r\n                      const isActive = pathname.includes(item.href);\r\n                      return (\r\n                        <Link\r\n                          key={item.name}\r\n                          href={item.href}\r\n                          onClick={handleLinkClick}\r\n                          className={`flex items-center ${\r\n                            sidebarOpen ? \"px-3\" : \"px-0 justify-center\"\r\n                          } py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200  ${\r\n                            isActive ? \"bg-gray-100 dark:bg-gray-700\" : \"\"\r\n                          }`}\r\n                          title={!sidebarOpen ? item.name : \"\"}\r\n                        >\r\n                          <Icon className=\"h-5 w-5\" />\r\n                          {sidebarOpen && item.name}\r\n                        </Link>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </nav>\r\n          </div>\r\n\r\n          {/* Configuration Section - Fixed at bottom */}\r\n          <div\r\n            className={`flex-shrink-0 border-t border-gray-200 dark:border-gray-700 px-4 py-4 ${\r\n              !sidebarOpen && \"px-2\"\r\n            }`}\r\n          >\r\n            {/* Billing Card - Now inside configuration section */}\r\n\r\n            {/* Credit Display Component */}\r\n            {sidebarOpen && <CreditDisplay />}\r\n\r\n            <div className=\"space-y-3\">\r\n              {configSection.links.map((item) => {\r\n                const Icon = item.icon;\r\n                const isActive = pathname.includes(item.href);\r\n                const isSettingsActive =\r\n                  item.isParent &&\r\n                  (isActive ||\r\n                    item.subLinks?.some((subLink) =>\r\n                      pathname.includes(subLink.href)\r\n                    ));\r\n\r\n                return (\r\n                  <div key={item.name}>\r\n                    {item.isParent ? (\r\n                      <>\r\n                        <button\r\n                          onClick={() => setSettingsExpanded(!settingsExpanded)}\r\n                          className={`w-full cursor-pointer flex items-center ${\r\n                            sidebarOpen\r\n                              ? \"justify-between px-3\"\r\n                              : \"justify-center px-0\"\r\n                          } py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200  ${\r\n                            isSettingsActive\r\n                              ? \"bg-gray-100 dark:bg-gray-700\"\r\n                              : \"\"\r\n                          }`}\r\n                          title={!sidebarOpen ? item.name : \"\"}\r\n                        >\r\n                          <div className=\"flex items-center gap-3\">\r\n                            <Icon className=\"h-5 w-5\" />\r\n                            {sidebarOpen && item.name}\r\n                          </div>\r\n                          {sidebarOpen && (\r\n                            <svg\r\n                              className={`w-4 h-4 transition-transform duration-300 ease-in-out ${\r\n                                settingsExpanded ? \"rotate-180\" : \"rotate-0\"\r\n                              }`}\r\n                              fill=\"none\"\r\n                              stroke=\"currentColor\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                            >\r\n                              <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                strokeWidth={2}\r\n                                d=\"M19 9l-7 7-7-7\"\r\n                              />\r\n                            </svg>\r\n                          )}\r\n                        </button>\r\n\r\n                        {/* Sublinks - only visible when sidebar is expanded */}\r\n                        {sidebarOpen && (\r\n                          <div\r\n                            className={`ml-8 space-y-1 mt-1 mb-2 overflow-hidden transition-all duration-700 ease-in-out ${\r\n                              settingsExpanded\r\n                                ? \"max-h-[200px] opacity-100 transform-none\"\r\n                                : \"max-h-0 opacity-0 transform translate-y-2\"\r\n                            }`}\r\n                          >\r\n                            {item.subLinks?.map((subLink) => {\r\n                              const SubIcon = subLink.icon;\r\n                              const isSubActive = pathname === subLink.href;\r\n\r\n                              return (\r\n                                <Link\r\n                                  key={subLink.name}\r\n                                  href={subLink.href}\r\n                                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-300  hover:translate-x-1 hover:scale-[1.02] ${\r\n                                    isSubActive\r\n                                      ? \"bg-gray-100 dark:bg-gray-700\"\r\n                                      : \"\"\r\n                                  }`}\r\n                                >\r\n                                  <SubIcon className=\"h-4 w-4\" />\r\n                                  {subLink.name}\r\n                                </Link>\r\n                              );\r\n                            })}\r\n                          </div>\r\n                        )}\r\n                      </>\r\n                    ) : (\r\n                      <Link\r\n                        href={item.href}\r\n                        className={`flex items-center ${\r\n                          sidebarOpen ? \"px-3\" : \"px-0 justify-center\"\r\n                        } py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-300 hover:translate-x-1 hover:scale-[1.02] ${\r\n                          isActive ? \"bg-gray-100 dark:bg-gray-700\" : \"\"\r\n                        }`}\r\n                        title={!sidebarOpen ? item.name : \"\"}\r\n                      >\r\n                        <Icon className=\"h-5 w-5\" />\r\n                        {sidebarOpen && item.name}\r\n                      </Link>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n\r\n            {/* Settings link (last item in config section) */}\r\n            {/* {configSection.links.slice(3).map((item) => {\r\n                const Icon = item.icon;\r\n                const isActive = pathname.includes(item.href);\r\n                return (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200 hover:shadow-sm hover:translate-x-1 hover:scale-[1.02] ${\r\n                      isActive ? \"bg-gray-100 dark:bg-gray-700\" : \"\"\r\n                    }`}\r\n                  >\r\n                    <Icon className=\"h-5 w-5\" />\r\n                    {item.name}\r\n                  </Link>\r\n                );\r\n              })} */}\r\n            {/* </div> */}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile Sidebar Toggle Button */}\r\n\r\n        {sidebarOpen && (\r\n          <div\r\n            className=\"fixed inset-0 bg-gray-800 bg-opacity-50 z-10 md:hidden\"\r\n            onClick={() => setSidebarOpen(false)}\r\n          />\r\n        )}\r\n\r\n        {/* Main Content */}\r\n        <div\r\n          className={`transition-all duration-300 ${\r\n            sidebarOpen ? \"md:pl-60 pl-0\" : \"md:pl-16 pl-0\"\r\n          }`}\r\n        >\r\n          {/* Header */}\r\n          <header className=\"bg-white sticky top-0 z-500 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 w-full\">\r\n            <div className=\"h-16 px-4 md:px-8 flex items-center justify-between\">\r\n              {/* Left section with menu button and welcome message */}\r\n              <div className=\"flex items-center space-x-4\">\r\n                {/* Hamburger menu button */}\r\n                <button\r\n                  onClick={toggleSidebar}\r\n                  className=\"p-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white\"\r\n                >\r\n                  <svg\r\n                    className=\"w-6 h-6\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M4 6h16M4 12h16M4 18h16\"\r\n                    />\r\n                  </svg>\r\n                </button>\r\n\r\n                {/* Welcome message */}\r\n                <h2 className=\"text-xl md:text-2xl font-bold text-gray-800 dark:text-gray-100 hidden sm:block\">\r\n                  Welcome Back, {user?.fullName || \"Guest\"}\r\n                </h2>\r\n              </div>\r\n\r\n              {/* Right section with profile and theme controls */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                <ProfileMenu user={userData} />\r\n                <div className=\"h-6 w-px bg-gray-200 dark:bg-gray-700\" />\r\n                <button\r\n                  onClick={toggleTheme}\r\n                  className=\"p-2 rounded-lg text-gray-800 dark:text-white\"\r\n                >\r\n                  {theme === \"dark\" ? <Sun size={18} /> : <Moon size={18} />}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </header>\r\n\r\n          {/* Page Content */}\r\n          <FadeIn>\r\n            <main className=\"p-4 md:p-10\">{children}</main>\r\n          </FadeIn>\r\n        </div>\r\n      </div>\r\n    </CreditProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;;AAkBA,oEAAoE;AAEpE,MAAM,eAAe,6DAAsC;AAE5C,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0BAA0B;IAC1B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,IAAI;gBACF,yCAAyC;gBACzC,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,GAAG,aAAa,YAAY,CAAC;gBAE9D,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,MAAM,WAAW,MAAM,SAAS,IAAI;gBAEpC,wDAAwD;gBACxD,MAAM,WAAW,SAAS,QAAQ,IAAI,SAAS,IAAI;gBACnD,MAAM,SAAS,SAAS,MAAM,IAAI,SAAS,GAAG,IAAI,SAAS,EAAE;gBAC7D,MAAM,QAAQ,SAAS,KAAK;gBAC5B,MAAM,iBAAiB,SAAS,cAAc;gBAE9C,IAAI,UAAU,OAAO;oBACnB,gCAAgC;oBAChC,MAAM,qBAAqB;wBACzB,UAAU,YAAY,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;wBACzC,QAAQ;wBACR,KAAK;wBACL,OAAO;wBACP,MAAM,SAAS,IAAI,IAAI;wBACvB,gBAAgB,kBAAkB;oBACpC;oBAEA,2EAA2E;oBAC3E,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;oBAEjD,QAAQ;gBACV,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kDAAkD;QAClD,MAAM,sBAAsB,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;QAE9C,2CAA2C;QAC3C,OAAO;IACT,GAAG,EAAE;IAEL,uEAAuE;IAEvE,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,UAAU,GAAG,KAAK;gBAC3B,eAAe;YACjB,OAAO;gBACL,eAAe;YACjB;QACF;QAEA,yCAAyC;QACzC;QAEA,qBAAqB;QACrB,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,eAAe,CAAC;IAClB;IAEA,MAAM,kBAAkB;QACtB,2CAA2C;QAC3C,IAAI,OAAO,UAAU,GAAG,KAAK;YAC3B,eAAe;QACjB;IACF;IAEA,iDAAiD;IACjD,MAAM,WAAW,OACb;QACE,UAAU,KAAK,QAAQ,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,KAAK,KAAK;QACjB,QAAQ;QACR,MAAM,KAAK,IAAI;IACjB,IACA;QACE,UAAU;QACV,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;IACR;IAEJ,yCAAyC;IACzC,MAAM,qBAAqB;QACzB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAa,MAAM;oBAAc,MAAM,4NAAA,CAAA,kBAAe;gBAAC;gBAC/D;oBAAE,MAAM;oBAAU,MAAM;oBAAW,MAAM,gMAAA,CAAA,MAAG;gBAAC;gBAC7C;oBAAE,MAAM;oBAAa,MAAM;oBAAa,MAAM,gNAAA,CAAA,YAAS;gBAAC;gBACxD;oBAAE,MAAM;oBAAW,MAAM;oBAAY,MAAM,kNAAA,CAAA,aAAU;gBAAC;gBACtD;oBAAE,MAAM;oBAAY,MAAM;oBAAa,MAAM,0MAAA,CAAA,WAAQ;gBAAC;aACvD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,MAAM;oBAAU,MAAM,oMAAA,CAAA,QAAK;gBAAC;gBAC7C;oBAAE,MAAM;oBAAY,MAAM;oBAAa,MAAM,wNAAA,CAAA,gBAAa;gBAAC;aAC5D;QACH;KACD;IAED,wCAAwC;IACxC,MAAM,gBAAgB;QACpB,OAAO;QACP,OAAO;YACL,8CAA8C;YAC9C;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU;gBACV,UAAU;oBACR;wBAAE,MAAM;wBAAW,MAAM;wBAAa,MAAM,0MAAA,CAAA,WAAQ;oBAAC;oBACrD;wBAAE,MAAM;wBAAS,MAAM;wBAAU,MAAM,oMAAA,CAAA,QAAK;oBAAC;oBAC7C;wBAAE,MAAM;wBAAW,MAAM;wBAAY,MAAM,sMAAA,CAAA,SAAM;oBAAC;oBAClD,kDAAkD;oBAClD,+DAA+D;oBAC/D,6DAA6D;oBAC7D,+CAA+C;uBAC3C,MAAM,SAAS,eAAe;wBAAC;4BAAE,MAAM;4BAAc,MAAM;4BAAe,MAAM,gNAAA,CAAA,YAAS;wBAAC;qBAAE,GAAG,EAAE;iBACtG;YACH;SACD;IACH;IAEA,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,4DAA4D;IAC5D,IAAI,CAAC,WAAW,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;;;;;;;;;;;;IAI3C;IAEA,qBACI,8OAAC,iIAAA,CAAA,iBAAc;QAAC,iBAAiB;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEf,8OAAC;oBACC,WAAW,CAAC,2JAA2J,EACrK,cACI,uBACA,2CACJ;;sCAEF,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAW,CAAC,mBAAmB,EAC7B,cAAc,eAAe,yBAC7B;8CAEF,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;4CACR,6BACC,8OAAC,6HAAA,CAAA,UAAO;gDAAC,OAAO;0DACd,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,UAAU,SAAS,4SAAA,CAAA,UAAS,GAAG,8SAAA,CAAA,UAAS;oDAC7C,KAAI;oDACJ,WAAU;;;;;;;;;;;4CAKf,CAAC,6BACA,8OAAC,6HAAA,CAAA,UAAO;gDAAC,OAAO;0DACd,cAAA,8OAAC;oDAAI,WAAU;8DAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWtH,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAW,CAAC,oBAAoB,EAAE,CAAC,eAAe,QAAQ;0CAC5D,mBAAmB,GAAG,CAAC,CAAC,SAAS,sBAChC,8OAAC;;4CACE,6BACC,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAGlB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;oDAClB,MAAM,OAAO,KAAK,IAAI;oDACtB,MAAM,WAAW,SAAS,QAAQ,CAAC,KAAK,IAAI;oDAC5C,qBACE,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS;wDACT,WAAW,CAAC,kBAAkB,EAC5B,cAAc,SAAS,sBACxB,oJAAoJ,EACnJ,WAAW,iCAAiC,IAC5C;wDACF,OAAO,CAAC,cAAc,KAAK,IAAI,GAAG;;0EAElC,8OAAC;gEAAK,WAAU;;;;;;4DACf,eAAe,KAAK,IAAI;;uDAXpB,KAAK,IAAI;;;;;gDAcpB;;;;;;;uCA1BM;;;;;;;;;;;;;;;sCAkChB,8OAAC;4BACC,WAAW,CAAC,sEAAsE,EAChF,CAAC,eAAe,QAChB;;gCAKD,6BAAe,8OAAC,mIAAA,CAAA,gBAAa;;;;;8CAE9B,8OAAC;oCAAI,WAAU;8CACZ,cAAc,KAAK,CAAC,GAAG,CAAC,CAAC;wCACxB,MAAM,OAAO,KAAK,IAAI;wCACtB,MAAM,WAAW,SAAS,QAAQ,CAAC,KAAK,IAAI;wCAC5C,MAAM,mBACJ,KAAK,QAAQ,IACb,CAAC,YACC,KAAK,QAAQ,EAAE,KAAK,CAAC,UACnB,SAAS,QAAQ,CAAC,QAAQ,IAAI,EAC/B;wCAEL,qBACE,8OAAC;sDACE,KAAK,QAAQ,iBACZ;;kEACE,8OAAC;wDACC,SAAS,IAAM,oBAAoB,CAAC;wDACpC,WAAW,CAAC,wCAAwC,EAClD,cACI,yBACA,sBACL,oJAAoJ,EACnJ,mBACI,iCACA,IACJ;wDACF,OAAO,CAAC,cAAc,KAAK,IAAI,GAAG;;0EAElC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;;;;;oEACf,eAAe,KAAK,IAAI;;;;;;;4DAE1B,6BACC,8OAAC;gEACC,WAAW,CAAC,sDAAsD,EAChE,mBAAmB,eAAe,YAClC;gEACF,MAAK;gEACL,QAAO;gEACP,SAAQ;gEACR,OAAM;0EAEN,cAAA,8OAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,aAAa;oEACb,GAAE;;;;;;;;;;;;;;;;;oDAOT,6BACC,8OAAC;wDACC,WAAW,CAAC,iFAAiF,EAC3F,mBACI,6CACA,6CACJ;kEAED,KAAK,QAAQ,EAAE,IAAI,CAAC;4DACnB,MAAM,UAAU,QAAQ,IAAI;4DAC5B,MAAM,cAAc,aAAa,QAAQ,IAAI;4DAE7C,qBACE,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,QAAQ,IAAI;gEAClB,WAAW,CAAC,iNAAiN,EAC3N,cACI,iCACA,IACJ;;kFAEF,8OAAC;wEAAQ,WAAU;;;;;;oEAClB,QAAQ,IAAI;;+DATR,QAAQ,IAAI;;;;;wDAYvB;;;;;;;6EAKN,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,kBAAkB,EAC5B,cAAc,SAAS,sBACxB,0LAA0L,EACzL,WAAW,iCAAiC,IAC5C;gDACF,OAAO,CAAC,cAAc,KAAK,IAAI,GAAG;;kEAElC,8OAAC;wDAAK,WAAU;;;;;;oDACf,eAAe,KAAK,IAAI;;;;;;;2CAlFrB,KAAK,IAAI;;;;;oCAuFvB;;;;;;;;;;;;;;;;;;gBA0BL,6BACC,8OAAC;oBACC,WAAU;oBACV,SAAS,IAAM,eAAe;;;;;;8BAKlC,8OAAC;oBACC,WAAW,CAAC,4BAA4B,EACtC,cAAc,kBAAkB,iBAChC;;sCAGF,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;oDACR,OAAM;8DAEN,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;0DAMR,8OAAC;gDAAG,WAAU;;oDAAiF;oDAC9E,MAAM,YAAY;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;0DACnB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDACC,SAAS;gDACT,WAAU;0DAET,UAAU,uBAAS,8OAAC,gMAAA,CAAA,MAAG;oDAAC,MAAM;;;;;yEAAS,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC,4HAAA,CAAA,UAAM;sCACL,cAAA,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}]}