import { Document, Types } from 'mongoose';

export interface Organization {
  _id?: string | Types.ObjectId;
  name: string;
  description?: string;
  status: 'active' | 'inactive' | 'suspended';
  credits: number; // Paid credits
  monthlyFreeCredits: number; // Monthly allowance in minutes
  monthlyFreeCreditsUsed: number; // Used free credits this month
  lastMonthlyReset: Date; // Last time monthly credits were reset
  usingFreeCredits: boolean; // Whether currently using free credits
  monthlyResetDate: number; // Day of month (1-28) when credits reset
  autoRechargeEnabled: boolean;
  autoRechargeThreshold: number;
  autoRechargeAmount: number;
  // Billing configuration settings (moved from global settings)
  callPricePerMinute: number;
  minimumCreditsThreshold: number;
  monthlyMinutesAllowance: number;
  stripeCustomerId?: string;
  // Email notification settings
  fullName?: string; // Client's full name for email personalization
  email?: string; // Email address to send credit notifications to
  lastWarningEmailSent?: Date; // Last time warning email was sent
  lastRunoutEmailSent?: Date; // Last time runout email was sent
  // S3 Configuration for knowledge base file storage
  s3Config?: {
    accessKeyId?: string;
    secretAccessKey?: string;
    region?: string;
    bucketName?: string;
    enabled?: boolean;
  };
  adminUsers: string[]; // User IDs
  users: string[]; // User IDs
  createdAt: Date;
  updatedAt: Date;
}

export type OrganizationDocument = Organization & Document;
