import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, IsOptional, Min, Max, IsString, IsEmail, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class S3ConfigDto {
  @ApiProperty({ description: 'AWS S3 Access Key ID', example: 'AKIAIOSFODNN7EXAMPLE' })
  @IsString()
  @IsOptional()
  accessKeyId?: string;

  @ApiProperty({ description: 'AWS S3 Secret Access Key', example: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY' })
  @IsString()
  @IsOptional()
  secretAccessKey?: string;

  @ApiProperty({ description: 'AWS S3 Region', example: 'us-east-1' })
  @IsString()
  @IsOptional()
  region?: string;

  @ApiProperty({ description: 'S3 Bucket Name', example: 'my-organization-knowledge-base' })
  @IsString()
  @IsOptional()
  bucketName?: string;

  @ApiProperty({ description: 'Enable S3 storage for knowledge base', example: true })
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;
}

export class UpdateOrganizationSettingsDto {
  @ApiProperty({ description: 'Day of month when credits reset (1-28)', example: 1 })
  @IsNumber()
  @Min(1)
  @Max(28)
  @IsOptional()
  monthlyResetDate?: number;

  @ApiProperty({ description: 'Client full name for email personalization', example: 'John Doe' })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty({ description: 'Email address to send credit notifications to', example: '<EMAIL>' })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: 'S3 configuration for knowledge base file storage', type: S3ConfigDto })
  @ValidateNested()
  @Type(() => S3ConfigDto)
  @IsOptional()
  s3Config?: S3ConfigDto;
}
