"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3Service = void 0;
const common_1 = require("@nestjs/common");
const client_s3_1 = require("@aws-sdk/client-s3");
const s3_request_presigner_1 = require("@aws-sdk/s3-request-presigner");
let S3Service = class S3Service {
    createS3Client(config) {
        return new client_s3_1.S3Client({
            region: config.region,
            credentials: {
                accessKeyId: config.accessKeyId.trim(),
                secretAccessKey: config.secretAccessKey.trim(),
            },
            forcePathStyle: false,
            useAccelerateEndpoint: false,
        });
    }
    async testConnection(config) {
        try {
            const s3Client = this.createS3Client(config);
            const command = new client_s3_1.HeadBucketCommand({ Bucket: config.bucketName });
            await s3Client.send(command);
            return {
                success: true,
                message: 'S3 connection successful! Bucket is accessible.',
            };
        }
        catch (error) {
            console.error('S3 connection test failed:', error);
            if (error.name === 'NoSuchBucket') {
                return {
                    success: false,
                    message: 'Bucket does not exist or is not accessible.',
                };
            }
            else if (error.name === 'Forbidden') {
                return {
                    success: false,
                    message: 'Access denied. Check your credentials and bucket permissions.',
                };
            }
            else if (error.name === 'InvalidAccessKeyId') {
                return {
                    success: false,
                    message: 'Invalid Access Key ID.',
                };
            }
            else if (error.name === 'SignatureDoesNotMatch') {
                return {
                    success: false,
                    message: 'Invalid Secret Access Key.',
                };
            }
            else {
                return {
                    success: false,
                    message: `Connection failed: ${error.message || 'Unknown error'}`,
                };
            }
        }
    }
    async uploadFile(config, file, key, contentType, organizationId, knowledgeBaseId) {
        try {
            const s3Client = this.createS3Client(config);
            const fullKey = knowledgeBaseId
                ? `organizations/${organizationId}/knowledge-bases/${knowledgeBaseId}/${key}`
                : `organizations/${organizationId}/files/${key}`;
            const safeContentType = contentType && typeof contentType === 'string'
                ? contentType.trim()
                : 'application/octet-stream';
            const metadata = {
                organizationId: String(organizationId).trim(),
                uploadedAt: new Date().toISOString(),
            };
            if (knowledgeBaseId) {
                metadata.knowledgeBaseId = String(knowledgeBaseId).trim();
            }
            console.log('S3 Upload Parameters:', {
                bucket: config.bucketName,
                key: fullKey,
                contentType: safeContentType,
                metadata,
                fileSize: file.length
            });
            const uploadParams = {
                Bucket: config.bucketName,
                Key: fullKey,
                Body: file,
            };
            if (safeContentType && safeContentType !== 'undefined') {
                uploadParams.ContentType = safeContentType;
            }
            if (Object.keys(metadata).length > 0) {
                uploadParams.Metadata = metadata;
            }
            const command = new client_s3_1.PutObjectCommand(uploadParams);
            await s3Client.send(command);
            const url = `https://${config.bucketName}.s3.${config.region}.amazonaws.com/${fullKey}`;
            return {
                key: fullKey,
                url,
                size: file.length,
            };
        }
        catch (error) {
            console.error('S3 upload failed:', error);
            throw new common_1.InternalServerErrorException(`Failed to upload file to S3: ${error.message}`);
        }
    }
    async deleteFile(config, key) {
        try {
            const s3Client = this.createS3Client(config);
            const command = new client_s3_1.DeleteObjectCommand({
                Bucket: config.bucketName,
                Key: key,
            });
            await s3Client.send(command);
            return {
                success: true,
                message: 'File deleted successfully',
            };
        }
        catch (error) {
            console.error('S3 delete failed:', error);
            throw new common_1.InternalServerErrorException(`Failed to delete file from S3: ${error.message}`);
        }
    }
    async listFiles(config, organizationId, knowledgeBaseId) {
        try {
            const s3Client = this.createS3Client(config);
            const prefix = knowledgeBaseId
                ? `organizations/${organizationId}/knowledge-bases/${knowledgeBaseId}/`
                : `organizations/${organizationId}/files/`;
            const command = new client_s3_1.ListObjectsV2Command({
                Bucket: config.bucketName,
                Prefix: prefix,
            });
            const response = await s3Client.send(command);
            return response.Contents || [];
        }
        catch (error) {
            console.error('S3 list files failed:', error);
            throw new common_1.InternalServerErrorException(`Failed to list files from S3: ${error.message}`);
        }
    }
    async getPresignedUrl(config, key, expiresIn = 3600) {
        try {
            const s3Client = this.createS3Client(config);
            const command = new client_s3_1.GetObjectCommand({
                Bucket: config.bucketName,
                Key: key,
            });
            const url = await (0, s3_request_presigner_1.getSignedUrl)(s3Client, command, { expiresIn });
            return url;
        }
        catch (error) {
            console.error('S3 presigned URL generation failed:', error);
            throw new common_1.InternalServerErrorException(`Failed to generate presigned URL: ${error.message}`);
        }
    }
    async getFileMetadata(config, key) {
        try {
            const s3Client = this.createS3Client(config);
            const command = new client_s3_1.GetObjectCommand({
                Bucket: config.bucketName,
                Key: key,
            });
            const response = await s3Client.send(command);
            return {
                contentType: response.ContentType,
                contentLength: response.ContentLength,
                lastModified: response.LastModified,
                metadata: response.Metadata,
            };
        }
        catch (error) {
            console.error('S3 get metadata failed:', error);
            throw new common_1.InternalServerErrorException(`Failed to get file metadata: ${error.message}`);
        }
    }
};
exports.S3Service = S3Service;
exports.S3Service = S3Service = __decorate([
    (0, common_1.Injectable)()
], S3Service);
//# sourceMappingURL=s3.service.js.map