"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Eye, EyeOff, Database, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { updateOrganizationSettings, Organization } from "@/app/api/organizations";

interface S3SettingsProps {
  organization: Organization | null;
  organizations?: Organization[];
  onUpdate?: (organization: Organization) => void;
  onOrganizationChange?: (organization: Organization) => void;
}

export function S3Settings({ organization, organizations, onUpdate, onOrganizationChange }: S3SettingsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    enabled: false,
    accessKeyId: "",
    secretAccessKey: "",
    region: "us-east-1",
    bucketName: "",
  });

  // Load organization S3 config
  useEffect(() => {
    if (organization?.s3Config) {
      setFormData({
        enabled: organization.s3Config.enabled || false,
        accessKeyId: organization.s3Config.accessKeyId || "",
        secretAccessKey: organization.s3Config.secretAccessKey || "",
        region: organization.s3Config.region || "us-east-1",
        bucketName: organization.s3Config.bucketName || "",
      });
    }
  }, [organization]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    if (!organization) {
      toast.error("No organization selected");
      return;
    }

    // Validation
    if (formData.enabled) {
      if (!formData.accessKeyId.trim()) {
        toast.error("Access Key ID is required when S3 is enabled");
        return;
      }
      if (!formData.secretAccessKey.trim()) {
        toast.error("Secret Access Key is required when S3 is enabled");
        return;
      }
      if (!formData.region.trim()) {
        toast.error("Region is required when S3 is enabled");
        return;
      }
      if (!formData.bucketName.trim()) {
        toast.error("Bucket Name is required when S3 is enabled");
        return;
      }
    }

    setIsLoading(true);
    try {
      const updatedOrg = await updateOrganizationSettings(organization._id, {
        s3Config: formData
      });

      toast.success("S3 settings saved successfully");
      onUpdate?.(updatedOrg);
    } catch (error) {
      console.error("Failed to save S3 settings:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save S3 settings");
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    if (!formData.accessKeyId || !formData.secretAccessKey || !formData.region || !formData.bucketName) {
      toast.error("Please fill in all S3 credentials before testing");
      return;
    }

    setTestingConnection(true);
    try {
      // This would be a real API call to test S3 connection
      // For now, we'll simulate it
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success("S3 connection test successful!");
    } catch (error) {
      toast.error("S3 connection test failed. Please check your credentials.");
    } finally {
      setTestingConnection(false);
    }
  };

  const awsRegions = [
    { value: "us-east-1", label: "US East (N. Virginia)" },
    { value: "us-east-2", label: "US East (Ohio)" },
    { value: "us-west-1", label: "US West (N. California)" },
    { value: "us-west-2", label: "US West (Oregon)" },
    { value: "eu-west-1", label: "Europe (Ireland)" },
    { value: "eu-west-2", label: "Europe (London)" },
    { value: "eu-west-3", label: "Europe (Paris)" },
    { value: "eu-central-1", label: "Europe (Frankfurt)" },
    { value: "ap-southeast-1", label: "Asia Pacific (Singapore)" },
    { value: "ap-southeast-2", label: "Asia Pacific (Sydney)" },
    { value: "ap-northeast-1", label: "Asia Pacific (Tokyo)" },
    { value: "ap-south-1", label: "Asia Pacific (Mumbai)" },
  ];

  if (!organization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Organization Selected
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Please select an organization to configure S3 settings
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Organization Selector */}
      {organizations && organizations.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Select Organization</CardTitle>
            <CardDescription>
              Choose which organization to configure S3 settings for
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="organization-select">Organization</Label>
              <select
                id="organization-select"
                value={organization?._id || ""}
                onChange={(e) => {
                  const selectedOrg = organizations.find(org => org._id === e.target.value);
                  if (selectedOrg && onOrganizationChange) {
                    onOrganizationChange(selectedOrg);
                  }
                }}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {organizations.map((org) => (
                  <option key={org._id} value={org._id}>
                    {org.name}
                  </option>
                ))}
              </select>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>S3 Storage Configuration</span>
          </CardTitle>
          <CardDescription>
            Configure AWS S3 storage for knowledge base files. Each organization can have its own S3 bucket for secure file storage.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable S3 Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Enable S3 Storage</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Enable AWS S3 storage for this organization's knowledge base files
              </p>
            </div>
            <Switch
              checked={formData.enabled}
              onCheckedChange={(checked) => handleInputChange("enabled", checked)}
            />
          </div>

          {formData.enabled && (
            <>
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Make sure your AWS credentials have the necessary permissions for S3 operations (s3:GetObject, s3:PutObject, s3:DeleteObject, s3:ListBucket).
                </AlertDescription>
              </Alert>

              {/* AWS Credentials */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="accessKeyId">Access Key ID *</Label>
                  <Input
                    id="accessKeyId"
                    type="text"
                    placeholder="AKIAIOSFODNN7EXAMPLE"
                    value={formData.accessKeyId}
                    onChange={(e) => handleInputChange("accessKeyId", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="secretAccessKey">Secret Access Key *</Label>
                  <div className="relative">
                    <Input
                      id="secretAccessKey"
                      type={showSecretKey ? "text" : "password"}
                      placeholder="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                      value={formData.secretAccessKey}
                      onChange={(e) => handleInputChange("secretAccessKey", e.target.value)}
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowSecretKey(!showSecretKey)}
                    >
                      {showSecretKey ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="region">AWS Region *</Label>
                  <select
                    id="region"
                    value={formData.region}
                    onChange={(e) => handleInputChange("region", e.target.value)}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {awsRegions.map((region) => (
                      <option key={region.value} value={region.value}>
                        {region.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bucketName">Bucket Name *</Label>
                  <Input
                    id="bucketName"
                    type="text"
                    placeholder="my-organization-knowledge-base"
                    value={formData.bucketName}
                    onChange={(e) => handleInputChange("bucketName", e.target.value)}
                  />
                </div>
              </div>

              {/* Test Connection */}
              <div className="flex items-center space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={testConnection}
                  disabled={testingConnection || !formData.accessKeyId || !formData.secretAccessKey}
                >
                  {testingConnection ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Testing Connection...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Test Connection
                    </>
                  )}
                </Button>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Test your S3 credentials and bucket access
                </p>
              </div>
            </>
          )}

          {/* Save Button */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save S3 Settings"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">How it works</CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
          <p>• Files uploaded to the knowledge base will be stored in your configured S3 bucket</p>
          <p>• Each organization uses its own S3 configuration for data isolation</p>
          <p>• Files are organized in folders by knowledge base ID within your bucket</p>
          <p>• Make sure your bucket has appropriate CORS settings for web uploads</p>
        </CardContent>
      </Card>
    </div>
  );
}
