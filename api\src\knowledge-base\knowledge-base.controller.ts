import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes, ApiBody, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { KnowledgeBaseService } from './knowledge-base.service';
import { CreateKnowledgeBaseFolderDto, UpdateKnowledgeBaseFolderDto, CreateKnowledgeBaseItemDto, UpdateKnowledgeBaseItemDto } from './dto/knowledge-base.dto';
import { OrganizationsService } from '../organizations/organizations.service';

interface RequestWithUser extends Request {
  user: {
    userId: string;
    email: string;
    role: string;
  };
}

@ApiTags('Knowledge Base')
@Controller('knowledge-base')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class KnowledgeBaseController {
  constructor(
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly organizationsService: OrganizationsService,
  ) {}

  // Folder endpoints
  @Post('organizations/:organizationId/folders')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Create a new knowledge base folder' })
  @ApiResponse({ status: 201, description: 'Folder created successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  async createFolder(
    @Param('organizationId') organizationId: string,
    @Body() createFolderDto: CreateKnowledgeBaseFolderDto,
    @Req() req: RequestWithUser,
  ) {
    // Check if user has access to this organization
    await this.checkOrganizationAccess(req.user, organizationId);

    const folder = await this.knowledgeBaseService.createFolder(
      organizationId,
      req.user.userId,
      createFolderDto
    );

    return {
      success: true,
      message: 'Folder created successfully',
      data: folder,
    };
  }

  @Get('organizations/:organizationId/folders')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Get all folders for an organization' })
  @ApiResponse({ status: 200, description: 'Folders retrieved successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  async getFolders(
    @Param('organizationId') organizationId: string,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    const folders = await this.knowledgeBaseService.getFoldersByOrganization(organizationId);

    return {
      success: true,
      message: 'Folders retrieved successfully',
      data: folders,
    };
  }

  @Get('organizations/:organizationId/folders/:folderId')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Get a specific folder' })
  @ApiResponse({ status: 200, description: 'Folder retrieved successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'folderId', description: 'Folder ID' })
  async getFolder(
    @Param('organizationId') organizationId: string,
    @Param('folderId') folderId: string,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    const folder = await this.knowledgeBaseService.getFolderById(folderId, organizationId);

    return {
      success: true,
      message: 'Folder retrieved successfully',
      data: folder,
    };
  }

  @Put('organizations/:organizationId/folders/:folderId')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Update a folder' })
  @ApiResponse({ status: 200, description: 'Folder updated successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'folderId', description: 'Folder ID' })
  async updateFolder(
    @Param('organizationId') organizationId: string,
    @Param('folderId') folderId: string,
    @Body() updateFolderDto: UpdateKnowledgeBaseFolderDto,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    const folder = await this.knowledgeBaseService.updateFolder(
      folderId,
      organizationId,
      updateFolderDto
    );

    return {
      success: true,
      message: 'Folder updated successfully',
      data: folder,
    };
  }

  @Delete('organizations/:organizationId/folders/:folderId')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Delete a folder and all its items' })
  @ApiResponse({ status: 200, description: 'Folder deleted successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'folderId', description: 'Folder ID' })
  async deleteFolder(
    @Param('organizationId') organizationId: string,
    @Param('folderId') folderId: string,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    await this.knowledgeBaseService.deleteFolder(folderId, organizationId);

    return {
      success: true,
      message: 'Folder deleted successfully',
    };
  }

  // Item endpoints
  @Post('organizations/:organizationId/items')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Create a new knowledge base item' })
  @ApiResponse({ status: 201, description: 'Item created successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  async createItem(
    @Param('organizationId') organizationId: string,
    @Body() createItemDto: CreateKnowledgeBaseItemDto,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    const item = await this.knowledgeBaseService.createItem(
      organizationId,
      req.user.userId,
      createItemDto
    );

    return {
      success: true,
      message: 'Item created successfully',
      data: item,
    };
  }

  @Post('organizations/:organizationId/items/:itemId/upload')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Upload document file for a knowledge base item' })
  @ApiResponse({ status: 200, description: 'File uploaded successfully' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'itemId', description: 'Knowledge base item ID' })
  async uploadDocument(
    @Param('organizationId') organizationId: string,
    @Param('itemId') itemId: string,
    @UploadedFile() file: Express.Multer.File,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size exceeds 50MB limit');
    }

    const item = await this.knowledgeBaseService.uploadDocumentToS3(
      itemId,
      organizationId,
      file.buffer,
      file.originalname,
      file.mimetype
    );

    return {
      success: true,
      message: 'File uploaded successfully',
      data: item,
    };
  }

  @Get('organizations/:organizationId/folders/:folderId/items')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Get all items in a folder' })
  @ApiResponse({ status: 200, description: 'Items retrieved successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'folderId', description: 'Folder ID' })
  async getItemsByFolder(
    @Param('organizationId') organizationId: string,
    @Param('folderId') folderId: string,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    const items = await this.knowledgeBaseService.getItemsByFolder(folderId, organizationId);

    return {
      success: true,
      message: 'Items retrieved successfully',
      data: items,
    };
  }

  @Get('organizations/:organizationId/items/:itemId')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Get a specific item' })
  @ApiResponse({ status: 200, description: 'Item retrieved successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'itemId', description: 'Item ID' })
  async getItem(
    @Param('organizationId') organizationId: string,
    @Param('itemId') itemId: string,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    const item = await this.knowledgeBaseService.getItemById(itemId, organizationId);

    return {
      success: true,
      message: 'Item retrieved successfully',
      data: item,
    };
  }

  @Put('organizations/:organizationId/items/:itemId')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Update an item' })
  @ApiResponse({ status: 200, description: 'Item updated successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'itemId', description: 'Item ID' })
  async updateItem(
    @Param('organizationId') organizationId: string,
    @Param('itemId') itemId: string,
    @Body() updateItemDto: UpdateKnowledgeBaseItemDto,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    const item = await this.knowledgeBaseService.updateItem(
      itemId,
      organizationId,
      updateItemDto
    );

    return {
      success: true,
      message: 'Item updated successfully',
      data: item,
    };
  }

  @Delete('organizations/:organizationId/items/:itemId')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Delete an item' })
  @ApiResponse({ status: 200, description: 'Item deleted successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'itemId', description: 'Item ID' })
  async deleteItem(
    @Param('organizationId') organizationId: string,
    @Param('itemId') itemId: string,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    await this.knowledgeBaseService.deleteItem(itemId, organizationId);

    return {
      success: true,
      message: 'Item deleted successfully',
    };
  }

  @Get('organizations/:organizationId/items/:itemId/download')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Get presigned download URL for a document' })
  @ApiResponse({ status: 200, description: 'Download URL generated successfully' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'itemId', description: 'Item ID' })
  async getDownloadUrl(
    @Param('organizationId') organizationId: string,
    @Param('itemId') itemId: string,
    @Req() req: RequestWithUser,
  ) {
    await this.checkOrganizationAccess(req.user, organizationId);

    const url = await this.knowledgeBaseService.getPresignedDownloadUrl(itemId, organizationId);

    return {
      success: true,
      message: 'Download URL generated successfully',
      data: { url },
    };
  }

  // Helper method to check organization access
  private async checkOrganizationAccess(user: any, organizationId: string): Promise<void> {
    if (user.role !== 'superadmin') {
      const userAdminOrgs = await this.organizationsService.findByAdmin(user.userId);
      const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);

      if (!hasAccess) {
        throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
      }
    }
  }
}
