import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { OrganizationsController } from './organizations.controller';
import { OrganizationsService } from './organizations.service';
import { OrganizationSchema } from './schemas/organization.schema';
import { UsersModule } from '../users/users.module';
import { UserSchema } from '../users/schemas/user.schema';
import { CreditModule } from '../credit/credit.module';
import { MonthlyCreditsService } from './monthly-credits.service';
import { GlobalSettingsModule } from '../global-settings/global-settings.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { S3Module } from '../s3/s3.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Organization', schema: OrganizationSchema },
      { name: 'User', schema: UserSchema },
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => CreditModule),
    GlobalSettingsModule,
    NotificationsModule,
    S3Module,
  ],
  controllers: [OrganizationsController],
  providers: [OrganizationsService, MonthlyCreditsService],
  exports: [OrganizationsService, MonthlyCreditsService],
})
export class OrganizationsModule {}
