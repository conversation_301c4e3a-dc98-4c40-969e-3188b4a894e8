{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/lib/validations/authSchema.ts"], "sourcesContent": ["\r\nimport { z } from \"zod\";\r\n\r\nexport const registerSchema = z.object({\r\n  fullName: z.string().min(2, { message: \"Full name must be at least 2 characters\" }),\r\n  email: z.string().email({ message: \"Please enter a valid email address\" }),\r\n  password: z.string().min(3, { message: \"Password must be at least 6 characters\" }),\r\n});\r\n\r\nexport const loginSchema = z.object({\r\n  email: z.string().email({ message: \"Please enter a valid email address\" }),\r\n  password: z.string().min(1, { message: \"Password is required\" }),\r\n});\r\n\r\nexport type RegisterInput = z.infer<typeof registerSchema>;\r\nexport type LoginInput = z.infer<typeof loginSchema>;"], "names": [], "mappings": ";;;;AACA;;AAEO,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA0C;IACjF,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAAqC;IACxE,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAyC;AAClF;AAEO,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAAqC;IACxE,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB;AAChE", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28auth%29/actions/auth.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\"use server\";\r\nimport { cookies } from \"next/headers\";\r\nimport { registerSchema, loginSchema } from \"@/lib/validations/authSchema\";\r\n\r\n\r\n\r\nexport type ActionResult = {\r\n  success: boolean;\r\n  message: string;\r\n  fieldErrors?: Record<string, string[]>;\r\n  redirect?: string;\r\n  tokens?: {\r\n    access_token: string;\r\n    refresh_token: string;\r\n  };\r\n};\r\n \r\nexport type UserInfo = {\r\n  fullName: string;\r\n  userId: string;\r\n  email: string;\r\n  role: string;\r\n};\r\n \r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n \r\n \r\nexport async function registerUser(formData: FormData): Promise<ActionResult> {\r\n    // Extract data from form\r\n    const rawData = {\r\n      fullName: formData.get(\"fullName\"),\r\n      email: formData.get(\"email\"),\r\n      password: formData.get(\"password\"),\r\n    };\r\n \r\n    // Validate with Zod\r\n    const validationResult = registerSchema.safeParse(rawData);\r\n   \r\n    if (!validationResult.success) {\r\n      // Return validation errors\r\n      return {\r\n        success: false,\r\n        message: \"Validation failed\",\r\n        fieldErrors: validationResult.error.flatten().fieldErrors,\r\n      };\r\n    }\r\n \r\n    // Validation passed, destructure the validated data\r\n    const { fullName, email, password } = validationResult.data;\r\n \r\n    try {\r\n      // Make API request to registration endpoint\r\n      const response = await fetch(`${API_BASE_URL}/api/users/register`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          fullName,\r\n          email,\r\n          password,\r\n        }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      // First check if response is ok\r\n      if (!response.ok) {\r\n        try {\r\n          // Try to parse as JSON for error details\r\n          const errorData = await response.json();\r\n          return {\r\n            success: false,\r\n            message: errorData.message || \"Registration failed. Please try again.\",\r\n          };\r\n        } catch {\r\n          // If not JSON, get text or use statusText\r\n          const errorText = await response.text().catch(() => response.statusText);\r\n          return {\r\n            success: false,\r\n            message: errorText || \"Registration failed. Please try again.\",\r\n          };\r\n        }\r\n      }\r\n \r\n      // For successful responses, get the text content\r\n      const successText = await response.text();\r\n     \r\n      return {\r\n        success: true,\r\n        message: successText || \"User registered successfully! Awaiting admin approval.\"\r\n      };\r\n \r\n    } catch (error) {\r\n      console.error(\"Registration error:\", error);\r\n      return {\r\n        success: false,\r\n        message: \"An unexpected error occurred. Please try again later.\"\r\n      };\r\n    }\r\n  }\r\n \r\nexport async function loginUser(formData: FormData): Promise<ActionResult> {\r\n    const rawData = {\r\n      email: formData.get(\"email\"),\r\n      password: formData.get(\"password\"),\r\n    };\r\n \r\n    // Validate with Zod\r\n    const validationResult = loginSchema.safeParse(rawData);\r\n   \r\n    if (!validationResult.success) {\r\n      // Return validation errors\r\n      return {\r\n        success: false,\r\n        message: \"Validation failed\",\r\n        fieldErrors: validationResult.error.flatten().fieldErrors,\r\n      };\r\n    }\r\n \r\n    // Validation passed, destructure the validated data\r\n    const { email, password } = validationResult.data;\r\n \r\n    try {\r\n      // Make API request to login endpoint\r\n      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          email,\r\n          password,\r\n        }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      // Check if the response is OK\r\n      if (!response.ok) {\r\n        try {\r\n          const errorData = await response.json();\r\n          return {\r\n            success: false,\r\n            message: errorData.message || \"Login failed. Please check your credentials.\",\r\n          };\r\n        } catch {\r\n          return {\r\n            success: false,\r\n            message: response.statusText || \"Login failed. Please try again.\",\r\n          };\r\n        }\r\n      }\r\n \r\n      // Parse the successful response to get tokens\r\n      try {\r\n        const data = await response.json();\r\n       \r\n        if (data.access_token && data.refresh_token) {\r\n          // Return tokens in the response rather than setting cookies\r\n          return {\r\n            success: true,\r\n            message: \"Login successful!\",\r\n            redirect: \"/dashboard\",\r\n            tokens: {\r\n              access_token: data.access_token,\r\n              refresh_token: data.refresh_token\r\n            }\r\n          };\r\n        } else {\r\n          return {\r\n            success: false,\r\n            message: \"Invalid response from server. Missing authentication tokens.\",\r\n          };\r\n        }\r\n      } catch (jsonError) {\r\n        return {\r\n          success: false,\r\n          message: \"Failed to process login response.\",\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Login error:\", error);\r\n      return {\r\n        success: false,\r\n        message: \"An unexpected error occurred. Please try again later.\"\r\n      };\r\n    }\r\n  }\r\n \r\n  // Add a refresh token function for later use\r\n  export async function refreshAccessToken(): Promise<{\r\n    success: boolean;\r\n    newAccessToken?: string;\r\n  }> {\r\n   \r\n    // Get the refresh token from cookies\r\n    const refreshToken = (await cookies()).get(\"refresh_token\")?.value;\r\n \r\n   \r\n    if (!refreshToken) {\r\n      // Without a refresh token, we can't refresh the session\r\n      return { success: false };\r\n    }\r\n \r\n    try {\r\n      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"Authorization\": `Bearer ${refreshToken}`,\r\n        },\r\n        body: JSON.stringify({ refreshToken }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      if (!response.ok) {\r\n        // If the refresh token is invalid or expired, user needs to log in again\r\n        return { success: false };\r\n      }\r\n \r\n      const data = await response.json();\r\n     \r\n      if (data.access_token) {\r\n        // Update the access token in cookies\r\n        (await\r\n          // Update the access token in cookies\r\n          cookies()).set(\"access_token\", data.access_token, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          path: \"/\",\r\n          sameSite: \"lax\",\r\n          maxAge: 60 * 60, // 1 hour\r\n        });\r\n \r\n        return {\r\n          success: true,\r\n          newAccessToken: data.access_token,\r\n        };\r\n      }\r\n     \r\n      return { success: false };\r\n    } catch (error) {\r\n      console.error(\"Token refresh error:\", error);\r\n      return { success: false };\r\n    }\r\n  }\r\n \r\n// Function to get current user information\r\nexport async function getCurrentUser(): Promise<{\r\n  success: boolean;\r\n  user?: UserInfo;\r\n  error?: string;\r\n}> {\r\n  let accessToken;\r\n \r\n  if (typeof window !== 'undefined') {\r\n    // We're in a browser\r\n    accessToken = localStorage.getItem('access_token');\r\n  } else {\r\n    // We're on the server\r\n    accessToken = (await cookies()).get(\"access_token\")?.value;\r\n  }\r\n \r\n  if (!accessToken) {\r\n    console.log(\"No access token found\");\r\n    return {\r\n      success: false,\r\n      error: \"Not authenticated\"\r\n    };\r\n  }\r\n \r\n  try {\r\n \r\n   \r\n    const response = await fetch(`${API_BASE_URL}/api/auth/me`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Authorization\": `Bearer ${accessToken}`,\r\n      },\r\n      cache: \"no-store\",\r\n    });\r\n \r\n \r\n    if (!response.ok) {\r\n      // If response is 401 or 403, try to refresh the token\r\n      if (response.status === 401 || response.status === 403) {\r\n        // Attempt to refresh the token\r\n        const refreshResult = await refreshAccessToken();\r\n       \r\n        if (refreshResult.success) {\r\n          // Retry with new token\r\n          const retryResponse = await fetch(`${API_BASE_URL}/api/auth/me`, {\r\n            method: \"GET\",\r\n            headers: {\r\n              \"Authorization\": `Bearer ${refreshResult.newAccessToken}`,\r\n            },\r\n            cache: \"no-store\",\r\n          });\r\n \r\n          if (retryResponse.ok) {\r\n            const userData = await retryResponse.json();\r\n           \r\n            // Check if we got actual user data back (not empty object)\r\n            if (userData && userData.userId && userData.email) {\r\n              return {\r\n                success: true,\r\n                user: userData\r\n              };\r\n            } else {\r\n              return {\r\n                success: false,\r\n                error: \"Account not authorized or pending approval\"\r\n              };\r\n            }\r\n          }\r\n        }\r\n       \r\n        // If refresh failed or retry failed\r\n        return {\r\n          success: false,\r\n          error: \"Not authorized\"\r\n        };\r\n      }\r\n     \r\n      // For other errors\r\n      return {\r\n        success: false,\r\n        error: `Error: ${response.status}`\r\n      };\r\n    }\r\n \r\n    // If first attempt was successful\r\n    const userData = await response.json();\r\n    // This confirms the user has proper approval, not just valid tokens\r\n    if (userData && userData.userId && userData.email) {\r\n      return {\r\n        success: true,\r\n        user: userData\r\n      };\r\n    } else {\r\n      // User has valid tokens but no data - likely pending approval\r\n      return {\r\n        success: false,\r\n        error: \"Account not authorized or pending approval\"\r\n      };\r\n    }\r\n \r\n  } catch (error) {\r\n    console.error(\"Error fetching user data:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An error occurred while fetching user data\"\r\n    };\r\n  }\r\n}\r\n \r\nexport async function logoutUser(): Promise<ActionResult> {\r\n  try {\r\n    // Clear the cookies\r\n    const cookieStore = await cookies();\r\n    cookieStore.delete(\"access_token\");\r\n    cookieStore.delete(\"refresh_token\");\r\n   \r\n    return {\r\n      success: true,\r\n      message: \"Logged out successfully\",\r\n      redirect: \"/\"\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Logout error:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"An error occurred during logout\"\r\n    };\r\n  }\r\n}"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;;;;AAEpD;AACA;;;;;;AAsBA,MAAM,eAAe,6DAAsC;AAGpD,eAAe,uCAAU,GAAV,aAAa,QAAkB;IACjD,yBAAyB;IACzB,MAAM,UAAU;QACd,UAAU,SAAS,GAAG,CAAC;QACvB,OAAO,SAAS,GAAG,CAAC;QACpB,UAAU,SAAS,GAAG,CAAC;IACzB;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,uIAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;IAElD,IAAI,CAAC,iBAAiB,OAAO,EAAE;QAC7B,2BAA2B;QAC3B,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa,iBAAiB,KAAK,CAAC,OAAO,GAAG,WAAW;QAC3D;IACF;IAEA,oDAAoD;IACpD,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,iBAAiB,IAAI;IAE3D,IAAI;QACF,4CAA4C;QAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,mBAAmB,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA;YACF;YACA,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI;gBACF,yCAAyC;gBACzC,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,OAAO;oBACL,SAAS;oBACT,SAAS,UAAU,OAAO,IAAI;gBAChC;YACF,EAAE,OAAM;gBACN,0CAA0C;gBAC1C,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,SAAS,UAAU;gBACvE,OAAO;oBACL,SAAS;oBACT,SAAS,aAAa;gBACxB;YACF;QACF;QAEA,iDAAiD;QACjD,MAAM,cAAc,MAAM,SAAS,IAAI;QAEvC,OAAO;YACL,SAAS;YACT,SAAS,eAAe;QAC1B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEK,eAAe,uCAAO,GAAP,UAAU,QAAkB;IAC9C,MAAM,UAAU;QACd,OAAO,SAAS,GAAG,CAAC;QACpB,UAAU,SAAS,GAAG,CAAC;IACzB;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,uIAAA,CAAA,cAAW,CAAC,SAAS,CAAC;IAE/C,IAAI,CAAC,iBAAiB,OAAO,EAAE;QAC7B,2BAA2B;QAC3B,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa,iBAAiB,KAAK,CAAC,OAAO,GAAG,WAAW;QAC3D;IACF;IAEA,oDAAoD;IACpD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,iBAAiB,IAAI;IAEjD,IAAI;QACF,qCAAqC;QACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;YACF;YACA,OAAO;QACT;QAEA,8BAA8B;QAC9B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI;gBACF,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,OAAO;oBACL,SAAS;oBACT,SAAS,UAAU,OAAO,IAAI;gBAChC;YACF,EAAE,OAAM;gBACN,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,UAAU,IAAI;gBAClC;YACF;QACF;QAEA,8CAA8C;QAC9C,IAAI;YACF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,YAAY,IAAI,KAAK,aAAa,EAAE;gBAC3C,4DAA4D;gBAC5D,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,UAAU;oBACV,QAAQ;wBACN,cAAc,KAAK,YAAY;wBAC/B,eAAe,KAAK,aAAa;oBACnC;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,WAAW;YAClB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAGO,eAAe,uCAAgB,GAAhB;IAKpB,qCAAqC;IACrC,MAAM,eAAe,CAAC,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CAAC,kBAAkB;IAG7D,IAAI,CAAC,cAAc;QACjB,wDAAwD;QACxD,OAAO;YAAE,SAAS;QAAM;IAC1B;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC,EAAE;YAC/D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,cAAc;YAC3C;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAa;YACpC,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,yEAAyE;YACzE,OAAO;gBAAE,SAAS;YAAM;QAC1B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,YAAY,EAAE;YACrB,qCAAqC;YACrC,CAAC,MACC,qCAAqC;YACrC,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CAAC,gBAAgB,KAAK,YAAY,EAAE;gBAClD,UAAU;gBACV,QAAQ,oDAAyB;gBACjC,MAAM;gBACN,UAAU;gBACV,QAAQ,KAAK;YACf;YAEA,OAAO;gBACL,SAAS;gBACT,gBAAgB,KAAK,YAAY;YACnC;QACF;QAEA,OAAO;YAAE,SAAS;QAAM;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YAAE,SAAS;QAAM;IAC1B;AACF;AAGK,eAAe,uCAAY,GAAZ;IAKpB,IAAI;IAEJ,uCAAmC;;IAGnC,OAAO;QACL,sBAAsB;QACtB,cAAc,CAAC,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,GAAG,EAAE,GAAG,CAAC,iBAAiB;IACvD;IAEA,IAAI,CAAC,aAAa;QAChB,QAAQ,GAAG,CAAC;QACZ,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QAGF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;YAC1C;YACA,OAAO;QACT;QAGA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,sDAAsD;YACtD,IAAI,SAAS,MAAM,KAAK,OAAO,SAAS,MAAM,KAAK,KAAK;gBACtD,+BAA+B;gBAC/B,MAAM,gBAAgB,MAAM;gBAE5B,IAAI,cAAc,OAAO,EAAE;oBACzB,uBAAuB;oBACvB,MAAM,gBAAgB,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;wBAC/D,QAAQ;wBACR,SAAS;4BACP,iBAAiB,CAAC,OAAO,EAAE,cAAc,cAAc,EAAE;wBAC3D;wBACA,OAAO;oBACT;oBAEA,IAAI,cAAc,EAAE,EAAE;wBACpB,MAAM,WAAW,MAAM,cAAc,IAAI;wBAEzC,2DAA2D;wBAC3D,IAAI,YAAY,SAAS,MAAM,IAAI,SAAS,KAAK,EAAE;4BACjD,OAAO;gCACL,SAAS;gCACT,MAAM;4BACR;wBACF,OAAO;4BACL,OAAO;gCACL,SAAS;gCACT,OAAO;4BACT;wBACF;oBACF;gBACF;gBAEA,oCAAoC;gBACpC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,OAAO,EAAE,SAAS,MAAM,EAAE;YACpC;QACF;QAEA,kCAAkC;QAClC,MAAM,WAAW,MAAM,SAAS,IAAI;QACpC,oEAAoE;QACpE,IAAI,YAAY,SAAS,MAAM,IAAI,SAAS,KAAK,EAAE;YACjD,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,OAAO;YACL,8DAA8D;YAC9D,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEO,eAAe,uCAAQ,GAAR;IACpB,IAAI;QACF,oBAAoB;QACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,YAAY,MAAM,CAAC;QACnB,YAAY,MAAM,CAAC;QAEnB,OAAO;YACL,SAAS;YACT,SAAS;YACT,UAAU;QACZ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;;;IA3VsB;IA0EA;IAwFE;IA0DF;IA4GA;;AAxUA,+OAAA;AA0EA,+OAAA;AAwFE,+OAAA;AA0DF,+OAAA;AA4GA,+OAAA", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28workspace%29/workspaces/OrganizationsContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2T,GACxV,yFACA", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28workspace%29/workspaces/OrganizationsContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28workspace%29/workspaces/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport OrganizationsContent from './OrganizationsContent';\r\n\r\nexport default function OrganizationsPage() {\r\n  return <OrganizationsContent />;\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,kKAAA,CAAA,UAAoB;;;;;AAC9B", "debugId": null}}]}