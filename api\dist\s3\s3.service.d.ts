export interface S3Config {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    bucketName: string;
}
export interface UploadResult {
    key: string;
    url: string;
    size: number;
}
export declare class S3Service {
    private createS3Client;
    testConnection(config: S3Config): Promise<{
        success: boolean;
        message: string;
    }>;
    uploadFile(config: S3Config, file: Buffer, key: string, contentType: string, organizationId: string, knowledgeBaseId?: string): Promise<UploadResult>;
    deleteFile(config: S3Config, key: string): Promise<{
        success: boolean;
        message: string;
    }>;
    listFiles(config: S3Config, organizationId: string, knowledgeBaseId?: string): Promise<any[]>;
    getPresignedUrl(config: S3Config, key: string, expiresIn?: number): Promise<string>;
    getFileMetadata(config: S3Config, key: string): Promise<any>;
}
