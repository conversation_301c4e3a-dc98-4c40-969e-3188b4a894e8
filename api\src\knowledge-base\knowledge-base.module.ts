import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { KnowledgeBaseController } from './knowledge-base.controller';
import { KnowledgeBaseService } from './knowledge-base.service';
import { KnowledgeBaseFolderSchema, KnowledgeBaseItemSchema } from './schemas/knowledge-base.schema';
import { S3Module } from '../s3/s3.module';
import { OrganizationsModule } from '../organizations/organizations.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'KnowledgeBaseFolder', schema: KnowledgeBaseFolderSchema },
      { name: 'KnowledgeBaseItem', schema: KnowledgeBaseItemSchema },
    ]),
    S3Module,
    forwardRef(() => OrganizationsModule),
  ],
  controllers: [KnowledgeBaseController],
  providers: [KnowledgeBaseService],
  exports: [KnowledgeBaseService],
})
export class KnowledgeBaseModule {}
