{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border \",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\"flex flex-col gap-1.5 px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gXACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sbACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/config.ts"], "sourcesContent": ["// API Configuration\r\nexport const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';\r\n// Stripe Configuration\r\nexport const STRIPE_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '';\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;AACb,MAAM,UAAU,6DAAsC;AAEtD,MAAM,yBAAyB,mJAAkD", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/api/organizations.ts"], "sourcesContent": ["import { API_URL } from \"./config\";\r\n\r\n// Types\r\nexport interface Organization {\r\n  _id: string;\r\n  name: string;\r\n  description?: string;\r\n  status: 'active' | 'inactive' | 'suspended';\r\n  credits: number; // Paid credits\r\n  monthlyFreeCredits: number; // Monthly allowance in minutes\r\n  monthlyFreeCreditsUsed: number; // Used free credits this month\r\n  lastMonthlyReset: string; // Last time monthly credits were reset\r\n  usingFreeCredits: boolean; // Whether currently using free credits\r\n  autoRechargeEnabled: boolean;\r\n  autoRechargeThreshold: number;\r\n  autoRechargeAmount: number;\r\n  // Billing configuration settings (moved from global settings)\r\n  callPricePerMinute: number;\r\n  minimumCreditsThreshold: number;\r\n  monthlyMinutesAllowance: number;\r\n  stripeCustomerId?: string;\r\n  // Email notification settings\r\n  fullName?: string; // Client's full name for email personalization\r\n  email?: string; // Email address to send credit notifications to\r\n  lastWarningEmailSent?: string; // Last time warning email was sent\r\n  lastRunoutEmailSent?: string; // Last time runout email was sent\r\n  // S3 Configuration for knowledge base file storage\r\n  s3Config?: {\r\n    accessKeyId?: string;\r\n    secretAccessKey?: string;\r\n    region?: string;\r\n    bucketName?: string;\r\n    enabled?: boolean;\r\n  };\r\n  adminUsers: string[];\r\n  users: string[];\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface CreateOrganizationRequest {\r\n  name: string;\r\n  description?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  adminUsers?: string[];\r\n}\r\n\r\nexport interface UpdateOrganizationRequest {\r\n  name?: string;\r\n  description?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  adminUsers?: string[];\r\n  users?: string[];\r\n}\r\n\r\nexport interface UpdateOrganizationBillingRequest {\r\n  credits?: number;\r\n  autoRechargeEnabled?: boolean;\r\n  autoRechargeThreshold?: number;\r\n  autoRechargeAmount?: number;\r\n  callPricePerMinute?: number;\r\n  minimumCreditsThreshold?: number;\r\n  monthlyMinutesAllowance?: number;\r\n  monthlyResetDate?: number;\r\n}\r\n\r\nexport interface UpdateOrganizationSettingsRequest {\r\n  monthlyResetDate?: number;\r\n  fullName?: string;\r\n  email?: string;\r\n  s3Config?: {\r\n    accessKeyId?: string;\r\n    secretAccessKey?: string;\r\n    region?: string;\r\n    bucketName?: string;\r\n    enabled?: boolean;\r\n  };\r\n}\r\n\r\n// API Functions\r\nexport const getOrganizations = async (): Promise<Organization[]> => {\r\n  const response = await fetch(`${API_URL}/api/organizations`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch organizations');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const getMyOrganizations = async (): Promise<Organization[]> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/my-organizations`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch your organizations');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const getOrganization = async (id: string): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to fetch organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const createOrganization = async (data: CreateOrganizationRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to create organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganization = async (id: string, data: UpdateOrganizationRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganizationBilling = async (id: string, data: UpdateOrganizationBillingRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}/billing`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization billing');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const updateOrganizationSettings = async (id: string, data: UpdateOrganizationSettingsRequest): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}/settings`, {\r\n    method: 'PATCH',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify(data),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to update organization settings');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const deleteOrganization = async (id: string): Promise<{ message: string }> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${id}`, {\r\n    method: 'DELETE',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to delete organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const addUserToOrganization = async (organizationId: string, userId: string, isAdmin: boolean): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${organizationId}/users/${userId}`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n    body: JSON.stringify({ isAdmin }),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to add user to organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport const removeUserFromOrganization = async (organizationId: string, userId: string): Promise<Organization> => {\r\n  const response = await fetch(`${API_URL}/api/organizations/${organizationId}/users/${userId}`, {\r\n    method: 'DELETE',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${localStorage.getItem('access_token')}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.message || 'Failed to remove user from organization');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAgFO,MAAM,mBAAmB;IAC9B,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,EAAE;QAC3D,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,mCAAmC,CAAC,EAAE;QAC5E,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,EAAE;QAC3D,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO,IAAY;IACnD,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,4BAA4B,OAAO,IAAY;IAC1D,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,GAAG,QAAQ,CAAC,EAAE;QACzE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,6BAA6B,OAAO,IAAY;IAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,GAAG,SAAS,CAAC,EAAE;QAC1E,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,wBAAwB,OAAO,gBAAwB,QAAgB;IAClF,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,eAAe,OAAO,EAAE,QAAQ,EAAE;QAC7F,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAQ;IACjC;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,6BAA6B,OAAO,gBAAwB;IACvE,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,eAAe,OAAO,EAAE,QAAQ,EAAE;QAC7F,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;QACjE;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;IACnC;IAEA,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28workspace%29/workspaces/OrganizationsContent.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Loader2, Plus, Edit, Trash2, Users, CreditCard } from 'lucide-react';\r\nimport { toast } from \"sonner\";\r\nimport { getOrganizations, createOrganization, deleteOrganization, Organization } from '@/app/api/organizations';\r\nimport { formatCurrency } from '@/lib/utils';\r\n\r\nexport default function OrganizationsContent() {\r\n  const [organizations, setOrganizations] = useState<Organization[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\r\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\r\n  const [organizationToDelete, setOrganizationToDelete] = useState<Organization | null>(null);\r\n  const [newOrganization, setNewOrganization] = useState<{\r\n    name: string;\r\n    description: string;\r\n    status: 'active' | 'inactive' | 'suspended';\r\n  }>({\r\n    name: '',\r\n    description: '',\r\n    status: 'active',\r\n  });\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    fetchOrganizations();\r\n  }, []);\r\n\r\n  const fetchOrganizations = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const data = await getOrganizations();\r\n      setOrganizations(data);\r\n    } catch (error) {\r\n      console.error('Error fetching Workspaces:', error);\r\n      toast.error('Failed to fetch Workspaces');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCreateOrganization = async () => {\r\n    try {\r\n      setIsSubmitting(true);\r\n      const created = await createOrganization(newOrganization);\r\n      setOrganizations([...organizations, created]);\r\n      setCreateDialogOpen(false);\r\n      setNewOrganization({\r\n        name: '',\r\n        description: '',\r\n        status: 'active',\r\n      });\r\n      toast.success('Workspace created successfully');\r\n    } catch (error) {\r\n      console.error('Error creating Workspace:', error);\r\n      toast.error('Failed to create Workspace');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteOrganization = async () => {\r\n    if (!organizationToDelete) return;\r\n\r\n    try {\r\n      setIsSubmitting(true);\r\n      await deleteOrganization(organizationToDelete._id);\r\n      setOrganizations(organizations.filter(org => org._id !== organizationToDelete._id));\r\n      setDeleteDialogOpen(false);\r\n      setOrganizationToDelete(null);\r\n      toast.success('Workspace deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting Workspace:', error);\r\n      toast.error('Failed to delete Workspace');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    switch (status) {\r\n      case 'active':\r\n        return <Badge className=\"bg-green-500\">Active</Badge>;\r\n      case 'inactive':\r\n        return <Badge className=\"bg-gray-500\">Inactive</Badge>;\r\n      case 'suspended':\r\n        return <Badge className=\"bg-red-500\">Suspended</Badge>;\r\n      default:\r\n        return <Badge>{status}</Badge>;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <h1 className=\"text-3xl font-bold\">Workspaces</h1>\r\n        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>\r\n          <DialogTrigger asChild>\r\n            <Button>\r\n              <Plus className=\"mr-2 h-4 w-4\" />\r\n              Create Workspaces\r\n            </Button>\r\n          </DialogTrigger>\r\n          <DialogContent>\r\n            <DialogHeader>\r\n              <DialogTitle>Create New Workspaces</DialogTitle>\r\n              <DialogDescription>\r\n                Add a new Workspaces to the system.\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n            <div className=\"grid gap-4 py-4\">\r\n              <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                <Label htmlFor=\"name\" className=\"text-right\">\r\n                  Name\r\n                </Label>\r\n                <Input\r\n                  id=\"name\"\r\n                  value={newOrganization.name}\r\n                  onChange={(e) => setNewOrganization({ ...newOrganization, name: e.target.value })}\r\n                  className=\"col-span-3\"\r\n                />\r\n              </div>\r\n              <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                <Label htmlFor=\"description\" className=\"text-right\">\r\n                  Description\r\n                </Label>\r\n                <Textarea\r\n                  id=\"description\"\r\n                  value={newOrganization.description}\r\n                  onChange={(e) => setNewOrganization({ ...newOrganization, description: e.target.value })}\r\n                  className=\"col-span-3\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <DialogFooter>\r\n              <Button variant=\"outline\" onClick={() => setCreateDialogOpen(false)}>\r\n                Cancel\r\n              </Button>\r\n              <Button onClick={handleCreateOrganization} disabled={isSubmitting || !newOrganization.name}>\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                    Creating...\r\n                  </>\r\n                ) : (\r\n                  'Create'\r\n                )}\r\n              </Button>\r\n            </DialogFooter>\r\n          </DialogContent>\r\n        </Dialog>\r\n      </div>\r\n\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>All Workspaces</CardTitle>\r\n          <CardDescription>Manage your Workspaces and their settings.</CardDescription>\r\n        </CardHeader>\r\n        <CardContent>\r\n          {loading ? (\r\n            <div className=\"flex justify-center items-center py-8\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin\" />\r\n            </div>\r\n          ) : organizations.length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500\">\r\n              No Workspaces found. Create one to get started.\r\n            </div>\r\n          ) : (\r\n            <Table>\r\n              <TableHeader>\r\n                <TableRow>\r\n                  <TableHead>Name</TableHead>\r\n                  <TableHead>Status</TableHead>\r\n                  <TableHead>Credits</TableHead>\r\n                  <TableHead>Users</TableHead>\r\n                  <TableHead>Created</TableHead>\r\n                  <TableHead className=\"text-right\">Actions</TableHead>\r\n                </TableRow>\r\n              </TableHeader>\r\n              <TableBody>\r\n                {organizations.map((org) => (\r\n                  <TableRow key={org._id}>\r\n                    <TableCell className=\"font-medium\">{org.name}</TableCell>\r\n                    <TableCell>{getStatusBadge(org.status)}</TableCell>\r\n                    <TableCell>{formatCurrency(org.credits || 0)}</TableCell>\r\n                    <TableCell>{(org.users?.length || 0) + (org.adminUsers?.length || 0)}</TableCell>\r\n                    <TableCell>{new Date(org.createdAt).toLocaleDateString()}</TableCell>\r\n                    <TableCell className=\"text-right\">\r\n                      <div className=\"flex justify-end gap-2\">\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"icon\"\r\n                          onClick={() => router.push(`/workspaces/${org._id}`)}\r\n                        >\r\n                          <Edit className=\"h-4 w-4\" />\r\n                        </Button>\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"icon\"\r\n                          onClick={() => router.push(`/workspaces/${org._id}/users`)}\r\n                        >\r\n                          <Users className=\"h-4 w-4\" />\r\n                        </Button>\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"icon\"\r\n                          className=\"text-red-500\"\r\n                          onClick={() => {\r\n                            setOrganizationToDelete(org);\r\n                            setDeleteDialogOpen(true);\r\n                          }}\r\n                        >\r\n                          <Trash2 className=\"h-4 w-4\" />\r\n                        </Button>\r\n                      </div>\r\n                    </TableCell>\r\n                  </TableRow>\r\n                ))}\r\n              </TableBody>\r\n            </Table>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Delete Organization</DialogTitle>\r\n            <DialogDescription>\r\n              Are you sure you want to delete the organization &quot;{organizationToDelete?.name}&quot;? This action cannot be undone.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setDeleteDialogOpen(false)}>\r\n              Cancel\r\n            </Button>\r\n            <Button variant=\"destructive\" onClick={handleDeleteOrganization} disabled={isSubmitting}>\r\n              {isSubmitting ? (\r\n                <>\r\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                  Deleting...\r\n                </>\r\n              ) : (\r\n                'Delete'\r\n              )}\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AAGpD;AACA;AACA;AAQA;AAQA;AAgBA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AA5CA;;;;;;;;;;;;;;;;AA8Ce,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIlD;QACD,MAAM;QACN,aAAa;QACb,QAAQ;IACV;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD;YAClC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B;QAC/B,IAAI;YACF,gBAAgB;YAChB,MAAM,UAAU,MAAM,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YACzC,iBAAiB;mBAAI;gBAAe;aAAQ;YAC5C,oBAAoB;YACpB,mBAAmB;gBACjB,MAAM;gBACN,aAAa;gBACb,QAAQ;YACV;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,2BAA2B;QAC/B,IAAI,CAAC,sBAAsB;QAE3B,IAAI;YACF,gBAAgB;YAChB,MAAM,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE,qBAAqB,GAAG;YACjD,iBAAiB,cAAc,MAAM,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,qBAAqB,GAAG;YACjF,oBAAoB;YACpB,wBAAwB;YACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAe;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAc;;;;;;YACxC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAa;;;;;;YACvC;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;8BAAE;;;;;;QACnB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAkB,cAAc;;0CAC5C,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;;sDACL,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAO,WAAU;kEAAa;;;;;;kEAG7C,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,gBAAgB,IAAI;wDAC3B,UAAU,CAAC,IAAM,mBAAmB;gEAAE,GAAG,eAAe;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC/E,WAAU;;;;;;;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAc,WAAU;kEAAa;;;;;;kEAGpD,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO,gBAAgB,WAAW;wDAClC,UAAU,CAAC,IAAM,mBAAmB;gEAAE,GAAG,eAAe;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACtF,WAAU;;;;;;;;;;;;;;;;;;kDAIhB,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,oBAAoB;0DAAQ;;;;;;0DAGrE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAA0B,UAAU,gBAAgB,CAAC,gBAAgB,IAAI;0DACvF,6BACC;;sEACE,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA8B;;mEAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQZ,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACT,wBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;mCAEnB,cAAc,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;sCAAiC;;;;;iDAIhD,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;;;;;;;;;;;;8CAGtC,8OAAC,iIAAA,CAAA,YAAS;8CACP,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAe,IAAI,IAAI;;;;;;8DAC5C,8OAAC,iIAAA,CAAA,YAAS;8DAAE,eAAe,IAAI,MAAM;;;;;;8DACrC,8OAAC,iIAAA,CAAA,YAAS;8DAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,OAAO,IAAI;;;;;;8DAC1C,8OAAC,iIAAA,CAAA,YAAS;8DAAE,CAAC,IAAI,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,UAAU,EAAE,UAAU,CAAC;;;;;;8DACnE,8OAAC,iIAAA,CAAA,YAAS;8DAAE,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB;;;;;;8DACtD,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DACnB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,IAAI,GAAG,EAAE;0EAEnD,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC;0EAEzD,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,wBAAwB;oEACxB,oBAAoB;gEACtB;0EAEA,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA/BX,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA2ClC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;;wCAAC;wCACuC,sBAAsB;wCAAK;;;;;;;;;;;;;sCAGvF,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CAGrE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS;oCAA0B,UAAU;8CACxE,6BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}]}