import { Model } from "mongoose";
import { ScheduledCall, ScheduledCallDocument } from "./schemas/scheduled-call.schema";
export declare class ScheduledCallService {
    private readonly scheduledCallModel;
    constructor(scheduledCallModel: Model<ScheduledCallDocument>);
    createScheduledCall(payload: {
        agentId: string;
        contacts: {
            Name: string;
            MobileNumber: string;
        }[];
        scheduledTime: string;
        region: string;
        scheduledByName: string;
    }): Promise<ScheduledCall>;
    getScheduledCalls(page?: number, limit?: number, search?: string, dateFilter?: string): Promise<ScheduledCall[]>;
    getLatestScheduledCallForCampaign(agentId: string): Promise<ScheduledCall[]>;
    getPendingCallsForCampaign(agentId: string): Promise<ScheduledCall[]>;
    removeDuplicateCalls(agentId: string): Promise<{
        duplicatesRemoved: number;
    }>;
    rescheduleCampaignCalls(agentId: string, concurrentCalls: number, batchIntervalMinutes: number, callWindow: {
        startTime: string;
        endTime: string;
        daysOfWeek: string[];
    }): Promise<{
        rescheduledCount: number;
        duplicatesRemoved: number;
        totalCount: number;
    }>;
    updateScheduledCall(id: string, updateData: Partial<ScheduledCall>): Promise<ScheduledCall>;
    getScheduledCallsByStatusAndTime(status: string, now: Date): Promise<(import("mongoose").Document<unknown, {}, ScheduledCallDocument> & ScheduledCall & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    getStuckProcessingCalls(timeThreshold?: number): Promise<(import("mongoose").Document<unknown, {}, ScheduledCallDocument> & ScheduledCall & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    getScheduledCallById(id: string): Promise<ScheduledCall>;
    deleteScheduledCall(id: string): Promise<boolean>;
    updateAgentForPendingCalls(contactNames: string[], contactPhones: string[], newAgentId: string): Promise<number>;
    cancelPendingCallsForContacts(contactNames: string[], contactPhones: string[]): Promise<number>;
    deleteCallsByStatus(status: string): Promise<{
        deletedCount: number;
    }>;
}
