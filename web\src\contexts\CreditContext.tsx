/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { getUserCredits, UserCreditsResponse } from "@/app/api/users";
import { useWebSocket } from "@/hooks/useWebSocket";
import { toast } from "sonner";

// Type for cached credit data with timestamp
interface CachedCreditData extends UserCreditsResponse {
  timestamp: number;
}

interface CreditContextType {
  // Legacy fields for backward compatibility
  credits: number;
  minutes: number;
  // New monthly credits fields
  freeCreditsRemaining: number;
  paidCredits: number;
  totalAvailable: number;
  usingFreeCredits: boolean;
  freeMinutesRemaining: number;
  paidMinutes: number;
  totalMinutesAvailable: number;
  // Common fields
  callPricePerMinute: number;
  monthlyAllowance: number;
  hasSufficientCredits: boolean;
  isLoading: boolean;
  refreshCredits: () => Promise<void>;
  creditThreshold: number;
  organizationCreditThreshold: number;
  effectiveThreshold: number;
  // Connection state
  isConnected: boolean;
  hasValidData: boolean;
  lastSuccessfulFetch: number | null;
}

const CreditContext = createContext<CreditContextType | undefined>(undefined);

interface CreditProviderProps {
  children: ReactNode;
  creditThreshold?: number;
}

export function CreditProvider({
  children,
  creditThreshold = 1
}: CreditProviderProps) {
  // Legacy fields for backward compatibility
  const [credits, setCredits] = useState<number>(0);
  const [minutes, setMinutes] = useState<number>(0);
  // New monthly credits fields
  const [freeCreditsRemaining, setFreeCreditsRemaining] = useState<number>(0);
  const [paidCredits, setPaidCredits] = useState<number>(0);
  const [totalAvailable, setTotalAvailable] = useState<number>(0);
  const [usingFreeCredits, setUsingFreeCredits] = useState<boolean>(false);
  const [freeMinutesRemaining, setFreeMinutesRemaining] = useState<number>(0);
  const [paidMinutes, setPaidMinutes] = useState<number>(0);
  const [totalMinutesAvailable, setTotalMinutesAvailable] = useState<number>(0);
  // Common fields
  const [callPricePerMinute, setCallPricePerMinute] = useState<number>(0.1);
  const [monthlyAllowance, setMonthlyAllowance] = useState<number>(0);
  const [organizationCreditThreshold, setOrganizationCreditThreshold] = useState<number>(1.0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [organizationId, setOrganizationId] = useState<string | null>(null);

  // Connection and data validity state
  const [hasValidData, setHasValidData] = useState<boolean>(false);
  const [lastSuccessfulFetch, setLastSuccessfulFetch] = useState<number | null>(null);
  const [consecutiveFailures, setConsecutiveFailures] = useState<number>(0);

  // Initialize WebSocket connection to the credits namespace
  const { isConnected, on, registerUser } = useWebSocket('credits');

  // Persistence helpers
  const saveCreditDataToStorage = (data: UserCreditsResponse) => {
    try {
      const creditData = {
        ...data,
        timestamp: Date.now()
      };
      localStorage.setItem('lastKnownCredits', JSON.stringify(creditData));
    } catch (error) {
      console.warn('Failed to save credit data to localStorage:', error);
    }
  };

  const loadCreditDataFromStorage = (): CachedCreditData | null => {
    try {
      const stored = localStorage.getItem('lastKnownCredits');
      if (stored) {
        const data = JSON.parse(stored);
        // Only use stored data if it's less than 5 minutes old
        if (Date.now() - data.timestamp < 5 * 60 * 1000) {
          return data;
        }
      }
    } catch (error) {
      console.warn('Failed to load credit data from localStorage:', error);
    }
    return null;
  };

  const fetchCredits = async () => {
    try {
      setIsLoading(true);
      const response = await getUserCredits();

      // Update new monthly credits fields
      setFreeCreditsRemaining(response.freeCreditsRemaining || 0);
      setPaidCredits(response.paidCredits || 0);
      setTotalAvailable(response.totalAvailable || 0);
      setUsingFreeCredits(response.usingFreeCredits || false);
      setFreeMinutesRemaining(response.freeMinutesRemaining || 0);
      setPaidMinutes(response.paidMinutes || 0);
      setTotalMinutesAvailable(response.totalMinutesAvailable || 0);

      // Update legacy fields for backward compatibility
      setCredits(response.credits || response.totalAvailable || 0);
      setMinutes(response.minutes || response.totalMinutesAvailable || 0);
      setCallPricePerMinute(response.callPricePerMinute || 0.1);
      setMonthlyAllowance(response.monthlyAllowance || 0);
      setOrganizationCreditThreshold(response.minimumCreditsThreshold || 1.0);

      // Mark as successful fetch
      setHasValidData(true);
      setLastSuccessfulFetch(Date.now());
      setConsecutiveFailures(0);

      // Save to localStorage for persistence
      saveCreditDataToStorage(response);

      return response;
    } catch (error) {
      console.error("Error fetching user credits:", error);
      setConsecutiveFailures(prev => prev + 1);

      // If we have recent cached data and this is a temporary failure, use cached data
      const cachedData = loadCreditDataFromStorage();
      if (cachedData && consecutiveFailures < 3) {
        console.log('Using cached credit data due to fetch failure');

        // Update with cached data instead of zeros
        setFreeCreditsRemaining(cachedData.freeCreditsRemaining || 0);
        setPaidCredits(cachedData.paidCredits || 0);
        setTotalAvailable(cachedData.totalAvailable || 0);
        setUsingFreeCredits(cachedData.usingFreeCredits || false);
        setFreeMinutesRemaining(cachedData.freeMinutesRemaining || 0);
        setPaidMinutes(cachedData.paidMinutes || 0);
        setTotalMinutesAvailable(cachedData.totalMinutesAvailable || 0);
        setCredits(cachedData.credits || cachedData.totalAvailable || 0);
        setMinutes(cachedData.minutes || cachedData.totalMinutesAvailable || 0);
        setCallPricePerMinute(cachedData.callPricePerMinute || 0.1);
        setMonthlyAllowance(cachedData.monthlyAllowance || 0);
        setOrganizationCreditThreshold(cachedData.minimumCreditsThreshold || 1.0);

        return cachedData;
      }

      // Only return zeros if we have no cached data and multiple failures
      return {
        credits: 0,
        minutes: 0,
        callPricePerMinute: 0.1,
        freeCreditsRemaining: 0,
        paidCredits: 0,
        totalAvailable: 0,
        usingFreeCredits: false,
        freeMinutesRemaining: 0,
        paidMinutes: 0,
        totalMinutesAvailable: 0,
        monthlyAllowance: 0
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Get user ID and organization ID from localStorage on mount
  useEffect(() => {
    try {
      const userData = localStorage.getItem('user_data');
      if (userData) {
        const parsedData = JSON.parse(userData);
        const userId = parsedData._id || null;
        const organizationId = parsedData.organizationId || null;

        // console.log('Retrieved user data from localStorage:', {
        //   userId,
        //   organizationId,
        //   rawData: parsedData
        // });

        setUserId(userId);
        setOrganizationId(organizationId);

        // If we don't have an organization ID, try to fetch it from the API
        if (!organizationId && userId) {
          console.log('No organization ID found in localStorage, will try to fetch from API');
          fetchCredits().then(() => {
            console.log('Fetched credits and possibly updated organization ID');
          });
        }
      } else {
        console.warn('No user data found in localStorage');
      }
    } catch (error) {
      console.error("Error getting user data from localStorage:", error);
    }
  }, []);

  // Register user with WebSocket when connected and userId is available
  useEffect(() => {
    if (isConnected && userId) {
      console.log('Registering user with WebSocket:', +9*9);
      registerUser(userId);
    }
  }, [isConnected, userId, registerUser]);

  // Set up WebSocket listeners for credit updates
  useEffect(() => {
    if (!isConnected) {
      console.log('WebSocket not connected, skipping credit update listeners setup');
      return;
    }

    console.log('Setting up credit update listeners, organizationId:', organizationId);

    // Listen for credit updates for this user
    const unsubscribeCreditUpdate = on('creditUpdate', (data: { credits: number }) => {
      console.log('Received credit update via WebSocket:', data);
      setCredits(data.credits);
      // Calculate minutes based on current callPricePerMinute
      if (callPricePerMinute > 0) {
        setMinutes(data.credits / callPricePerMinute);
      }
      toast.info(`Your credits have been updated`);
    });

    // Listen for organization credit updates
    const unsubscribeOrgUpdate = on('organizationCreditUpdate', (data: { organizationId: string, credits: number }) => {
      console.log('Received organization credit update via WebSocket:', data);
      console.log('Current organizationId:', organizationId);
      console.log('Received organizationId:', data.organizationId);

      // Only update if it's for our organization
      // Convert both IDs to strings for comparison to avoid type mismatches
      const ourOrgId = organizationId?.toString();
      const receivedOrgId = data.organizationId?.toString();

      console.log('Comparing organization IDs:', {
        ourOrgId,
        receivedOrgId,
        match: ourOrgId === receivedOrgId
      });

      if (ourOrgId && receivedOrgId && ourOrgId === receivedOrgId) {
        console.log('Organization ID matched, updating credits to:', data.credits);
        setCredits(data.credits);
        // Calculate minutes based on current callPricePerMinute
        if (callPricePerMinute > 0) {
          setMinutes(data.credits / callPricePerMinute);
        }
        toast.info(`Your organization credits have been updated`);
      } else {
        console.log('Organization ID did not match or is missing, not updating credits');
        console.log('Our organization ID:', ourOrgId);
        console.log('Received organization ID:', receivedOrgId);
      }
    });

    return () => {
      console.log('Cleaning up credit update listeners');
      unsubscribeCreditUpdate();
      unsubscribeOrgUpdate();
    };
  }, [isConnected, on, organizationId]);

  // Load cached data on mount, then fetch fresh data
  useEffect(() => {
    // Try to load cached data first for immediate display
    const cachedData = loadCreditDataFromStorage();
    if (cachedData) {
      console.log('Loading cached credit data on mount');
      setFreeCreditsRemaining(cachedData.freeCreditsRemaining || 0);
      setPaidCredits(cachedData.paidCredits || 0);
      setTotalAvailable(cachedData.totalAvailable || 0);
      setUsingFreeCredits(cachedData.usingFreeCredits || false);
      setFreeMinutesRemaining(cachedData.freeMinutesRemaining || 0);
      setPaidMinutes(cachedData.paidMinutes || 0);
      setTotalMinutesAvailable(cachedData.totalMinutesAvailable || 0);
      setCredits(cachedData.credits || cachedData.totalAvailable || 0);
      setMinutes(cachedData.minutes || cachedData.totalMinutesAvailable || 0);
      setCallPricePerMinute(cachedData.callPricePerMinute || 0.1);
      setMonthlyAllowance(cachedData.monthlyAllowance || 0);
      setOrganizationCreditThreshold(cachedData.minimumCreditsThreshold || 1.0);
      setHasValidData(true);
      setLastSuccessfulFetch(cachedData.timestamp);
    }

    // Initial fetch
    fetchCredits();

    // Set up polling interval (as a fallback)
    const intervalId = setInterval(() => {
      fetchCredits();
    }, 30000); // 30 seconds

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [callPricePerMinute]);

  const refreshCredits = async () => {
    await fetchCredits();
  };

  // Use the higher of the organization threshold or the prop threshold
  const effectiveThreshold = Math.max(creditThreshold, organizationCreditThreshold);
  const hasSufficientCredits = totalAvailable >= effectiveThreshold;

  const value = {
    // Legacy fields for backward compatibility
    credits,
    minutes,
    // New monthly credits fields
    freeCreditsRemaining,
    paidCredits,
    totalAvailable,
    usingFreeCredits,
    freeMinutesRemaining,
    paidMinutes,
    totalMinutesAvailable,
    // Common fields
    callPricePerMinute,
    monthlyAllowance,
    hasSufficientCredits,
    isLoading,
    refreshCredits,
    creditThreshold,
    organizationCreditThreshold,
    effectiveThreshold,
    // Connection state
    isConnected,
    hasValidData,
    lastSuccessfulFetch
  };

  return (
    <CreditContext.Provider value={value}>
      {children}
    </CreditContext.Provider>
  );
}

export function useCredits() {
  const context = useContext(CreditContext);
  if (context === undefined) {
    throw new Error("useCredits must be used within a CreditProvider");
  }
  return context;
}
