"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationSchema = void 0;
const mongoose_1 = require("mongoose");
exports.OrganizationSchema = new mongoose_1.Schema({
    name: { type: String, required: true },
    description: { type: String },
    status: {
        type: String,
        enum: ['active', 'inactive', 'suspended'],
        default: 'active'
    },
    credits: { type: Number, default: 0 },
    monthlyFreeCredits: { type: Number, default: 0 },
    monthlyFreeCreditsUsed: { type: Number, default: 0 },
    lastMonthlyReset: { type: Date, default: Date.now },
    usingFreeCredits: { type: Boolean, default: true },
    monthlyResetDate: { type: Number, default: 1 },
    autoRechargeEnabled: { type: Boolean, default: false },
    autoRechargeThreshold: { type: Number, default: 1.0 },
    autoRechargeAmount: { type: Number, default: 0 },
    callPricePerMinute: { type: Number, default: 0.10 },
    minimumCreditsThreshold: { type: Number, default: 1.0 },
    monthlyMinutesAllowance: { type: Number, default: 0 },
    stripeCustomerId: { type: String },
    fullName: { type: String },
    email: { type: String },
    lastWarningEmailSent: { type: Date },
    lastRunoutEmailSent: { type: Date },
    s3Config: {
        accessKeyId: { type: String },
        secretAccessKey: { type: String },
        region: { type: String },
        bucketName: { type: String },
        enabled: { type: Boolean, default: false }
    },
    adminUsers: [{ type: mongoose_1.Types.ObjectId, ref: 'User' }],
    users: [{ type: mongoose_1.Types.ObjectId, ref: 'User' }],
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
exports.OrganizationSchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});
//# sourceMappingURL=organization.schema.js.map