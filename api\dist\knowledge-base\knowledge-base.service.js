"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBaseService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const s3_service_1 = require("../s3/s3.service");
const organizations_service_1 = require("../organizations/organizations.service");
let KnowledgeBaseService = class KnowledgeBaseService {
    constructor(folderModel, itemModel, s3Service, organizationsService) {
        this.folderModel = folderModel;
        this.itemModel = itemModel;
        this.s3Service = s3Service;
        this.organizationsService = organizationsService;
    }
    async createFolder(organizationId, userId, createFolderDto) {
        const folder = new this.folderModel({
            ...createFolderDto,
            organizationId,
            createdBy: userId,
        });
        return folder.save();
    }
    async getFoldersByOrganization(organizationId) {
        return this.folderModel.find({ organizationId }).sort({ createdAt: -1 }).exec();
    }
    async getFolderById(folderId, organizationId) {
        const folder = await this.folderModel.findOne({ _id: folderId, organizationId }).exec();
        if (!folder) {
            throw new common_1.NotFoundException('Folder not found');
        }
        return folder;
    }
    async updateFolder(folderId, organizationId, updateFolderDto) {
        const folder = await this.folderModel.findOneAndUpdate({ _id: folderId, organizationId }, { ...updateFolderDto, updatedAt: new Date() }, { new: true }).exec();
        if (!folder) {
            throw new common_1.NotFoundException('Folder not found');
        }
        return folder;
    }
    async deleteFolder(folderId, organizationId) {
        const items = await this.itemModel.find({ folderId, organizationId }).exec();
        const organization = await this.organizationsService.findOne(organizationId);
        if (organization.s3Config && organization.s3Config.enabled) {
            const s3Config = {
                accessKeyId: organization.s3Config.accessKeyId,
                secretAccessKey: organization.s3Config.secretAccessKey,
                region: organization.s3Config.region,
                bucketName: organization.s3Config.bucketName,
            };
            for (const item of items) {
                if (item.type === 'document' && item.s3Key) {
                    try {
                        await this.s3Service.deleteFile(s3Config, item.s3Key);
                    }
                    catch (error) {
                        console.error(`Failed to delete S3 file ${item.s3Key}:`, error);
                    }
                }
            }
        }
        await this.itemModel.deleteMany({ folderId, organizationId }).exec();
        const result = await this.folderModel.deleteOne({ _id: folderId, organizationId }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException('Folder not found');
        }
    }
    async createItem(organizationId, userId, createItemDto) {
        await this.getFolderById(createItemDto.folderId, organizationId);
        const item = new this.itemModel({
            ...createItemDto,
            organizationId,
            uploadedBy: userId,
            status: createItemDto.type === 'document' ? 'uploading' : 'ready',
        });
        return item.save();
    }
    async uploadDocumentToS3(itemId, organizationId, file, originalName, mimeType) {
        const item = await this.itemModel.findOne({ _id: itemId, organizationId }).exec();
        if (!item) {
            throw new common_1.NotFoundException('Knowledge base item not found');
        }
        if (item.type !== 'document') {
            throw new common_1.BadRequestException('Item is not a document type');
        }
        const organization = await this.organizationsService.findOne(organizationId);
        if (!organization.s3Config || !organization.s3Config.enabled) {
            throw new common_1.BadRequestException('S3 is not configured for this organization');
        }
        const s3Config = {
            accessKeyId: organization.s3Config.accessKeyId,
            secretAccessKey: organization.s3Config.secretAccessKey,
            region: organization.s3Config.region,
            bucketName: organization.s3Config.bucketName,
        };
        try {
            const timestamp = Date.now();
            const fileExtension = originalName.split('.').pop();
            const fileName = `${timestamp}-${originalName}`;
            const uploadResult = await this.s3Service.uploadFile(s3Config, file, fileName, mimeType, organizationId, item.folderId);
            item.filename = fileName;
            item.originalName = originalName;
            item.mimeType = mimeType;
            item.size = file.length;
            item.s3Key = uploadResult.key;
            item.s3Url = uploadResult.url;
            item.s3Bucket = s3Config.bucketName;
            item.status = 'ready';
            item.updatedAt = new Date();
            return item.save();
        }
        catch (error) {
            item.status = 'error';
            item.errorMessage = error.message;
            item.updatedAt = new Date();
            await item.save();
            throw error;
        }
    }
    async getItemsByFolder(folderId, organizationId) {
        await this.getFolderById(folderId, organizationId);
        return this.itemModel.find({ folderId, organizationId }).sort({ createdAt: -1 }).exec();
    }
    async getItemById(itemId, organizationId) {
        const item = await this.itemModel.findOne({ _id: itemId, organizationId }).exec();
        if (!item) {
            throw new common_1.NotFoundException('Knowledge base item not found');
        }
        return item;
    }
    async updateItem(itemId, organizationId, updateItemDto) {
        const item = await this.itemModel.findOneAndUpdate({ _id: itemId, organizationId }, { ...updateItemDto, updatedAt: new Date() }, { new: true }).exec();
        if (!item) {
            throw new common_1.NotFoundException('Knowledge base item not found');
        }
        return item;
    }
    async deleteItem(itemId, organizationId) {
        const item = await this.itemModel.findOne({ _id: itemId, organizationId }).exec();
        if (!item) {
            throw new common_1.NotFoundException('Knowledge base item not found');
        }
        if (item.type === 'document' && item.s3Key) {
            const organization = await this.organizationsService.findOne(organizationId);
            if (organization.s3Config && organization.s3Config.enabled) {
                const s3Config = {
                    accessKeyId: organization.s3Config.accessKeyId,
                    secretAccessKey: organization.s3Config.secretAccessKey,
                    region: organization.s3Config.region,
                    bucketName: organization.s3Config.bucketName,
                };
                try {
                    await this.s3Service.deleteFile(s3Config, item.s3Key);
                }
                catch (error) {
                    console.error(`Failed to delete S3 file ${item.s3Key}:`, error);
                }
            }
        }
        await this.itemModel.deleteOne({ _id: itemId, organizationId }).exec();
    }
    async getPresignedDownloadUrl(itemId, organizationId) {
        const item = await this.getItemById(itemId, organizationId);
        if (item.type !== 'document' || !item.s3Key) {
            throw new common_1.BadRequestException('Item is not a downloadable document');
        }
        const organization = await this.organizationsService.findOne(organizationId);
        if (!organization.s3Config || !organization.s3Config.enabled) {
            throw new common_1.BadRequestException('S3 is not configured for this organization');
        }
        const s3Config = {
            accessKeyId: organization.s3Config.accessKeyId,
            secretAccessKey: organization.s3Config.secretAccessKey,
            region: organization.s3Config.region,
            bucketName: organization.s3Config.bucketName,
        };
        return this.s3Service.getPresignedUrl(s3Config, item.s3Key, 3600);
    }
};
exports.KnowledgeBaseService = KnowledgeBaseService;
exports.KnowledgeBaseService = KnowledgeBaseService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('KnowledgeBaseFolder')),
    __param(1, (0, mongoose_1.InjectModel)('KnowledgeBaseItem')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        s3_service_1.S3Service,
        organizations_service_1.OrganizationsService])
], KnowledgeBaseService);
//# sourceMappingURL=knowledge-base.service.js.map