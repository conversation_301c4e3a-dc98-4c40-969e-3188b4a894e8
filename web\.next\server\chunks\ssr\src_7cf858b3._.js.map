{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatCurrency(amount: number): string {\r\n  return new Intl.NumberFormat('en-US', {\r\n    style: 'currency',\r\n    currency: 'USD',\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2,\r\n  }).format(amount);\r\n}\r\n\r\nexport function formatPhoneNumber(phoneNumber: string): string {\r\n  // Assuming US phone format +1XXXXXXXXXX\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n\r\n  // Check if it's a valid US number\r\n  if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+${cleaned.substring(0, 1)} (${cleaned.substring(1, 4)}) ${cleaned.substring(4, 7)}-${cleaned.substring(7, 11)}`;\r\n  } else if (cleaned.length === 10) {\r\n    return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6, 10)}`;\r\n  }\r\n\r\n  // Return formatted or as-is if can't format\r\n  return phoneNumber;\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,kBAAkB,WAAmB;IACnD,wCAAwC;IACxC,MAAM,UAAU,YAAY,OAAO,CAAC,OAAO;IAE3C,kCAAkC;IAClC,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,MAAM;QACpD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,KAAK;IAC1H,OAAO,IAAI,QAAQ,MAAM,KAAK,IAAI;QAChC,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,KAAK;IACjG;IAEA,4CAA4C;IAC5C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border \",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\"flex flex-col gap-1.5 px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28auth%29/actions/auth.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\"use server\";\r\nimport { cookies } from \"next/headers\";\r\nimport { registerSchema, loginSchema } from \"@/lib/validations/authSchema\";\r\n\r\n\r\n\r\nexport type ActionResult = {\r\n  success: boolean;\r\n  message: string;\r\n  fieldErrors?: Record<string, string[]>;\r\n  redirect?: string;\r\n  tokens?: {\r\n    access_token: string;\r\n    refresh_token: string;\r\n  };\r\n};\r\n \r\nexport type UserInfo = {\r\n  fullName: string;\r\n  userId: string;\r\n  email: string;\r\n  role: string;\r\n};\r\n \r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n \r\n \r\nexport async function registerUser(formData: FormData): Promise<ActionResult> {\r\n    // Extract data from form\r\n    const rawData = {\r\n      fullName: formData.get(\"fullName\"),\r\n      email: formData.get(\"email\"),\r\n      password: formData.get(\"password\"),\r\n    };\r\n \r\n    // Validate with Zod\r\n    const validationResult = registerSchema.safeParse(rawData);\r\n   \r\n    if (!validationResult.success) {\r\n      // Return validation errors\r\n      return {\r\n        success: false,\r\n        message: \"Validation failed\",\r\n        fieldErrors: validationResult.error.flatten().fieldErrors,\r\n      };\r\n    }\r\n \r\n    // Validation passed, destructure the validated data\r\n    const { fullName, email, password } = validationResult.data;\r\n \r\n    try {\r\n      // Make API request to registration endpoint\r\n      const response = await fetch(`${API_BASE_URL}/api/users/register`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          fullName,\r\n          email,\r\n          password,\r\n        }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      // First check if response is ok\r\n      if (!response.ok) {\r\n        try {\r\n          // Try to parse as JSON for error details\r\n          const errorData = await response.json();\r\n          return {\r\n            success: false,\r\n            message: errorData.message || \"Registration failed. Please try again.\",\r\n          };\r\n        } catch {\r\n          // If not JSON, get text or use statusText\r\n          const errorText = await response.text().catch(() => response.statusText);\r\n          return {\r\n            success: false,\r\n            message: errorText || \"Registration failed. Please try again.\",\r\n          };\r\n        }\r\n      }\r\n \r\n      // For successful responses, get the text content\r\n      const successText = await response.text();\r\n     \r\n      return {\r\n        success: true,\r\n        message: successText || \"User registered successfully! Awaiting admin approval.\"\r\n      };\r\n \r\n    } catch (error) {\r\n      console.error(\"Registration error:\", error);\r\n      return {\r\n        success: false,\r\n        message: \"An unexpected error occurred. Please try again later.\"\r\n      };\r\n    }\r\n  }\r\n \r\nexport async function loginUser(formData: FormData): Promise<ActionResult> {\r\n    const rawData = {\r\n      email: formData.get(\"email\"),\r\n      password: formData.get(\"password\"),\r\n    };\r\n \r\n    // Validate with Zod\r\n    const validationResult = loginSchema.safeParse(rawData);\r\n   \r\n    if (!validationResult.success) {\r\n      // Return validation errors\r\n      return {\r\n        success: false,\r\n        message: \"Validation failed\",\r\n        fieldErrors: validationResult.error.flatten().fieldErrors,\r\n      };\r\n    }\r\n \r\n    // Validation passed, destructure the validated data\r\n    const { email, password } = validationResult.data;\r\n \r\n    try {\r\n      // Make API request to login endpoint\r\n      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          email,\r\n          password,\r\n        }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      // Check if the response is OK\r\n      if (!response.ok) {\r\n        try {\r\n          const errorData = await response.json();\r\n          return {\r\n            success: false,\r\n            message: errorData.message || \"Login failed. Please check your credentials.\",\r\n          };\r\n        } catch {\r\n          return {\r\n            success: false,\r\n            message: response.statusText || \"Login failed. Please try again.\",\r\n          };\r\n        }\r\n      }\r\n \r\n      // Parse the successful response to get tokens\r\n      try {\r\n        const data = await response.json();\r\n       \r\n        if (data.access_token && data.refresh_token) {\r\n          // Return tokens in the response rather than setting cookies\r\n          return {\r\n            success: true,\r\n            message: \"Login successful!\",\r\n            redirect: \"/dashboard\",\r\n            tokens: {\r\n              access_token: data.access_token,\r\n              refresh_token: data.refresh_token\r\n            }\r\n          };\r\n        } else {\r\n          return {\r\n            success: false,\r\n            message: \"Invalid response from server. Missing authentication tokens.\",\r\n          };\r\n        }\r\n      } catch (jsonError) {\r\n        return {\r\n          success: false,\r\n          message: \"Failed to process login response.\",\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Login error:\", error);\r\n      return {\r\n        success: false,\r\n        message: \"An unexpected error occurred. Please try again later.\"\r\n      };\r\n    }\r\n  }\r\n \r\n  // Add a refresh token function for later use\r\n  export async function refreshAccessToken(): Promise<{\r\n    success: boolean;\r\n    newAccessToken?: string;\r\n  }> {\r\n   \r\n    // Get the refresh token from cookies\r\n    const refreshToken = (await cookies()).get(\"refresh_token\")?.value;\r\n \r\n   \r\n    if (!refreshToken) {\r\n      // Without a refresh token, we can't refresh the session\r\n      return { success: false };\r\n    }\r\n \r\n    try {\r\n      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"Authorization\": `Bearer ${refreshToken}`,\r\n        },\r\n        body: JSON.stringify({ refreshToken }),\r\n        cache: \"no-store\",\r\n      });\r\n \r\n      if (!response.ok) {\r\n        // If the refresh token is invalid or expired, user needs to log in again\r\n        return { success: false };\r\n      }\r\n \r\n      const data = await response.json();\r\n     \r\n      if (data.access_token) {\r\n        // Update the access token in cookies\r\n        (await\r\n          // Update the access token in cookies\r\n          cookies()).set(\"access_token\", data.access_token, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          path: \"/\",\r\n          sameSite: \"lax\",\r\n          maxAge: 60 * 60, // 1 hour\r\n        });\r\n \r\n        return {\r\n          success: true,\r\n          newAccessToken: data.access_token,\r\n        };\r\n      }\r\n     \r\n      return { success: false };\r\n    } catch (error) {\r\n      console.error(\"Token refresh error:\", error);\r\n      return { success: false };\r\n    }\r\n  }\r\n \r\n// Function to get current user information\r\nexport async function getCurrentUser(): Promise<{\r\n  success: boolean;\r\n  user?: UserInfo;\r\n  error?: string;\r\n}> {\r\n  let accessToken;\r\n \r\n  if (typeof window !== 'undefined') {\r\n    // We're in a browser\r\n    accessToken = localStorage.getItem('access_token');\r\n  } else {\r\n    // We're on the server\r\n    accessToken = (await cookies()).get(\"access_token\")?.value;\r\n  }\r\n \r\n  if (!accessToken) {\r\n    console.log(\"No access token found\");\r\n    return {\r\n      success: false,\r\n      error: \"Not authenticated\"\r\n    };\r\n  }\r\n \r\n  try {\r\n \r\n   \r\n    const response = await fetch(`${API_BASE_URL}/api/auth/me`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Authorization\": `Bearer ${accessToken}`,\r\n      },\r\n      cache: \"no-store\",\r\n    });\r\n \r\n \r\n    if (!response.ok) {\r\n      // If response is 401 or 403, try to refresh the token\r\n      if (response.status === 401 || response.status === 403) {\r\n        // Attempt to refresh the token\r\n        const refreshResult = await refreshAccessToken();\r\n       \r\n        if (refreshResult.success) {\r\n          // Retry with new token\r\n          const retryResponse = await fetch(`${API_BASE_URL}/api/auth/me`, {\r\n            method: \"GET\",\r\n            headers: {\r\n              \"Authorization\": `Bearer ${refreshResult.newAccessToken}`,\r\n            },\r\n            cache: \"no-store\",\r\n          });\r\n \r\n          if (retryResponse.ok) {\r\n            const userData = await retryResponse.json();\r\n           \r\n            // Check if we got actual user data back (not empty object)\r\n            if (userData && userData.userId && userData.email) {\r\n              return {\r\n                success: true,\r\n                user: userData\r\n              };\r\n            } else {\r\n              return {\r\n                success: false,\r\n                error: \"Account not authorized or pending approval\"\r\n              };\r\n            }\r\n          }\r\n        }\r\n       \r\n        // If refresh failed or retry failed\r\n        return {\r\n          success: false,\r\n          error: \"Not authorized\"\r\n        };\r\n      }\r\n     \r\n      // For other errors\r\n      return {\r\n        success: false,\r\n        error: `Error: ${response.status}`\r\n      };\r\n    }\r\n \r\n    // If first attempt was successful\r\n    const userData = await response.json();\r\n    // This confirms the user has proper approval, not just valid tokens\r\n    if (userData && userData.userId && userData.email) {\r\n      return {\r\n        success: true,\r\n        user: userData\r\n      };\r\n    } else {\r\n      // User has valid tokens but no data - likely pending approval\r\n      return {\r\n        success: false,\r\n        error: \"Account not authorized or pending approval\"\r\n      };\r\n    }\r\n \r\n  } catch (error) {\r\n    console.error(\"Error fetching user data:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An error occurred while fetching user data\"\r\n    };\r\n  }\r\n}\r\n \r\nexport async function logoutUser(): Promise<ActionResult> {\r\n  try {\r\n    // Clear the cookies\r\n    const cookieStore = await cookies();\r\n    cookieStore.delete(\"access_token\");\r\n    cookieStore.delete(\"refresh_token\");\r\n   \r\n    return {\r\n      success: true,\r\n      message: \"Logged out successfully\",\r\n      redirect: \"/\"\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Logout error:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"An error occurred during logout\"\r\n    };\r\n  }\r\n}"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;;;;IA4B9B;IA0EA;IAwFE;IA0DF;IA4GA", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/assets/img/OROVA-WHITE.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 732, height: 124, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAYAAADjAO9DAAAAIklEQVR42mM4uvfUnc8fv1T9+/evFIi7gLgciAuBuBgkBgDAoxsmTQBGxAAAAABJRU5ErkJggg==\", blurWidth: 8, blurHeight: 1 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAsJ,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/lib/authFetch.ts"], "sourcesContent": ["// Add this new function to your auth-client.ts file\r\n\r\nimport { refreshAccessToken } from \"./auth-client\";\r\n\r\n/**\r\n * Authenticated fetch utility that handles token refresh automatically\r\n * @param url The URL to fetch\r\n * @param options Fetch options\r\n * @returns Response from the fetch call\r\n */\r\nexport async function authFetch(url: string, options: RequestInit = {}): Promise<Response> {\r\n    let accessToken = localStorage.getItem('access_token');\r\n    \r\n    // If no access token is available, try to refresh\r\n    if (!accessToken) {\r\n      const refreshResult = await refreshAccessToken();\r\n      if (!refreshResult.success) {\r\n        throw new Error('No authentication token available');\r\n      }\r\n      accessToken = refreshResult.newAccessToken as string;\r\n    }\r\n  \r\n    // Add authorization header if not already present\r\n    const headers = new Headers(options.headers || {});\r\n    if (!headers.has('Authorization')) {\r\n      headers.set('Authorization', `Bearer ${accessToken}`);\r\n    }\r\n  \r\n    // Make the request with the current token\r\n    const response = await fetch(url, {\r\n      ...options,\r\n      headers\r\n    });\r\n  \r\n    // If unauthorized or forbidden, try to refresh the token and retry the request\r\n    if (response.status === 401 || response.status === 403) {\r\n      console.log('Token expired, attempting refresh...');\r\n      const refreshResult = await refreshAccessToken();\r\n      \r\n      if (!refreshResult.success) {\r\n        console.error('Token refresh failed');\r\n        // Handle auth failure - redirect to login or show message\r\n        if (typeof window !== 'undefined') {\r\n          // Only redirect in browser\r\n          window.location.href = '/login';\r\n        }\r\n        throw new Error('Authentication failed');\r\n      }\r\n      \r\n      // Retry the request with the new token\r\n      console.log('Token refreshed, retrying request...');\r\n      const newHeaders = new Headers(options.headers || {});\r\n      newHeaders.set('Authorization', `Bearer ${refreshResult.newAccessToken as string}`);\r\n      \r\n      return fetch(url, {\r\n        ...options,\r\n        headers: newHeaders\r\n      });\r\n    }\r\n    \r\n    return response;\r\n  }"], "names": [], "mappings": "AAAA,oDAAoD;;;;AAEpD;;AAQO,eAAe,UAAU,GAAW,EAAE,UAAuB,CAAC,CAAC;IAClE,IAAI,cAAc,aAAa,OAAO,CAAC;IAEvC,kDAAkD;IAClD,IAAI,CAAC,aAAa;QAChB,MAAM,gBAAgB,MAAM,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD;QAC7C,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,MAAM,IAAI,MAAM;QAClB;QACA,cAAc,cAAc,cAAc;IAC5C;IAEA,kDAAkD;IAClD,MAAM,UAAU,IAAI,QAAQ,QAAQ,OAAO,IAAI,CAAC;IAChD,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB;QACjC,QAAQ,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,aAAa;IACtD;IAEA,0CAA0C;IAC1C,MAAM,WAAW,MAAM,MAAM,KAAK;QAChC,GAAG,OAAO;QACV;IACF;IAEA,+EAA+E;IAC/E,IAAI,SAAS,MAAM,KAAK,OAAO,SAAS,MAAM,KAAK,KAAK;QACtD,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,MAAM,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD;QAE7C,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,QAAQ,KAAK,CAAC;YACd,0DAA0D;YAC1D,uCAAmC;;YAGnC;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,uCAAuC;QACvC,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,IAAI,QAAQ,QAAQ,OAAO,IAAI,CAAC;QACnD,WAAW,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,cAAc,EAAY;QAElF,OAAO,MAAM,KAAK;YAChB,GAAG,OAAO;YACV,SAAS;QACX;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/lib/auth-client.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { authFetch } from \"./authFetch\";\r\n\r\nexport type UserInfo = {\r\n  fullName: string;\r\n  userId: string;\r\n  email: string;\r\n  role: string;\r\n};\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n\r\n// Client-side version of getCurrentUser\r\nexport async function getCurrentUser(): Promise<{\r\n  success: boolean;\r\n  user?: UserInfo;\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Use the authFetch utility instead of regular fetch\r\n    const response = await authFetch(`${API_BASE_URL}/api/auth/me`, {\r\n      method: \"GET\"\r\n    });\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        error: `Error: ${response.status}`\r\n      };\r\n    }\r\n\r\n    const userData = await response.json();\r\n\r\n    // More flexible check - look for any ID field\r\n    const userId = userData.userId || userData._id || userData.id;\r\n    const email = userData.email;\r\n\r\n    if (userId && email) {\r\n      // Normalize the user object to match expected structure\r\n      return {\r\n        success: true,\r\n        user: {\r\n          fullName: userData.fullName || email.split('@')[0],\r\n          userId: userId,\r\n          email: email,\r\n          role: userData.role || \"user\"\r\n        }\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Invalid user data received\"\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching user data:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An error occurred while fetching user data\"\r\n    };\r\n  }\r\n}\r\n\r\nexport function setupTokenRefresh(intervalMinutes = 10): () => void {\r\n  if (typeof window === 'undefined') return () => {};\r\n\r\n  const intervalId = setInterval(async () => {\r\n    // Only refresh if we're logged in\r\n    const accessToken = localStorage.getItem('access_token');\r\n    if (accessToken) {\r\n      try {\r\n        await refreshAccessToken();\r\n      } catch (error) {\r\n        console.error('Background token refresh failed:', error);\r\n      }\r\n    }\r\n  }, intervalMinutes * 60 * 1000);\r\n\r\n  // Return cleanup function\r\n  return () => clearInterval(intervalId);\r\n}\r\n\r\n// Client-side version of refreshAccessToken\r\nexport async function refreshAccessToken(): Promise<{\r\n  success: boolean;\r\n  newAccessToken?: string;\r\n}> {\r\n  const refreshToken = localStorage.getItem('refresh_token');\r\n\r\n  if (!refreshToken) {\r\n    return { success: false };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ refreshToken }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      return { success: false };\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (data.access_token) {\r\n      localStorage.setItem('access_token', data.access_token);\r\n      return {\r\n        success: true,\r\n        newAccessToken: data.access_token,\r\n      };\r\n    }\r\n\r\n    return { success: false };\r\n  } catch (error) {\r\n    console.error(\"Token refresh error:\", error);\r\n    return { success: false };\r\n  }\r\n}\r\n\r\n// Client-side logout function\r\nexport function logoutClient(): void {\r\n  localStorage.removeItem('access_token');\r\n  localStorage.removeItem('refresh_token');\r\n  localStorage.removeItem('user_data');\r\n}"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;AAWA,MAAM,eAAe,6DAAsC;AAGpD,eAAe;IAKpB,IAAI;QACF,qDAAqD;QACrD,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,GAAG,aAAa,YAAY,CAAC,EAAE;YAC9D,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,OAAO,EAAE,SAAS,MAAM,EAAE;YACpC;QACF;QAEA,MAAM,WAAW,MAAM,SAAS,IAAI;QAEpC,8CAA8C;QAC9C,MAAM,SAAS,SAAS,MAAM,IAAI,SAAS,GAAG,IAAI,SAAS,EAAE;QAC7D,MAAM,QAAQ,SAAS,KAAK;QAE5B,IAAI,UAAU,OAAO;YACnB,wDAAwD;YACxD,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,UAAU,SAAS,QAAQ,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;oBAClD,QAAQ;oBACR,OAAO;oBACP,MAAM,SAAS,IAAI,IAAI;gBACzB;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEO,SAAS,kBAAkB,kBAAkB,EAAE;IACpD,wCAAmC,OAAO,KAAO;;IAEjD,MAAM;AAcR;AAGO,eAAe;IAIpB,MAAM,eAAe,aAAa,OAAO,CAAC;IAE1C,IAAI,CAAC,cAAc;QACjB,OAAO;YAAE,SAAS;QAAM;IAC1B;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC,EAAE;YAC/D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAa;QACtC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBAAE,SAAS;YAAM;QAC1B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,YAAY,EAAE;YACrB,aAAa,OAAO,CAAC,gBAAgB,KAAK,YAAY;YACtD,OAAO;gBACL,SAAS;gBACT,gBAAgB,KAAK,YAAY;YACnC;QACF;QAEA,OAAO;YAAE,SAAS;QAAM;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YAAE,SAAS;QAAM;IAC1B;AACF;AAGO,SAAS;IACd,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/%28auth%29/login/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState} from \"react\";\r\n// import Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useFormStatus } from \"react-dom\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\nimport { loginUser, ActionResult } from \"@/app/(auth)/actions/auth\";\r\nimport ReCAPTCHA from \"react-google-recaptcha\";\r\nimport Image from \"next/image\";\r\nimport OrovaLogo from '@/assets/img/OROVA-WHITE.png';\r\nimport { getCurrentUser } from \"@/lib/auth-client\";\r\nimport { Eye, EyeOff } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nfunction SubmitButton() {\r\n  const { pending } = useFormStatus();\r\n\r\n  return (\r\n    <Button\r\n      type=\"submit\"\r\n      className=\"w-full h-12 bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md\"\r\n      disabled={pending}\r\n    >\r\n      {pending ? (\r\n        <div className=\"flex items-center justify-center\">\r\n          <div className=\"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2\" />\r\n          Signing in...\r\n        </div>\r\n      ) : (\r\n        \"Sign In\"\r\n      )}\r\n    </Button>\r\n  );\r\n}\r\n\r\nexport default function Login() {\r\n\r\n  const router = useRouter();\r\n  const [formState, setFormState] = useState<ActionResult | null>(null);\r\n  const [formErrors, setFormErrors] = useState<Record<string, string>>({});\r\n  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);\r\n  const [showPassword, setShowPassword] = useState(false); // State to toggle password visibility\r\n\r\n\r\n\r\n  async function handleSubmit(formData: FormData) {\r\n    if (!recaptchaToken) {\r\n      setFormState({\r\n        success: false,\r\n        message: \"Please complete the CAPTCHA.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    formData.append(\"recaptchaToken\", recaptchaToken);\r\n\r\n    const result = await loginUser(formData);\r\n    setFormState(result);\r\n\r\n    if (result.success && result.redirect && result.tokens) {\r\n      // Store tokens in localStorage\r\n      localStorage.setItem('access_token', result.tokens.access_token);\r\n      localStorage.setItem('refresh_token', result.tokens.refresh_token);\r\n\r\n      // Check if the user is actually authorized\r\n      const userResult = await getCurrentUser();\r\n\r\n      if (!userResult.success) {\r\n        // If not authorized, show an error\r\n        setFormState({\r\n          success: false,\r\n          message: \"Account not authorized. Please contact an administrator.\",\r\n        });\r\n\r\n        // Remove the tokens since they're not useful\r\n        localStorage.removeItem('access_token');\r\n        localStorage.removeItem('refresh_token');\r\n        localStorage.removeItem('user_data');\r\n        return;\r\n      }\r\n\r\n      // Store user data in localStorage for other components to use\r\n      if (userResult.user) {\r\n        localStorage.setItem('user_data', JSON.stringify({\r\n          ...userResult.user,\r\n          _id: userResult.user.userId // Add _id for compatibility with CreditContext\r\n        }));\r\n      }\r\n\r\n      router.push(result.redirect);\r\n      return;\r\n    }\r\n\r\n    // Handle validation errors\r\n    if (result.fieldErrors) {\r\n      const errors: Record<string, string> = {};\r\n      Object.entries(result.fieldErrors).forEach(([key, messages]) => {\r\n        if (Array.isArray(messages) && messages.length > 0) {\r\n          errors[key] = messages[0];\r\n        }\r\n      });\r\n      setFormErrors(errors);\r\n    } else {\r\n      setFormErrors({});\r\n    }\r\n  }\r\n\r\n\r\n\r\n  return (\r\n    <>\r\n    <div className=\"min-h-screen flex flex-col md:flex-row\">\r\n      {/* Left Side - Brand Panel */}\r\n      <div className=\"hidden md:flex md:w-1/2 bg-gradient-to-br from-[#383D73] to-[#74546D] text-white flex-col justify-center items-center p-8\">\r\n        <div className=\"max-w-md mx-auto flex flex-col items-center space-y-12\">\r\n          <Image\r\n            src={OrovaLogo}\r\n            alt=\"Orova Logo\"\r\n            width={280}\r\n            height={70}\r\n          />\r\n\r\n          <div className=\"text-center space-y-4\">\r\n            <h1 className=\"text-4xl font-bold\">Welcome to Orova AI</h1>\r\n            <p className=\"text-lg opacity-80\">\r\n              The next generation calling platform powered by artificial intelligence\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 w-full\">\r\n            <blockquote className=\"text-center italic\">\r\n              &quot;Orova transformed our customer engagement with intelligent calls that feel personal and professional.&quot;\r\n            </blockquote>\r\n            <div className=\"mt-4 flex items-center justify-center\">\r\n              <div className=\"h-px w-12 bg-white/30\"></div>\r\n              <div className=\"h-px w-12 bg-white/30\"></div>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right Side - Login Form */}\r\n      <div className=\"flex flex-1 items-center justify-center bg-gray-50 dark:bg-gray-900 p-8\">\r\n        <div className=\"w-full max-w-md\">\r\n          {/* Mobile Logo - Only visible on mobile */}\r\n          <div className=\"flex justify-center mb-8 md:hidden\">\r\n            <Image\r\n              src={OrovaLogo}\r\n              alt=\"Orova Logo\"\r\n              width={200}\r\n              height={50}\r\n              className=\"dark:filter dark:brightness-0 dark:invert\"\r\n            />\r\n          </div>\r\n\r\n          <Card className=\"bg-white dark:bg-gray-800 shadow-xl rounded-xl border-0\">\r\n            <CardHeader className=\"space-y-2 pb-2\">\r\n              <CardTitle className=\"text-center text-2xl font-bold bg-gradient-to-r from-[#383D73] to-[#74546D] bg-clip-text text-transparent dark:text-white\">\r\n                Welcome to Orova\r\n              </CardTitle>\r\n              <p className=\"text-center text-gray-500 dark:text-gray-400\">\r\n                Sign in to your account\r\n              </p>\r\n            </CardHeader>\r\n\r\n            <CardContent className=\"pt-4\">\r\n              {formState && !formState.success && !formState.fieldErrors && (\r\n                <Alert className=\"mb-4 bg-red-50 text-red-800 border-red-200\">\r\n                  <AlertDescription>{formState.message}</AlertDescription>\r\n                </Alert>\r\n              )}\r\n\r\n              <form action={handleSubmit} className=\"space-y-6\">\r\n                <div className=\"space-y-2\">\r\n                  <Input\r\n                    name=\"email\"\r\n                    type=\"email\"\r\n                    placeholder=\"Email\"\r\n                    required\r\n                    className=\"h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700\"\r\n                    aria-invalid={!!formErrors.email}\r\n                  />\r\n                  {formErrors.email && (\r\n                    <p className=\"text-sm text-red-500 mt-1\">\r\n                      {formErrors.email}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"relative\">\r\n                    <Input\r\n                      name=\"password\"\r\n                      type={showPassword ? \"text\" : \"password\"}\r\n                      placeholder=\"Password\"\r\n                      required\r\n                      className=\"h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700 pr-10\"\r\n                      aria-invalid={!!formErrors.password}\r\n                    />\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setShowPassword(prev => !prev)}\r\n                      className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n                      tabIndex={-1} // Skip tab focusing\r\n                      aria-label={showPassword ? \"Hide password\" : \"Show password\"}\r\n                    >\r\n                      {showPassword ? (\r\n                        <EyeOff className=\"h-5 w-5\" />\r\n                      ) : (\r\n                        <Eye className=\"h-5 w-5\" />\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                  {formErrors.password && (\r\n                    <p className=\"text-sm text-red-500 mt-1\">\r\n                      {formErrors.password}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n                <div className=\"flex justify-center\">\r\n                <ReCAPTCHA\r\n                  sitekey=\"6LfyPfgqAAAAAJy91ZTqWkEaQcGJJtNC-MnxUa6e\"\r\n                  onChange={(token) => setRecaptchaToken(token)}\r\n                />\r\n                </div>\r\n\r\n\r\n                <div className=\"flex items-center justify-between\">\r\n\r\n                  {/* <div className=\"text-sm\">\r\n                    <a href=\"#\" className=\"font-medium text-[#383D73] hover:text-[#74546D]\">\r\n                      Forgot password?\r\n                    </a>\r\n                  </div> */}\r\n                </div>\r\n\r\n                <SubmitButton />\r\n\r\n                {/* <div className=\"text-center text-sm mt-6 pt-4 border-t border-gray-200 dark:border-gray-700\">\r\n                  <Link\r\n                    href=\"/register\"\r\n                    className=\"text-[#383D73] hover:text-[#74546D] font-medium transition-colors duration-200\"\r\n                  >\r\n                    Need an account? Sign up\r\n                  </Link>\r\n                </div> */}\r\n              </form>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <div className=\"mt-6 text-center text-xs text-gray-500 dark:text-gray-400\">\r\n            &copy; {new Date().getFullYear()} Orova AI. All rights reserved.\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAfA;;;;;;;;;;;;;;;AAiBA,SAAS;IACP,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4MAAA,CAAA,gBAAa,AAAD;IAEhC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,MAAK;QACL,WAAU;QACV,UAAU;kBAET,wBACC,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;gBAA8E;;;;;;mBAI/F;;;;;;AAIR;AAEe,SAAS;IAEtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,sCAAsC;IAI/F,eAAe,aAAa,QAAkB;QAC5C,IAAI,CAAC,gBAAgB;YACnB,aAAa;gBACX,SAAS;gBACT,SAAS;YACX;YACA;QACF;QAEA,SAAS,MAAM,CAAC,kBAAkB;QAElC,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE;QAC/B,aAAa;QAEb,IAAI,OAAO,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAO,MAAM,EAAE;YACtD,+BAA+B;YAC/B,aAAa,OAAO,CAAC,gBAAgB,OAAO,MAAM,CAAC,YAAY;YAC/D,aAAa,OAAO,CAAC,iBAAiB,OAAO,MAAM,CAAC,aAAa;YAEjE,2CAA2C;YAC3C,MAAM,aAAa,MAAM,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;YAEtC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,mCAAmC;gBACnC,aAAa;oBACX,SAAS;oBACT,SAAS;gBACX;gBAEA,6CAA6C;gBAC7C,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB;YACF;YAEA,8DAA8D;YAC9D,IAAI,WAAW,IAAI,EAAE;gBACnB,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;oBAC/C,GAAG,WAAW,IAAI;oBAClB,KAAK,WAAW,IAAI,CAAC,MAAM,CAAC,+CAA+C;gBAC7E;YACF;YAEA,OAAO,IAAI,CAAC,OAAO,QAAQ;YAC3B;QACF;QAEA,2BAA2B;QAC3B,IAAI,OAAO,WAAW,EAAE;YACtB,MAAM,SAAiC,CAAC;YACxC,OAAO,OAAO,CAAC,OAAO,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,SAAS;gBACzD,IAAI,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,GAAG,GAAG;oBAClD,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,EAAE;gBAC3B;YACF;YACA,cAAc;QAChB,OAAO;YACL,cAAc,CAAC;QACjB;IACF;IAIA,qBACE;kBACA,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,4SAAA,CAAA,UAAS;gCACd,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAKpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAW,WAAU;kDAAqB;;;;;;kDAG3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,4SAAA,CAAA,UAAS;oCACd,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAId,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA4H;;;;;;0DAGjJ,8OAAC;gDAAE,WAAU;0DAA+C;;;;;;;;;;;;kDAK9D,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;4CACpB,aAAa,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,WAAW,kBACxD,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DACf,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;8DAAE,UAAU,OAAO;;;;;;;;;;;0DAIxC,8OAAC;gDAAK,QAAQ;gDAAc,WAAU;;kEACpC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,MAAK;gEACL,aAAY;gEACZ,QAAQ;gEACR,WAAU;gEACV,gBAAc,CAAC,CAAC,WAAW,KAAK;;;;;;4DAEjC,WAAW,KAAK,kBACf,8OAAC;gEAAE,WAAU;0EACV,WAAW,KAAK;;;;;;;;;;;;kEAKvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,MAAM,eAAe,SAAS;wEAC9B,aAAY;wEACZ,QAAQ;wEACR,WAAU;wEACV,gBAAc,CAAC,CAAC,WAAW,QAAQ;;;;;;kFAErC,8OAAC;wEACC,MAAK;wEACL,SAAS,IAAM,gBAAgB,CAAA,OAAQ,CAAC;wEACxC,WAAU;wEACV,UAAU,CAAC;wEACX,cAAY,eAAe,kBAAkB;kFAE5C,6BACC,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;iGAElB,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;;;;;;4DAIpB,WAAW,QAAQ,kBAClB,8OAAC;gEAAE,WAAU;0EACV,WAAW,QAAQ;;;;;;;;;;;;kEAI1B,8OAAC;wDAAI,WAAU;kEACf,cAAA,8OAAC,mLAAA,CAAA,UAAS;4DACR,SAAQ;4DACR,UAAU,CAAC,QAAU,kBAAkB;;;;;;;;;;;kEAKzC,8OAAC;wDAAI,WAAU;;;;;;kEASf,8OAAC;;;;;;;;;;;;;;;;;;;;;;;0CAcP,8OAAC;gCAAI,WAAU;;oCAA4D;oCACjE,IAAI,OAAO,WAAW;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/app/page.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\"use client\";\r\n\r\nimport AnimatedSection from \"../animations/AnimatedSection\";\r\nimport FadeIn from \"@/animations/FadeIn\";\r\nimport ScaleIn from \"@/animations/ScaleIn\";\r\nimport SlideIn from \"@/animations/SlideIn\";\r\nimport StaggerContainer from \"@/animations/StaggerContainer\";\r\nimport CTASection from \"@/components/CTASection\";\r\nimport FAQSection from \"@/components/FAQSection\";\r\nimport FeaturesGrid from \"@/components/FeaturesGrid\";\r\nimport Footer from \"@/components/Footer\";\r\nimport HeroSection from \"@/components/HeroSection\";\r\nimport Navigation from \"@/components/Navigation\";\r\nimport PricingSection from \"@/components/PricingSection\";\r\nimport Testimonials from \"@/components/Testimonials\";\r\nimport Login from \"./(auth)/login/page\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { authFetch } from \"@/lib/authFetch\";\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || \"\";\r\n\r\nexport default function Home() {\r\n  const router = useRouter();\r\n  const [loading, setLoading] = useState(true);\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Check if user is already logged in\r\n    async function checkAuthStatus() {\r\n      try {\r\n        // Check for token in localStorage\r\n        const token = localStorage.getItem(\"access_token\");\r\n        \r\n        if (!token) {\r\n          // No token found, stay on login page\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        // Verify token validity by making a request to the API\r\n        const response = await authFetch(`${API_BASE_URL}/api/auth/me`);\r\n        \r\n        if (response.ok) {\r\n          // User is authenticated, redirect to dashboard\r\n          setIsAuthenticated(true);\r\n          router.push(\"/dashboard\");\r\n        } else {\r\n          // Invalid token, stay on login page\r\n          setLoading(false);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking authentication status:\", error);\r\n        setLoading(false);\r\n      }\r\n    }\r\n\r\n    checkAuthStatus();\r\n  }, [router]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"flex flex-col items-center space-y-4\">\r\n          <div className=\"h-12 w-12 border-4 border-t-blue-500 border-gray-200 rounded-full animate-spin\"></div>\r\n          <p className=\"text-lg font-medium\">Loading...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n    {/* <div className=\"min-h-screen bg-white dark:bg-gray-950 text-gray-900 dark:text-white \">\r\n     \r\n      <Navigation />\r\n    \r\n      <FadeIn>\r\n        <HeroSection />\r\n      </FadeIn>\r\n  \r\n      <AnimatedSection delay={0.5} animation=\"stagger\">\r\n        <FeaturesGrid />\r\n      </AnimatedSection>\r\n   \r\n      <SlideIn delay={0.5} direction=\"up\">\r\n        <Testimonials />\r\n      </SlideIn>\r\n   \r\n      <ScaleIn delay={0.5}>\r\n        <PricingSection />\r\n      </ScaleIn>\r\n  \r\n      <StaggerContainer delay={0.5}>\r\n        <FAQSection />\r\n      </StaggerContainer>\r\n    \r\n      <FadeIn delay={0.5} direction=\"up\" distance={50}>\r\n        <CTASection />\r\n      </FadeIn>\r\n    \r\n      <Footer />\r\n    </div> */}\r\n   <Login/>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AAgBpD;AACA;AACA;AACA;AAlBA;;;;;;AAoBA,MAAM,eAAe,6DAAsC;AAE5C,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,eAAe;YACb,IAAI;gBACF,kCAAkC;gBAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;gBAEnC,IAAI,CAAC,OAAO;oBACV,qCAAqC;oBACrC,WAAW;oBACX;gBACF;gBAEA,uDAAuD;gBACvD,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,GAAG,aAAa,YAAY,CAAC;gBAE9D,IAAI,SAAS,EAAE,EAAE;oBACf,+CAA+C;oBAC/C,mBAAmB;oBACnB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,oCAAoC;oBACpC,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;;;;;;;;;;;;IAI3C;IAEA,qBACE;kBA+BD,cAAA,8OAAC,wIAAA,CAAA,UAAK;;;;;;AAGT", "debugId": null}}]}