import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { KnowledgeBaseFolder, KnowledgeBaseItem } from './interfaces/knowledge-base.interface';
import { CreateKnowledgeBaseFolderDto, UpdateKnowledgeBaseFolderDto, CreateKnowledgeBaseItemDto, UpdateKnowledgeBaseItemDto } from './dto/knowledge-base.dto';
import { S3Service } from '../s3/s3.service';
import { OrganizationsService } from '../organizations/organizations.service';

@Injectable()
export class KnowledgeBaseService {
  constructor(
    @InjectModel('KnowledgeBaseFolder') private folderModel: Model<KnowledgeBaseFolder>,
    @InjectModel('KnowledgeBaseItem') private itemModel: Model<KnowledgeBaseItem>,
    private s3Service: S3Service,
    private organizationsService: OrganizationsService,
  ) {}

  // Folder operations
  async createFolder(
    organizationId: string,
    userId: string,
    createFolderDto: CreateKnowledgeBaseFolderDto
  ): Promise<KnowledgeBaseFolder> {
    const folder = new this.folderModel({
      ...createFolderDto,
      organizationId,
      createdBy: userId,
    });

    return folder.save();
  }

  async getFoldersByOrganization(organizationId: string): Promise<KnowledgeBaseFolder[]> {
    return this.folderModel.find({ organizationId }).sort({ createdAt: -1 }).exec();
  }

  async getFolderById(folderId: string, organizationId: string): Promise<KnowledgeBaseFolder> {
    const folder = await this.folderModel.findOne({ _id: folderId, organizationId }).exec();
    if (!folder) {
      throw new NotFoundException('Folder not found');
    }
    return folder;
  }

  async updateFolder(
    folderId: string,
    organizationId: string,
    updateFolderDto: UpdateKnowledgeBaseFolderDto
  ): Promise<KnowledgeBaseFolder> {
    const folder = await this.folderModel.findOneAndUpdate(
      { _id: folderId, organizationId },
      { ...updateFolderDto, updatedAt: new Date() },
      { new: true }
    ).exec();

    if (!folder) {
      throw new NotFoundException('Folder not found');
    }

    return folder;
  }

  async deleteFolder(folderId: string, organizationId: string): Promise<void> {
    // First, delete all items in the folder
    const items = await this.itemModel.find({ folderId, organizationId }).exec();
    
    // Delete S3 files for document items
    const organization = await this.organizationsService.findOne(organizationId);
    if (organization.s3Config && organization.s3Config.enabled) {
      const s3Config = {
        accessKeyId: organization.s3Config.accessKeyId,
        secretAccessKey: organization.s3Config.secretAccessKey,
        region: organization.s3Config.region,
        bucketName: organization.s3Config.bucketName,
      };

      for (const item of items) {
        if (item.type === 'document' && item.s3Key) {
          try {
            await this.s3Service.deleteFile(s3Config, item.s3Key);
          } catch (error) {
            console.error(`Failed to delete S3 file ${item.s3Key}:`, error);
          }
        }
      }
    }

    // Delete all items in the folder
    await this.itemModel.deleteMany({ folderId, organizationId }).exec();

    // Delete the folder
    const result = await this.folderModel.deleteOne({ _id: folderId, organizationId }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException('Folder not found');
    }
  }

  // Item operations
  async createItem(
    organizationId: string,
    userId: string,
    createItemDto: CreateKnowledgeBaseItemDto
  ): Promise<KnowledgeBaseItem> {
    // Verify folder exists and belongs to organization
    await this.getFolderById(createItemDto.folderId, organizationId);

    const item = new this.itemModel({
      ...createItemDto,
      organizationId,
      uploadedBy: userId,
      status: createItemDto.type === 'document' ? 'uploading' : 'ready',
    });

    return item.save();
  }

  async uploadDocumentToS3(
    itemId: string,
    organizationId: string,
    file: Buffer,
    originalName: string,
    mimeType: string
  ): Promise<KnowledgeBaseItem> {
    const item = await this.itemModel.findOne({ _id: itemId, organizationId }).exec();
    if (!item) {
      throw new NotFoundException('Knowledge base item not found');
    }

    if (item.type !== 'document') {
      throw new BadRequestException('Item is not a document type');
    }

    // Get organization S3 config
    const organization = await this.organizationsService.findOne(organizationId);
    if (!organization.s3Config || !organization.s3Config.enabled) {
      throw new BadRequestException('S3 is not configured for this organization');
    }

    const s3Config = {
      accessKeyId: organization.s3Config.accessKeyId,
      secretAccessKey: organization.s3Config.secretAccessKey,
      region: organization.s3Config.region,
      bucketName: organization.s3Config.bucketName,
    };

    try {
      // Generate unique filename
      const timestamp = Date.now();
      const fileExtension = originalName.split('.').pop();
      const fileName = `${timestamp}-${originalName}`;

      // Upload to S3
      const uploadResult = await this.s3Service.uploadFile(
        s3Config,
        file,
        fileName,
        mimeType,
        organizationId,
        item.folderId
      );

      // Update item with S3 information
      item.filename = fileName;
      item.originalName = originalName;
      item.mimeType = mimeType;
      item.size = file.length;
      item.s3Key = uploadResult.key;
      item.s3Url = uploadResult.url;
      item.s3Bucket = s3Config.bucketName;
      item.status = 'ready';
      item.updatedAt = new Date();

      return item.save();
    } catch (error) {
      // Update item status to error
      item.status = 'error';
      item.errorMessage = error.message;
      item.updatedAt = new Date();
      await item.save();
      
      throw error;
    }
  }

  async getItemsByFolder(folderId: string, organizationId: string): Promise<KnowledgeBaseItem[]> {
    // Verify folder exists and belongs to organization
    await this.getFolderById(folderId, organizationId);

    return this.itemModel.find({ folderId, organizationId }).sort({ createdAt: -1 }).exec();
  }

  async getItemById(itemId: string, organizationId: string): Promise<KnowledgeBaseItem> {
    const item = await this.itemModel.findOne({ _id: itemId, organizationId }).exec();
    if (!item) {
      throw new NotFoundException('Knowledge base item not found');
    }
    return item;
  }

  async updateItem(
    itemId: string,
    organizationId: string,
    updateItemDto: UpdateKnowledgeBaseItemDto
  ): Promise<KnowledgeBaseItem> {
    const item = await this.itemModel.findOneAndUpdate(
      { _id: itemId, organizationId },
      { ...updateItemDto, updatedAt: new Date() },
      { new: true }
    ).exec();

    if (!item) {
      throw new NotFoundException('Knowledge base item not found');
    }

    return item;
  }

  async deleteItem(itemId: string, organizationId: string): Promise<void> {
    const item = await this.itemModel.findOne({ _id: itemId, organizationId }).exec();
    if (!item) {
      throw new NotFoundException('Knowledge base item not found');
    }

    // Delete S3 file if it exists
    if (item.type === 'document' && item.s3Key) {
      const organization = await this.organizationsService.findOne(organizationId);
      if (organization.s3Config && organization.s3Config.enabled) {
        const s3Config = {
          accessKeyId: organization.s3Config.accessKeyId,
          secretAccessKey: organization.s3Config.secretAccessKey,
          region: organization.s3Config.region,
          bucketName: organization.s3Config.bucketName,
        };

        try {
          await this.s3Service.deleteFile(s3Config, item.s3Key);
        } catch (error) {
          console.error(`Failed to delete S3 file ${item.s3Key}:`, error);
        }
      }
    }

    // Delete the item
    await this.itemModel.deleteOne({ _id: itemId, organizationId }).exec();
  }

  async getPresignedDownloadUrl(itemId: string, organizationId: string): Promise<string> {
    const item = await this.getItemById(itemId, organizationId);
    
    if (item.type !== 'document' || !item.s3Key) {
      throw new BadRequestException('Item is not a downloadable document');
    }

    const organization = await this.organizationsService.findOne(organizationId);
    if (!organization.s3Config || !organization.s3Config.enabled) {
      throw new BadRequestException('S3 is not configured for this organization');
    }

    const s3Config = {
      accessKeyId: organization.s3Config.accessKeyId,
      secretAccessKey: organization.s3Config.secretAccessKey,
      region: organization.s3Config.region,
      bucketName: organization.s3Config.bucketName,
    };

    return this.s3Service.getPresignedUrl(s3Config, item.s3Key, 3600); // 1 hour expiry
  }
}
