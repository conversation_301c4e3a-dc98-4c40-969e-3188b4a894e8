import { authFetch } from '@/lib/authFetch';

const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';

export interface S3Config {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  bucketName: string;
}

export interface S3TestResult {
  success: boolean;
  message: string;
}

export interface S3UploadResult {
  success: boolean;
  message: string;
  data: {
    key: string;
    url: string;
    size: number;
  };
}

export interface S3DeleteResult {
  success: boolean;
  message: string;
}

export interface S3ListResult {
  success: boolean;
  message: string;
  data: Array<{
    Key: string;
    LastModified: string;
    Size: number;
    StorageClass: string;
  }>;
}

/**
 * Test S3 connection with provided credentials
 */
export const testS3Connection = async (config: S3Config): Promise<S3TestResult> => {
  const response = await authFetch(`${API_URL}/api/s3/test-connection`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(config),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to test S3 connection');
  }

  return response.json();
};

/**
 * Upload file to organization S3 bucket
 */
export const uploadFileToS3 = async (
  organizationId: string,
  file: File,
  knowledgeBaseId?: string
): Promise<S3UploadResult> => {
  const formData = new FormData();
  formData.append('file', file);
  
  if (knowledgeBaseId) {
    formData.append('knowledgeBaseId', knowledgeBaseId);
  }

  const response = await authFetch(`${API_URL}/api/s3/upload/${organizationId}`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to upload file to S3');
  }

  return response.json();
};

/**
 * Delete file from organization S3 bucket
 */
export const deleteFileFromS3 = async (
  organizationId: string,
  fileKey: string
): Promise<S3DeleteResult> => {
  // URL encode the file key to handle special characters
  const encodedKey = encodeURIComponent(fileKey);
  
  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/file/${encodedKey}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete file from S3');
  }

  return response.json();
};

/**
 * List files in organization S3 bucket
 */
export const listS3Files = async (organizationId: string): Promise<S3ListResult> => {
  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/files`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to list S3 files');
  }

  return response.json();
};

/**
 * Get presigned URL for file download
 */
export const getS3PresignedUrl = async (
  organizationId: string,
  fileKey: string
): Promise<{ success: boolean; message: string; data: { url: string } }> => {
  // URL encode the file key to handle special characters
  const encodedKey = encodeURIComponent(fileKey);
  
  const response = await authFetch(`${API_URL}/api/s3/${organizationId}/presigned-url/${encodedKey}`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to get presigned URL');
  }

  return response.json();
};

/**
 * Helper function to format file size
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Helper function to get file extension
 */
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * Helper function to get file type icon
 */
export const getFileTypeIcon = (filename: string): string => {
  const extension = getFileExtension(filename).toLowerCase();
  
  switch (extension) {
    case 'pdf':
      return '📄';
    case 'doc':
    case 'docx':
      return '📝';
    case 'xls':
    case 'xlsx':
      return '📊';
    case 'txt':
      return '📋';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return '🖼️';
    case 'mp4':
    case 'avi':
    case 'mov':
      return '🎥';
    case 'mp3':
    case 'wav':
      return '🎵';
    case 'zip':
    case 'rar':
      return '📦';
    default:
      return '📁';
  }
};
