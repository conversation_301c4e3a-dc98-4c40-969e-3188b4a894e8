import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MongooseModule } from "@nestjs/mongoose";
import { JwtModule } from "@nestjs/jwt";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { UsersModule } from "./users/users.module";
import { AuthModule } from "./auth/auth.module";
import { AgentModule } from "./agent/agent.module";
import { AiModule } from "./ai/ai.module";
import { ConversationsModule } from "./conversations/conversations.module";
import { VapiModule } from "./vapi/vapi.module";
import { ContactsModule } from "./contacts/contacts.module";
import { HistoryModule } from "./history/history.module";
import { CampaignModule } from "./campaign/campaign.module";
import { ScheduledCallModule } from './scheduled-call/scheduled-call.module';
import { ScheduleModule } from "@nestjs/schedule";
import { join } from "path";
import { ServeStaticModule } from "@nestjs/serve-static";
import { TokenModule } from "./token/token.module";
import { DashboardModule } from "./dashboard/dahboard.module";
import { BillingModule } from "./billing/billing.module";
import { CreditModule } from "./credit/credit.module";
import { GlobalSettingsModule } from "./global-settings/global-settings.module";
import { OrganizationsModule } from "./organizations/organizations.module";
import { WebsocketModule } from "./websocket/websocket.module";
import { PhoneNumberModule } from "./phone-number/phone-number.module";
import { EmailModule } from "./email/email.module";
import { NotificationsModule } from "./notifications/notifications.module";
import { S3Module } from "./s3/s3.module";
import { S3Controller } from "./s3/s3.controller";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ScheduleModule.forRoot(),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get<string>("MONGODB_URI"),
      }),
      inject: [ConfigService],
    }),
    ServeStaticModule.forRoot({
       rootPath: '/usr/src/app/uploads', // Updated path
      serveRoot: '/api/uploads',
      serveStaticOptions: {
        index: false,
        fallthrough: true,
      }
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>("JWT_SECRET"),
        signOptions: { expiresIn: "60m" },
      }),
      inject: [ConfigService],
    }),
    UsersModule,
    AuthModule,
    AgentModule,
    AiModule,
    ConversationsModule,
    VapiModule,
    ContactsModule,
    HistoryModule,
    CampaignModule,
    ScheduledCallModule,
    TokenModule,
    DashboardModule,
    BillingModule,
    CreditModule,
    GlobalSettingsModule,
    OrganizationsModule,
    WebsocketModule,
    PhoneNumberModule,
    EmailModule,
    NotificationsModule,
    S3Module
  ],
  controllers: [AppController, S3Controller],
  providers: [AppService],
})
export class AppModule {}
