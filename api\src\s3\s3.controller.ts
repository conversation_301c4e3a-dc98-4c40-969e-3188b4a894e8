import {
  Controller,
  Post,
  Delete,
  Get,
  Body,
  Param,
  UseGuards,
  Req,
  HttpException,
  HttpStatus,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { S3Service, S3Config } from './s3.service';
import { OrganizationsService } from '../organizations/organizations.service';
import { RequestWithUser } from '../auth/interfaces/request-with-user.interface';

class TestS3ConnectionDto {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  bucketName: string;
}

class UploadFileDto {
  organizationId: string;
  knowledgeBaseId?: string;
  fileName: string;
}

@ApiTags('S3')
@Controller('s3')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class S3Controller {
  constructor(
    private readonly s3Service: S3Service,
    private readonly organizationsService: OrganizationsService,
  ) {}

  @Post('test-connection')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Test S3 connection with provided credentials' })
  @ApiResponse({ status: 200, description: 'Connection test result' })
  @ApiBody({ type: TestS3ConnectionDto })
  async testConnection(@Body() testDto: TestS3ConnectionDto) {
    try {
      const config: S3Config = {
        accessKeyId: testDto.accessKeyId,
        secretAccessKey: testDto.secretAccessKey,
        region: testDto.region,
        bucketName: testDto.bucketName,
      };

      const result = await this.s3Service.testConnection(config);
      return result;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to test S3 connection',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('upload/:organizationId')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Upload file to organization S3 bucket' })
  @ApiResponse({ status: 201, description: 'File uploaded successfully' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Param('organizationId') organizationId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: { knowledgeBaseId?: string },
    @Req() req: RequestWithUser,
  ) {
    try {
      // Check if user has access to this organization
      if (req.user.role !== 'superadmin') {
        const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
        const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      // Get organization and S3 config
      const organization = await this.organizationsService.findOne(organizationId);
      
      if (!organization.s3Config || !organization.s3Config.enabled) {
        throw new BadRequestException('S3 is not configured or enabled for this organization');
      }

      if (!file) {
        throw new BadRequestException('No file provided');
      }

      // Validate file size (50MB limit)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        throw new BadRequestException('File size exceeds 50MB limit');
      }

      const config: S3Config = {
        accessKeyId: organization.s3Config.accessKeyId,
        secretAccessKey: organization.s3Config.secretAccessKey,
        region: organization.s3Config.region,
        bucketName: organization.s3Config.bucketName,
      };

      // Generate unique filename
      const timestamp = Date.now();
      const fileName = `${timestamp}-${file.originalname}`;

      const result = await this.s3Service.uploadFile(
        config,
        file.buffer,
        fileName,
        file.mimetype,
        organizationId,
        uploadDto.knowledgeBaseId,
      );

      return {
        success: true,
        message: 'File uploaded successfully',
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to upload file',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':organizationId/file/:key')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Delete file from organization S3 bucket' })
  @ApiResponse({ status: 200, description: 'File deleted successfully' })
  async deleteFile(
    @Param('organizationId') organizationId: string,
    @Param('key') key: string,
    @Req() req: RequestWithUser,
  ) {
    try {
      // Check if user has access to this organization
      if (req.user.role !== 'superadmin') {
        const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
        const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      // Get organization and S3 config
      const organization = await this.organizationsService.findOne(organizationId);
      
      if (!organization.s3Config || !organization.s3Config.enabled) {
        throw new BadRequestException('S3 is not configured or enabled for this organization');
      }

      const config: S3Config = {
        accessKeyId: organization.s3Config.accessKeyId,
        secretAccessKey: organization.s3Config.secretAccessKey,
        region: organization.s3Config.region,
        bucketName: organization.s3Config.bucketName,
      };

      // Decode the key parameter (it might be URL encoded)
      const decodedKey = decodeURIComponent(key);

      const result = await this.s3Service.deleteFile(config, decodedKey);

      return {
        success: true,
        message: 'File deleted successfully',
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to delete file',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':organizationId/files')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'List files in organization S3 bucket' })
  @ApiResponse({ status: 200, description: 'Files listed successfully' })
  async listFiles(
    @Param('organizationId') organizationId: string,
    @Req() req: RequestWithUser,
  ) {
    try {
      // Check if user has access to this organization
      if (req.user.role !== 'superadmin') {
        const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
        const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      // Get organization and S3 config
      const organization = await this.organizationsService.findOne(organizationId);
      
      if (!organization.s3Config || !organization.s3Config.enabled) {
        throw new BadRequestException('S3 is not configured or enabled for this organization');
      }

      const config: S3Config = {
        accessKeyId: organization.s3Config.accessKeyId,
        secretAccessKey: organization.s3Config.secretAccessKey,
        region: organization.s3Config.region,
        bucketName: organization.s3Config.bucketName,
      };

      const files = await this.s3Service.listFiles(config, organizationId);

      return {
        success: true,
        message: 'Files listed successfully',
        data: files,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to list files',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':organizationId/presigned-url/:key')
  @Roles('superadmin', 'admin')
  @ApiOperation({ summary: 'Get presigned URL for file download' })
  @ApiResponse({ status: 200, description: 'Presigned URL generated successfully' })
  async getPresignedUrl(
    @Param('organizationId') organizationId: string,
    @Param('key') key: string,
    @Req() req: RequestWithUser,
  ) {
    try {
      // Check if user has access to this organization
      if (req.user.role !== 'superadmin') {
        const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
        const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      // Get organization and S3 config
      const organization = await this.organizationsService.findOne(organizationId);
      
      if (!organization.s3Config || !organization.s3Config.enabled) {
        throw new BadRequestException('S3 is not configured or enabled for this organization');
      }

      const config: S3Config = {
        accessKeyId: organization.s3Config.accessKeyId,
        secretAccessKey: organization.s3Config.secretAccessKey,
        region: organization.s3Config.region,
        bucketName: organization.s3Config.bucketName,
      };

      // Decode the key parameter
      const decodedKey = decodeURIComponent(key);

      const url = await this.s3Service.getPresignedUrl(config, decodedKey);

      return {
        success: true,
        message: 'Presigned URL generated successfully',
        data: { url },
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to generate presigned URL',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
