import { KnowledgeBaseService } from './knowledge-base.service';
import { CreateKnowledgeBaseFolderDto, UpdateKnowledgeBaseFolderDto, CreateKnowledgeBaseItemDto, UpdateKnowledgeBaseItemDto } from './dto/knowledge-base.dto';
import { OrganizationsService } from '../organizations/organizations.service';
interface RequestWithUser extends Request {
    user: {
        userId: string;
        email: string;
        role: string;
    };
}
export declare class KnowledgeBaseController {
    private readonly knowledgeBaseService;
    private readonly organizationsService;
    constructor(knowledgeBaseService: KnowledgeBaseService, organizationsService: OrganizationsService);
    createFolder(organizationId: string, createFolderDto: CreateKnowledgeBaseFolderDto, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseFolder;
    }>;
    getFolders(organizationId: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseFolder[];
    }>;
    getFolder(organizationId: string, folderId: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseFolder;
    }>;
    updateFolder(organizationId: string, folderId: string, updateFolderDto: UpdateKnowledgeBaseFolderDto, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseFolder;
    }>;
    deleteFolder(organizationId: string, folderId: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
    }>;
    createItem(organizationId: string, createItemDto: CreateKnowledgeBaseItemDto, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseItem;
    }>;
    uploadDocument(organizationId: string, itemId: string, file: Express.Multer.File, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseItem;
    }>;
    getItemsByFolder(organizationId: string, folderId: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseItem[];
    }>;
    getItem(organizationId: string, itemId: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseItem;
    }>;
    updateItem(organizationId: string, itemId: string, updateItemDto: UpdateKnowledgeBaseItemDto, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: import("./interfaces/knowledge-base.interface").KnowledgeBaseItem;
    }>;
    deleteItem(organizationId: string, itemId: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
    }>;
    getDownloadUrl(organizationId: string, itemId: string, req: RequestWithUser): Promise<{
        success: boolean;
        message: string;
        data: {
            url: string;
        };
    }>;
    private checkOrganizationAccess;
}
export {};
