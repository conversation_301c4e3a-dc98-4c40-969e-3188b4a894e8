"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus } from "lucide-react";
import { KnowledgeBaseFolderSidebar } from "@/components/braincomponents/KnowledgeBaseFolderSidebar";
import { KnowledgeBaseContent } from "@/components/braincomponents/KnowledgeBaseContent";
import { CreateFolderModal } from "@/components/braincomponents/CreateFolderModal";
import { AddContentModal } from "@/components/braincomponents/AddContentModal";
import { getMyOrganizations, Organization } from "@/app/api/organizations";
import { deleteFileFromS3 } from "@/app/api/s3";
import {
  getKnowledgeBaseFolders,
  getKnowledgeBaseItems,
  createKnowledgeBaseFolder,
  createKnowledgeBaseItem,
  uploadKnowledgeBaseDocument,
  deleteKnowledgeBaseFolder,
  deleteKnowledgeBaseItem,
  KnowledgeBaseFolder as ApiKnowledgeBaseFolder,
  KnowledgeBaseItem as ApiKnowledgeBaseItem,
} from "@/app/api/knowledge-base";
import { toast } from "sonner";

// Use API types
type KnowledgeBaseFolder = ApiKnowledgeBaseFolder & {
  itemCount?: number; // We'll calculate this locally
};

type KnowledgeBaseItem = ApiKnowledgeBaseItem;

export default function BrainContent() {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);

  // Modal states
  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);
  const [isAddContentModalOpen, setIsAddContentModalOpen] = useState(false);

  // Data states
  const [folders, setFolders] = useState<KnowledgeBaseFolder[]>([]);
  const [items, setItems] = useState<KnowledgeBaseItem[]>([]);

  // Load organization and initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const organizations = await getMyOrganizations();
        if (organizations.length > 0) {
          const org = organizations[0];
          setCurrentOrganization(org);

          // Load folders for this organization
          const foldersData = await getKnowledgeBaseFolders(org._id);
          const foldersWithCount = foldersData.map(folder => ({
            ...folder,
            itemCount: 0 // We'll calculate this when loading items
          }));
          setFolders(foldersWithCount);

          // Select first folder if available
          if (foldersData.length > 0) {
            setSelectedFolderId(foldersData[0]._id);
          }
        }
      } catch (error) {
        console.error("Failed to load data:", error);
        toast.error("Failed to load knowledge base data");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Load items when folder selection changes
  useEffect(() => {
    const loadItems = async () => {
      if (!currentOrganization || !selectedFolderId) {
        setItems([]);
        return;
      }

      try {
        const itemsData = await getKnowledgeBaseItems(currentOrganization._id, selectedFolderId);
        setItems(itemsData);

        // Update folder item count
        setFolders(folders.map(folder =>
          folder._id === selectedFolderId
            ? { ...folder, itemCount: itemsData.length }
            : folder
        ));
      } catch (error) {
        console.error("Failed to load items:", error);
        toast.error("Failed to load folder items");
      }
    };

    loadItems();
  }, [currentOrganization, selectedFolderId]);

  // Handlers
  const handleCreateFolder = async (name: string, description?: string) => {
    if (!currentOrganization) {
      toast.error("No organization selected");
      return;
    }

    try {
      const newFolder = await createKnowledgeBaseFolder(currentOrganization._id, {
        name,
        description
      });

      const folderWithCount = { ...newFolder, itemCount: 0 };
      setFolders([...folders, folderWithCount]);
      toast.success("Folder created successfully");
    } catch (error) {
      console.error("Failed to create folder:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create folder");
    }
  };

  const handleSelectFolder = (folderId: string) => {
    setSelectedFolderId(folderId);
  };

  const handleAddContent = async (type: "document" | "url" | "text", data: any) => {
    if (!selectedFolderId || !currentOrganization) {
      toast.error("No folder or organization selected");
      return;
    }

    try {
      // Create the knowledge base item first
      const newItem = await createKnowledgeBaseItem(currentOrganization._id, {
        title: data.title,
        type,
        folderId: selectedFolderId,
        url: data.url,
        content: data.content,
        originalName: data.filename,
        size: data.size,
        pages: data.pages
      });

      // If it's a document with a file, upload it
      if (type === "document" && data.file) {
        try {
          const uploadedItem = await uploadKnowledgeBaseDocument(
            currentOrganization._id,
            newItem._id,
            data.file
          );

          // Update the items list with the uploaded item
          setItems([...items, uploadedItem]);
          toast.success("Document uploaded successfully");
        } catch (uploadError) {
          console.error("Failed to upload document:", uploadError);
          toast.error("Failed to upload document file");
          // Still add the item but with error status
          setItems([...items, { ...newItem, status: "error", errorMessage: "Upload failed" }]);
        }
      } else {
        // For URL and text items, just add them
        setItems([...items, newItem]);
        toast.success(`${type} item created successfully`);
      }

      // Update folder item count
      setFolders(folders.map(folder =>
        folder._id === selectedFolderId
          ? { ...folder, itemCount: (folder.itemCount || 0) + 1 }
          : folder
      ));
    } catch (error) {
      console.error("Failed to create item:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create item");
    }
  };

  const handleDeleteItem = async (itemId: string) => {
    if (!currentOrganization) {
      toast.error("No organization selected");
      return;
    }

    try {
      await deleteKnowledgeBaseItem(currentOrganization._id, itemId);

      // Remove from local state
      const item = items.find(i => i._id === itemId);
      setItems(items.filter(i => i._id !== itemId));

      // Update folder item count
      if (item) {
        setFolders(folders.map(folder =>
          folder._id === item.folderId
            ? { ...folder, itemCount: Math.max(0, (folder.itemCount || 1) - 1) }
            : folder
        ));
      }

      toast.success("Item deleted successfully");
    } catch (error) {
      console.error("Failed to delete item:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete item");
    }
  };

  const handleDeleteFolder = async (folderId: string) => {
    if (!currentOrganization) {
      toast.error("No organization selected");
      return;
    }

    try {
      await deleteKnowledgeBaseFolder(currentOrganization._id, folderId);

      // Remove from local state
      setFolders(folders.filter(folder => folder._id !== folderId));
      setItems(items.filter(item => item.folderId !== folderId));

      // Reset selection if deleted folder was selected
      if (selectedFolderId === folderId) {
        const remainingFolders = folders.filter(folder => folder._id !== folderId);
        setSelectedFolderId(remainingFolders.length > 0 ? remainingFolders[0]._id : null);
      }

      toast.success("Folder deleted successfully");
    } catch (error) {
      console.error("Failed to delete folder:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete folder");
    }
  };

  // Get items for selected folder
  const selectedFolderItems = selectedFolderId
    ? items.filter(item => item.folderId === selectedFolderId)
    : [];

  const selectedFolder = selectedFolderId
    ? folders.find(f => f._id === selectedFolderId)
    : null;

  return (
    <div className="flex h-[calc(100vh-4rem)]">
      {/* Sidebar */}
      <div className="w-80 border-r bg-white dark:bg-gray-800 dark:border-gray-700">
        <KnowledgeBaseFolderSidebar
          folders={folders}
          selectedFolderId={selectedFolderId}
          onSelectFolder={handleSelectFolder}
          onCreateFolder={() => setIsCreateFolderModalOpen(true)}
          onDeleteFolder={handleDeleteFolder}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <KnowledgeBaseContent
          folder={selectedFolder}
          items={selectedFolderItems}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          onAddContent={() => setIsAddContentModalOpen(true)}
          onDeleteItem={handleDeleteItem}
        />
      </div>

      {/* Modals */}
      <CreateFolderModal
        isOpen={isCreateFolderModalOpen}
        onClose={() => setIsCreateFolderModalOpen(false)}
        onCreate={handleCreateFolder}
      />

      <AddContentModal
        isOpen={isAddContentModalOpen}
        onClose={() => setIsAddContentModalOpen(false)}
        onAdd={handleAddContent}
        organizationId={currentOrganization?._id}
        knowledgeBaseId={selectedFolderId || undefined}
      />
    </div>
  );
}