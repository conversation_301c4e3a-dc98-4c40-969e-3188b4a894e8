"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus } from "lucide-react";
import { KnowledgeBaseFolderSidebar } from "@/components/braincomponents/KnowledgeBaseFolderSidebar";
import { KnowledgeBaseContent } from "@/components/braincomponents/KnowledgeBaseContent";
import { CreateFolderModal } from "@/components/braincomponents/CreateFolderModal";
import { AddContentModal } from "@/components/braincomponents/AddContentModal";

// Types
type KnowledgeBaseFolder = {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  itemCount: number;
};

type KnowledgeBaseItem = {
  id: string;
  title: string;
  type: "document" | "url" | "text";
  content?: string; // For text items
  url?: string; // For URL items
  filename?: string; // For document items
  size?: number; // For document items
  pages?: number; // For document items
  status: "processing" | "ready" | "error";
  uploadedAt: Date;
  lastSynced?: Date;
  folderId: string;
};

export default function BrainContent() {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>("1");
  const [searchQuery, setSearchQuery] = useState("");

  // Modal states
  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);
  const [isAddContentModalOpen, setIsAddContentModalOpen] = useState(false);

  // Data states
  const [folders, setFolders] = useState<KnowledgeBaseFolder[]>([
    {
      id: "1",
      name: "TESET",
      description: "Test knowledge base",
      createdAt: new Date("2025-03-06T14:00:00"),
      itemCount: 3
    },
    {
      id: "2",
      name: "orova website",
      description: "Company website content",
      createdAt: new Date("2025-03-05T10:00:00"),
      itemCount: 1
    }
  ]);

  const [items, setItems] = useState<KnowledgeBaseItem[]>([
    {
      id: "1",
      title: "osweb.solutions",
      type: "url",
      url: "https://osweb.solutions",
      status: "ready",
      uploadedAt: new Date("2025-03-06T14:01:00"),
      lastSynced: new Date("2025-03-06T14:01:00"),
      pages: 31,
      folderId: "1"
    },
    {
      id: "2",
      title: "test text",
      type: "text",
      content: "This is a test text content...",
      status: "ready",
      uploadedAt: new Date("2025-03-06T14:02:00"),
      size: 1024,
      folderId: "1"
    },
    {
      id: "3",
      title: "_8_Ai_tools_to_finish_your_work_in_1_hoursâ_1689356736.pdf",
      type: "document",
      filename: "_8_Ai_tools_to_finish_your_work_in_1_hoursâ_1689356736.pdf",
      status: "ready",
      uploadedAt: new Date("2025-03-06T14:03:00"),
      size: 1155 * 1024,
      pages: 8,
      folderId: "1"
    }
  ]);

  // Handlers
  const handleCreateFolder = (name: string, description?: string) => {
    const newFolder: KnowledgeBaseFolder = {
      id: Math.random().toString(36).substr(2, 9),
      name,
      description,
      createdAt: new Date(),
      itemCount: 0
    };
    setFolders([...folders, newFolder]);
  };

  const handleSelectFolder = (folderId: string) => {
    setSelectedFolderId(folderId);
  };

  const handleAddContent = (type: "document" | "url" | "text", data: any) => {
    if (!selectedFolderId) return;

    const newItem: KnowledgeBaseItem = {
      id: Math.random().toString(36).substr(2, 9),
      title: data.title,
      type,
      status: "processing",
      uploadedAt: new Date(),
      folderId: selectedFolderId,
      ...data
    };

    setItems([...items, newItem]);

    // Update folder item count
    setFolders(folders.map(folder =>
      folder.id === selectedFolderId
        ? { ...folder, itemCount: folder.itemCount + 1 }
        : folder
    ));
  };

  const handleDeleteItem = (itemId: string) => {
    const item = items.find(i => i.id === itemId);
    if (!item) return;

    setItems(items.filter(i => i.id !== itemId));

    // Update folder item count
    setFolders(folders.map(folder =>
      folder.id === item.folderId
        ? { ...folder, itemCount: folder.itemCount - 1 }
        : folder
    ));
  };

  const handleDeleteFolder = (folderId: string) => {
    // Delete all items in the folder first
    setItems(items.filter(item => item.folderId !== folderId));
    // Delete the folder
    setFolders(folders.filter(folder => folder.id !== folderId));
    // Reset selection if deleted folder was selected
    if (selectedFolderId === folderId) {
      setSelectedFolderId(folders.length > 1 ? folders[0].id : null);
    }
  };

  // Get items for selected folder
  const selectedFolderItems = selectedFolderId
    ? items.filter(item => item.folderId === selectedFolderId)
    : [];

  const selectedFolder = selectedFolderId
    ? folders.find(f => f.id === selectedFolderId)
    : null;

  return (
    <div className="flex h-[calc(100vh-4rem)]">
      {/* Sidebar */}
      <div className="w-80 border-r bg-white dark:bg-gray-800 dark:border-gray-700">
        <KnowledgeBaseFolderSidebar
          folders={folders}
          selectedFolderId={selectedFolderId}
          onSelectFolder={handleSelectFolder}
          onCreateFolder={() => setIsCreateFolderModalOpen(true)}
          onDeleteFolder={handleDeleteFolder}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <KnowledgeBaseContent
          folder={selectedFolder}
          items={selectedFolderItems}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          onAddContent={() => setIsAddContentModalOpen(true)}
          onDeleteItem={handleDeleteItem}
        />
      </div>

      {/* Modals */}
      <CreateFolderModal
        isOpen={isCreateFolderModalOpen}
        onClose={() => setIsCreateFolderModalOpen(false)}
        onCreate={handleCreateFolder}
      />

      <AddContentModal
        isOpen={isAddContentModalOpen}
        onClose={() => setIsAddContentModalOpen(false)}
        onAdd={handleAddContent}
      />
    </div>
  );
}