"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeBaseController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const knowledge_base_service_1 = require("./knowledge-base.service");
const knowledge_base_dto_1 = require("./dto/knowledge-base.dto");
const organizations_service_1 = require("../organizations/organizations.service");
let KnowledgeBaseController = class KnowledgeBaseController {
    constructor(knowledgeBaseService, organizationsService) {
        this.knowledgeBaseService = knowledgeBaseService;
        this.organizationsService = organizationsService;
    }
    async createFolder(organizationId, createFolderDto, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const folder = await this.knowledgeBaseService.createFolder(organizationId, req.user.userId, createFolderDto);
        return {
            success: true,
            message: 'Folder created successfully',
            data: folder,
        };
    }
    async getFolders(organizationId, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const folders = await this.knowledgeBaseService.getFoldersByOrganization(organizationId);
        return {
            success: true,
            message: 'Folders retrieved successfully',
            data: folders,
        };
    }
    async getFolder(organizationId, folderId, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const folder = await this.knowledgeBaseService.getFolderById(folderId, organizationId);
        return {
            success: true,
            message: 'Folder retrieved successfully',
            data: folder,
        };
    }
    async updateFolder(organizationId, folderId, updateFolderDto, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const folder = await this.knowledgeBaseService.updateFolder(folderId, organizationId, updateFolderDto);
        return {
            success: true,
            message: 'Folder updated successfully',
            data: folder,
        };
    }
    async deleteFolder(organizationId, folderId, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        await this.knowledgeBaseService.deleteFolder(folderId, organizationId);
        return {
            success: true,
            message: 'Folder deleted successfully',
        };
    }
    async createItem(organizationId, createItemDto, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const item = await this.knowledgeBaseService.createItem(organizationId, req.user.userId, createItemDto);
        return {
            success: true,
            message: 'Item created successfully',
            data: item,
        };
    }
    async uploadDocument(organizationId, itemId, file, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        if (!file) {
            throw new common_1.BadRequestException('No file provided');
        }
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new common_1.BadRequestException('File size exceeds 50MB limit');
        }
        const item = await this.knowledgeBaseService.uploadDocumentToS3(itemId, organizationId, file.buffer, file.originalname, file.mimetype);
        return {
            success: true,
            message: 'File uploaded successfully',
            data: item,
        };
    }
    async getItemsByFolder(organizationId, folderId, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const items = await this.knowledgeBaseService.getItemsByFolder(folderId, organizationId);
        return {
            success: true,
            message: 'Items retrieved successfully',
            data: items,
        };
    }
    async getItem(organizationId, itemId, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const item = await this.knowledgeBaseService.getItemById(itemId, organizationId);
        return {
            success: true,
            message: 'Item retrieved successfully',
            data: item,
        };
    }
    async updateItem(organizationId, itemId, updateItemDto, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const item = await this.knowledgeBaseService.updateItem(itemId, organizationId, updateItemDto);
        return {
            success: true,
            message: 'Item updated successfully',
            data: item,
        };
    }
    async deleteItem(organizationId, itemId, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        await this.knowledgeBaseService.deleteItem(itemId, organizationId);
        return {
            success: true,
            message: 'Item deleted successfully',
        };
    }
    async getDownloadUrl(organizationId, itemId, req) {
        await this.checkOrganizationAccess(req.user, organizationId);
        const url = await this.knowledgeBaseService.getPresignedDownloadUrl(itemId, organizationId);
        return {
            success: true,
            message: 'Download URL generated successfully',
            data: { url },
        };
    }
    async checkOrganizationAccess(user, organizationId) {
        if (user.role !== 'superadmin') {
            const userAdminOrgs = await this.organizationsService.findByAdmin(user.userId);
            const hasAccess = userAdminOrgs.some(org => org._id.toString() === organizationId);
            if (!hasAccess) {
                throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
            }
        }
    }
};
exports.KnowledgeBaseController = KnowledgeBaseController;
__decorate([
    (0, common_1.Post)('organizations/:organizationId/folders'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new knowledge base folder' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Folder created successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, knowledge_base_dto_1.CreateKnowledgeBaseFolderDto, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "createFolder", null);
__decorate([
    (0, common_1.Get)('organizations/:organizationId/folders'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all folders for an organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Folders retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getFolders", null);
__decorate([
    (0, common_1.Get)('organizations/:organizationId/folders/:folderId'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific folder' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Folder retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'folderId', description: 'Folder ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('folderId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getFolder", null);
__decorate([
    (0, common_1.Put)('organizations/:organizationId/folders/:folderId'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a folder' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Folder updated successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'folderId', description: 'Folder ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('folderId')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, knowledge_base_dto_1.UpdateKnowledgeBaseFolderDto, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "updateFolder", null);
__decorate([
    (0, common_1.Delete)('organizations/:organizationId/folders/:folderId'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a folder and all its items' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Folder deleted successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'folderId', description: 'Folder ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('folderId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "deleteFolder", null);
__decorate([
    (0, common_1.Post)('organizations/:organizationId/items'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new knowledge base item' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Item created successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, knowledge_base_dto_1.CreateKnowledgeBaseItemDto, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "createItem", null);
__decorate([
    (0, common_1.Post)('organizations/:organizationId/items/:itemId/upload'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload document file for a knowledge base item' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'File uploaded successfully' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', description: 'Knowledge base item ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('itemId')),
    __param(2, (0, common_1.UploadedFile)()),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "uploadDocument", null);
__decorate([
    (0, common_1.Get)('organizations/:organizationId/folders/:folderId/items'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all items in a folder' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Items retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'folderId', description: 'Folder ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('folderId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getItemsByFolder", null);
__decorate([
    (0, common_1.Get)('organizations/:organizationId/items/:itemId'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific item' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Item retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', description: 'Item ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('itemId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getItem", null);
__decorate([
    (0, common_1.Put)('organizations/:organizationId/items/:itemId'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Update an item' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Item updated successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', description: 'Item ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('itemId')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, knowledge_base_dto_1.UpdateKnowledgeBaseItemDto, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "updateItem", null);
__decorate([
    (0, common_1.Delete)('organizations/:organizationId/items/:itemId'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an item' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Item deleted successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', description: 'Item ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('itemId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "deleteItem", null);
__decorate([
    (0, common_1.Get)('organizations/:organizationId/items/:itemId/download'),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get presigned download URL for a document' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Download URL generated successfully' }),
    (0, swagger_1.ApiParam)({ name: 'organizationId', description: 'Organization ID' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', description: 'Item ID' }),
    __param(0, (0, common_1.Param)('organizationId')),
    __param(1, (0, common_1.Param)('itemId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], KnowledgeBaseController.prototype, "getDownloadUrl", null);
exports.KnowledgeBaseController = KnowledgeBaseController = __decorate([
    (0, swagger_1.ApiTags)('Knowledge Base'),
    (0, common_1.Controller)('knowledge-base'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [knowledge_base_service_1.KnowledgeBaseService,
        organizations_service_1.OrganizationsService])
], KnowledgeBaseController);
//# sourceMappingURL=knowledge-base.controller.js.map