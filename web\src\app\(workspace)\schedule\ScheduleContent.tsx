/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client"

import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog } from "@radix-ui/react-dialog";
import { Label } from "@radix-ui/react-dropdown-menu";
import { Search, Filter, MoreVertical, Trash2, Edit, Loader2, Clock, CalendarDays, AlertTriangle } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import FadeIn from "@/animations/FadeIn";
import { TimezoneSelector } from "@/components/ui/timezone-selector";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { formatCustomDate } from "@/lib/dateUtils";
import { useCredits } from "@/contexts/CreditContext";
import { LowCreditAlert } from "@/components/LowCreditAlert";
import { CreditWarningAlert } from "@/components/CreditWarningAlert";
import { Agent } from "@/types/agent.types";

interface ScheduledCall {
  _id: string;
  agentId: string;
  contacts: { Name: string; MobileNumber: string }[];
  scheduledTime: Date;
  region: string;
  status: string;
  scheduledByName: string;
  scheduledByTimestamp: Date;
}

interface EditScheduleData {
  scheduledTime: Date;
  region: string;
  status: string;
  agentId?: string;
}

export default function ScheduleContent() {
  const { credits, minutes, organizationCreditThreshold } = useCredits();
  const [loading, setLoading] = useState(true);
  const [schedules, setSchedules] = useState<ScheduledCall[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [activeSchedule, setActiveSchedule] = useState<ScheduledCall | null>(null);
  const [updating, setUpdating] = useState(false);
  const [activeFilter, setActiveFilter] = useState<string>("all");
  const [isMobile, setIsMobile] = useState(false);
  const [editSchedule, setEditSchedule] = useState<EditScheduleData>({
    scheduledTime: new Date(),
    region: '',
    status: ''
  });

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const loadingRef = useRef(null);
  const ITEMS_PER_PAGE = 20;

  // Bulk delete states
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [bulkDeleteType, setBulkDeleteType] = useState<'cancelled' | 'failed' | null>(null);
  const [bulkDeleting, setBulkDeleting] = useState(false);


  // Fetch schedules
  const fetchSchedules = async (pageNum: number = 1, search?: string) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
      const filterParam = activeFilter !== 'all' ? `&filter=${activeFilter}` : '';

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/scheduled-call?page=${pageNum}&limit=${ITEMS_PER_PAGE}${searchParam}${filterParam}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        }
      );

      if (!response.ok) throw new Error('Failed to fetch schedules');
      const data = await response.json();

      if (pageNum === 1) {
        setSchedules(data);
      } else {
        setSchedules(prev => [...prev, ...data]);
      }

      setHasMore(data.length === ITEMS_PER_PAGE);
    } catch (error) {
      console.error('Error fetching schedules:', error);
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  };

  const fetchUserProfile = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error("No access token available");
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user profile: ${response.status}`);
      }

      const userData = await response.json();
      setUserRole(userData.role);
    } catch (err) {
      console.error("Failed to load user profile:", err);
    }
  }

  const fetchAgents = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error('No access token available');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch agents: ${response.status}`);
      }

      const fetchedAgents = await response.json();
      if (Array.isArray(fetchedAgents)) {
        if (userRole === 'superadmin') {
          setAgents(fetchedAgents);
        } else {
          const activeAgents = fetchedAgents.filter(agent => agent.status === 'active');
          setAgents(activeAgents);
        }
      } else {
        console.error('Unexpected response format from API:', fetchedAgents);
      }
    } catch (error) {
      console.error('Error loading agents:', error);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch user profile to get role
        await fetchUserProfile();
        // Fetch agents
        await fetchAgents();

        setPage(1);
        // Fetch schedules
        await fetchSchedules();

      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        const firstEntry = entries[0];
        if (firstEntry.isIntersecting && hasMore && !loading && !isLoadingMore) {
          setPage(prev => prev + 1);
        }
      },
      { threshold: 0.1 }
    );

    const currentRef = loadingRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasMore, loading, isLoadingMore]);

  // Add effect for page changes
  useEffect(() => {
    if (page > 1) {
      fetchSchedules(page);
    }
  }, [page]);


  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchSchedules(1, searchTerm);
  };

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    // Initial check
    checkScreenSize();
    // Add event listener
    window.addEventListener('resize', checkScreenSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  useEffect(() => {
    setPage(1);
    fetchSchedules(1, searchTerm);
  }, [activeFilter]);



  const handleUpdate = async (data: EditScheduleData) => {
    if (!activeSchedule) return;

    setUpdating(true);

    try {
      // Convert the local time back to the selected timezone
      const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const adjustedTime = convertDateBetweenTimezones(
        new Date(data.scheduledTime),
        localTimezone,
        data.region
      );

      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error('No access token available');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/scheduled-call/${activeSchedule._id}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scheduledTime: adjustedTime.toISOString(),
          region: data.region,
          status: data.status,
          agentId: data.agentId // Add this line
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update schedule: ${response.status}`);
      }

      // Refresh schedules after update
      fetchSchedules();
      setEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating schedule:', error);
    } finally {
      setUpdating(false);
    }
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'scheduledTime') {
      try {
        // Check if the value is a valid date string
        if (!value || isNaN(new Date(value).getTime())) {
          return; // Invalid date, keep previous value
        }

        const localDate = new Date(value);

        // Prevent dates in the past by silently adjusting to current time
        const now = new Date();
        if (localDate < now) {
          // Instead of showing an error, just use the current time
          setEditSchedule(prev => ({ ...prev, [name]: now }));
          return;
        }

        setEditSchedule(prev => ({ ...prev, [name]: localDate }));
      } catch (error) {
        console.warn('Invalid date input:', error);
      }
    } else {
      setEditSchedule(prev => ({ ...prev, [name]: value }));
    }
  };

  const confirmDelete = async () => {
    if (!activeSchedule) return;

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/scheduled-call/${activeSchedule._id}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        }
      );

      if (!response.ok) throw new Error('Failed to delete schedule');

      // Refresh schedules after deletion
      await fetchSchedules();
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting schedule:', error);
    }
  };

  const handleEdit = (schedule: ScheduledCall) => {
    setActiveSchedule(schedule);

    // Convert the scheduled time from the schedule's region to local timezone for editing
    let adjustedTime = new Date(schedule.scheduledTime);

    if (schedule.region) {
      const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      adjustedTime = convertDateBetweenTimezones(
        new Date(schedule.scheduledTime),
        schedule.region,
        localTimezone
      );
    }

    setEditSchedule({
      scheduledTime: adjustedTime,
      region: schedule.region,
      status: schedule.status,
      agentId: schedule.agentId // Add this line
    });

    setEditDialogOpen(true);
  };

  const handleDelete = async (schedule: ScheduledCall) => {
    setActiveSchedule(schedule);
    setDeleteDialogOpen(true);
  };

  const handleBulkDelete = (type: 'cancelled' | 'failed') => {
    setBulkDeleteType(type);
    setBulkDeleteDialogOpen(true);
  };

  const confirmBulkDelete = async () => {
    if (!bulkDeleteType) return;

    setBulkDeleting(true);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error('No access token available');
        return;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/scheduled-call/bulk/${bulkDeleteType}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) throw new Error(`Failed to delete ${bulkDeleteType} calls`);

      const result = await response.json();
      console.log(result.message);

      // Refresh schedules after deletion
      await fetchSchedules();
      setBulkDeleteDialogOpen(false);
      setBulkDeleteType(null);
    } catch (error) {
      console.error(`Error deleting ${bulkDeleteType} calls:`, error);
    } finally {
      setBulkDeleting(false);
    }
  };


  const convertDateBetweenTimezones = (date: Date, fromTimezone: string, toTimezone: string): Date => {
    // Get the date string in the source timezone
    const dateInFromTz = new Date(date.toLocaleString('en-US', { timeZone: fromTimezone }));

    // Get the date string in the target timezone
    const dateInToTz = new Date(date.toLocaleString('en-US', { timeZone: toTimezone }));

    // Calculate the offset between the two timezones
    const offsetDiff = (dateInToTz.getTime() - dateInFromTz.getTime());

    // Apply the offset to the original date
    return new Date(date.getTime() - offsetDiff);
  };

  const getCurrentDateTimeLocal = () => {
    const now = new Date();
    // Format directly to YYYY-MM-DDTHH:MM without timezone adjustments
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  const formatDateForInput = (date: Date | null | undefined): string => {
    try {
      // Check if we have a valid date
      if (date && !isNaN(new Date(date).getTime())) {
        const dateToUse = new Date(date) < new Date()
          ? new Date()
          : new Date(date);

        // Format directly to YYYY-MM-DDTHH:MM without timezone adjustments
        const year = dateToUse.getFullYear();
        const month = String(dateToUse.getMonth() + 1).padStart(2, '0');
        const day = String(dateToUse.getDate()).padStart(2, '0');
        const hours = String(dateToUse.getHours()).padStart(2, '0');
        const minutes = String(dateToUse.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day}T${hours}:${minutes}`;
      } else {
        return getCurrentDateTimeLocal();
      }
    } catch (error) {
      return getCurrentDateTimeLocal();
    }
  };

  const formatDateInTimezone = (date: Date, timezone: string): string => {
    try {
      return formatCustomDate(date, timezone);
    } catch (error) {
      return formatCustomDate(date);
    }
  };

  const formatLocalDate = (date: Date): string => {
    return formatCustomDate(date);
  };



  return (
    <>
      {/* Header */}
      <FadeIn>
      {/* Credit Alerts */}
      <CreditWarningAlert credits={credits} threshold={organizationCreditThreshold} />
      <LowCreditAlert credits={credits} minutes={minutes} threshold={organizationCreditThreshold} />

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Scheduled Calls</h1>
        {userRole === 'superadmin' && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkDelete('cancelled')}
              className="text-orange-600 border-orange-200 hover:bg-orange-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Cancelled
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkDelete('failed')}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Failed
            </Button>
          </div>
        )}
      </div>

      {/* Search and Filters */}
      <div className="space-y-4 mb-6">
    <div className="flex flex-col sm:flex-row gap-2">
      <div className="relative flex-1">
      <form onSubmit={handleSearch} className="relative flex-1">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
      <Input
        placeholder="Search schedules..."
        className="pl-10 pr-20"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />
    </form>
      </div>


    </div>
    {isMobile ? (
  <div className="flex items-center gap-2">
    <Button variant="outline" size="sm" onClick={() => setActiveFilter("all")}>
      {activeFilter === "all" ? "All" : activeFilter}
    </Button>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        <DropdownMenuLabel>Filter by Time</DropdownMenuLabel>
        <DropdownMenuItem
          className={activeFilter === "all" ? "bg-muted" : ""}
          onClick={() => setActiveFilter("all")}
        >
          All
        </DropdownMenuItem>
        <DropdownMenuItem
          className={activeFilter === "today" ? "bg-muted" : ""}
          onClick={() => setActiveFilter("today")}
        >
          <Clock className="h-3 w-3 mr-2" /> Today
        </DropdownMenuItem>
        <DropdownMenuItem
          className={activeFilter === "thisWeek" ? "bg-muted" : ""}
          onClick={() => setActiveFilter("thisWeek")}
        >
          This Week
        </DropdownMenuItem>
        <DropdownMenuItem
          className={activeFilter === "weekend" ? "bg-muted" : ""}
          onClick={() => setActiveFilter("weekend")}
        >
          Weekend
        </DropdownMenuItem>
        <DropdownMenuItem
          className={activeFilter === "nextWeek" ? "bg-muted" : ""}
          onClick={() => setActiveFilter("nextWeek")}
        >
          Next Week
        </DropdownMenuItem>
        <DropdownMenuItem
          className={activeFilter === "upcoming" ? "bg-muted" : ""}
          onClick={() => setActiveFilter("upcoming")}
        >
          Upcoming
        </DropdownMenuItem>
        <DropdownMenuItem
          className={activeFilter === "past" ? "bg-muted" : ""}
          onClick={() => setActiveFilter("past")}
        >
          Past
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
) : (
  <Tabs defaultValue="all" value={activeFilter}
  onValueChange={(value) => {
    setActiveFilter(value);
    setPage(1);
    fetchSchedules(1, searchTerm);
  }}
  className="w-full">
    <TabsList className="grid md:grid-cols-7 w-full">
      <TabsTrigger value="all">All</TabsTrigger>
      <TabsTrigger value="today" className="flex items-center gap-1">
        <Clock className="h-3 w-3" /> Today
      </TabsTrigger>
      <TabsTrigger value="thisWeek">This Week</TabsTrigger>
      <TabsTrigger value="weekend">Weekend</TabsTrigger>
      <TabsTrigger value="nextWeek">Next Week</TabsTrigger>
      <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
      <TabsTrigger value="past">Past</TabsTrigger>
    </TabsList>
  </Tabs>
)}
  </div>

      {/* Schedules Table */}
      <div className="bg-card rounded-lg border shadow-sm overflow-x-auto">
        <Table className="min-w-[1200px]">
          <TableHeader className="border-b-3 border-gray-300 dark:border-gray-700 ">
            <TableRow className="">

              <TableHead className="font-bold text-gray-700 dark:text-gray-200 "><span className="ml-2"></span> Name</TableHead>
              <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center ">Agent</TableHead>
              <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center ">Phone Number</TableHead>
              <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center ">Local Date</TableHead>
              <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center ">Scheduled Date</TableHead>
              <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center ">Status</TableHead>
              <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center ">Scheduled By</TableHead>
              <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center ">Scheduled At</TableHead>
              <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center "></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading && page === 1 ? (
              <TableRow>
                <TableCell colSpan={9} className="h-80 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-12 w-12 animate-spin text-primary" />
                    <p className="text-lg font-medium">Loading...</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : schedules.length > 0 ? (
              schedules.map((schedule, index) =>
                schedule.contacts.map((contact) => (
                  <TableRow
                    key={`${index}-${contact.Name}`}
                    className="hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-default animate-in fade-in slide-in-from-bottom-10 duration-500"
                  >
                    <TableCell className="font-medium py-4">
                      <span className="ml-2"></span>
                      {contact.Name || 'N/A'}
                    </TableCell>
                    <TableCell className="py-4 text-center">
                      {(() => {
                        const agent = agents.find(agent => agent.id === schedule.agentId);
                        return (
                          <div className="flex flex-col items-center justify-center">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={agent?.avatar}
                                alt={agent?.name || ''}
                              />
                              <AvatarFallback className="bg-indigo-50 text-indigo-700 text-xs">
                                {agent?.name?.charAt(0) || 'A'}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-xs font-medium text-gray-600">
                              {agent?.name || ''}
                            </span>
                          </div>
                        );
                      })()}
                    </TableCell>
                    <TableCell className="py-4 text-center">
                      {contact.MobileNumber || ''}
                    </TableCell>
                    <TableCell className="py-4 text-center">
                      {formatLocalDate(new Date(schedule.scheduledTime))}
                      <div className="text-xs text-gray-500">
                        ({Intl.DateTimeFormat().resolvedOptions().timeZone})
                      </div>
                    </TableCell>
                    <TableCell className="py-4 text-center">
                    {schedule.region
                      ? formatDateInTimezone(new Date(schedule.scheduledTime), schedule.region)
                      : formatLocalDate(new Date(schedule.scheduledTime))}
                    <div className="text-xs text-gray-500">
                      {schedule.region ? `(${schedule.region})` : '(Local time)'}
                    </div>
                  </TableCell>
                    <TableCell className="py-4 text-center">
                      <span className={`px-2.5 py-1.5 rounded-full text-xs font-medium
                        ${schedule.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          schedule.status === 'executed' ? 'bg-green-100 text-green-800' :
                          'bg-red-100 text-red-800'}`}>
                        {schedule.status}
                      </span>
                    </TableCell>
                    <TableCell className="py-4 text-center">
                      {(() => {
                        // If scheduledByName is "agent", show the agent name from the agent column
                        if (schedule.scheduledByName === "agent") {
                          const agent = agents.find(agent => agent.id === schedule.agentId);
                          return agent?.name || "Agent";
                        }
                        // Otherwise show the scheduledByName as is
                        return schedule.scheduledByName || 'N/A';
                      })()}
                    </TableCell>
                    <TableCell className="py-4 text-center">
                      {schedule.scheduledByTimestamp ? new Date(schedule.scheduledByTimestamp).toLocaleString(): ''}
                    </TableCell>
                    <TableCell className="text-center py-4">
                      <div className="flex justify-center">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="hover:bg-gray-100 h-8 w-8">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-32">
                            <DropdownMenuItem
                              className="flex items-center cursor-pointer"
                              onClick={() => handleEdit(schedule)}
                            >
                              <Edit className="h-4 w-4 mr-2 text-blue-600" />
                              <span className="text-blue-600">Edit</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="flex items-center cursor-pointer"
                              onClick={() => handleDelete(schedule)}
                            >
                              <Trash2 className="h-4 w-4 mr-2 text-red-600" />
                              <span className="text-red-600">Delete</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )
            ) : (
              <TableRow>
                 <TableCell colSpan={9} className="h-60 text-center">
                <div className="flex flex-col items-center justify-center">
                  <CalendarDays className="h-12 w-12 text-muted-foreground mb-2" />
                  <p className="text-lg font-medium">No schedules found</p>
                  <p className="text-muted-foreground">
                    {searchTerm ?
                      "Try adjusting your search term" :
                      `No scheduled calls ${activeFilter === "past" ? "in the past" :
                        activeFilter === "today" ? "for today" :
                        activeFilter === "thisWeek" ? "this week" :
                        activeFilter === "weekend" ? "for the weekend" :
                        activeFilter === "nextWeek" ? "for next week" :
                        activeFilter === "upcoming" ? "upcoming" : ""}`
                    }
                  </p>
                </div>
              </TableCell>
              </TableRow>
            )}

          {hasMore && (
            <TableRow ref={loadingRef} className="h-20">
              <TableCell colSpan={9}>
                <div className="flex items-center justify-center py-4">
                  {isLoadingMore ? (
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  ) : (
                    <div className="h-8" />
                  )}
                </div>
              </TableCell>
            </TableRow>
          )}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this Schedule.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={confirmDelete}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Delete All {bulkDeleteType === 'cancelled' ? 'Cancelled' : 'Failed'} Calls
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete all {bulkDeleteType} scheduled calls from the database.
              <br />
              <br />
              <strong>Are you sure you want to proceed?</strong>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={bulkDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={confirmBulkDelete}
              disabled={bulkDeleting}
            >
              {bulkDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete All {bulkDeleteType === 'cancelled' ? 'Cancelled' : 'Failed'}
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Scheduled Call</DialogTitle>
            <DialogDescription>Update schedule information.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label id="contactName">Contact Name</Label>
              <Input
                id="name"
                value={activeSchedule?.contacts[0]?.Name || ''}
                disabled
                className="bg-gray-50"
              />
            </div>
            <div className="grid gap-2">
              <Label id="phone">Phone Number</Label>
              <Input
                id="phone"
                value={activeSchedule?.contacts[0]?.MobileNumber || ''}
                disabled
                className="bg-gray-50"
              />
            </div>

            <div className="grid gap-2">
            <Label id="agent">Agent</Label>
            <Select
              value={editSchedule.agentId}
              onValueChange={(value) => setEditSchedule(prev => ({ ...prev, agentId: value }))}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                {agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id}>
                    <div className="flex items-center">
                      <Avatar className="h-6 w-6 mr-2">
                        <AvatarImage
                          src={agent.avatar}
                          alt={agent.name}
                        />
                        <AvatarFallback className="bg-indigo-50 text-indigo-700 text-xs">
                          {agent.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      {agent.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

            <div className="grid gap-2">
            <Label id="scheduledTime">
              Schedule Date & Time
              <span className="text-xs text-gray-500 ml-1">
                (in {editSchedule.region || 'local timezone'})
              </span>
            </Label>
            <Input
              id="scheduledTime"
              name="scheduledTime"
              type="datetime-local"
              min={getCurrentDateTimeLocal()}
              value={formatDateForInput(editSchedule.scheduledTime)}
              onChange={handleEditInputChange}
            />
            <p className="text-xs text-gray-500">
              The call will be scheduled at this exact time in the selected timezone.
            </p>
          </div>
            <div className="grid gap-2">
              <Label id="region">Time Zone</Label>
              <TimezoneSelector
                value={editSchedule.region}
                onChange={(timezone) => {
                  setEditSchedule(prev => ({
                    ...prev,
                    region: timezone
                  }));
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => handleUpdate(editSchedule)} disabled={updating}>
              {updating ? "Saving..." : "Save changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </FadeIn>
    </>
  );
}