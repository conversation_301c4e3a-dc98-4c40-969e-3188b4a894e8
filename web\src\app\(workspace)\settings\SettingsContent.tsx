
"use client";

import { APISettings } from "@/components/settingscomponents/APISettings";
import { GeneralSettings } from "@/components/settingscomponents/GeneralSettings";
import LogsSettings from "@/components/settingscomponents/LogsSettings";
import { VoiceSettings } from "@/components/settingscomponents/VoiceSettings";
import { S3Settings } from "@/components/settingscomponents/S3Settings";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { authFetch } from "@/lib/authFetch";
import { getMyOrganizations, Organization } from "@/app/api/organizations";
import { useState, useEffect } from "react";

const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

interface UserInfo {
  userId: string;
  email: string;
  role: string;
}

export default function SettingsContent() {
  const [activeTab, setActiveTab] = useState("general");
  const [user, setUser] = useState<UserInfo | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);

  useEffect(() => {
    async function fetchUserData() {
      // Use authFetch instead of regular fetch
      const response = await authFetch(`${API_BASE_URL}/api/auth/me`);
      const userData = await response.json();
      setUser(userData);
    }

    async function fetchOrganizations() {
      try {
        const orgs = await getMyOrganizations();
        setOrganizations(orgs);
        if (orgs.length > 0) {
          setSelectedOrganization(orgs[0]); // Select first organization by default
        }
      } catch (error) {
        console.error("Failed to fetch organizations:", error);
      }
    }

    fetchUserData();
    fetchOrganizations();
  }, []);

  const handleOrganizationUpdate = (updatedOrg: Organization) => {
    setSelectedOrganization(updatedOrg);
    setOrganizations(orgs =>
      orgs.map(org => org._id === updatedOrg._id ? updatedOrg : org)
    );
  };

  const showLogs = user?.role === "superadmin";

  // Determine grid columns based on visible tabs
  let tabsGridCols = "grid-cols-4"; // Default: general, api, voice, s3
  if (showLogs) {
    tabsGridCols = "grid-cols-5"; // Add logs tab
  }

  return (
    <div className="container py-6 px-4 md:px-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className={`grid w-full ${tabsGridCols} mb-8`}>
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="api">API Keys</TabsTrigger>
              <TabsTrigger value="voice">Voice Settings</TabsTrigger>
              <TabsTrigger value="s3">S3 Storage</TabsTrigger>
              {showLogs && <TabsTrigger value="logs">Logs</TabsTrigger>}
            </TabsList>

            <TabsContent value="general">
              <GeneralSettings />
            </TabsContent>

            <TabsContent value="api">
              <APISettings />
            </TabsContent>

            <TabsContent value="voice">
              <VoiceSettings />
            </TabsContent>

            <TabsContent value="s3">
              <S3Settings
                organization={selectedOrganization}
                organizations={organizations}
                onUpdate={handleOrganizationUpdate}
                onOrganizationChange={setSelectedOrganization}
              />
            </TabsContent>

            {showLogs && (
              <TabsContent value="logs">
                <LogsSettings />
              </TabsContent>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
