{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport type { NextRequest } from 'next/server';\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  // Get the access token from cookies\r\n  const accessToken = request.cookies.get('access_token')?.value;\r\n\r\n  // Define the paths that require authentication\r\n  const isProtectedPath = request.nextUrl.pathname.startsWith('/dashboard') || \r\n                          request.nextUrl.pathname.startsWith('/agents') ||\r\n                          request.nextUrl.pathname.startsWith('/settings');\r\n  \r\n  // Define authentication paths (login/register)\r\n  const isAuthPath = request.nextUrl.pathname === '/login' || \r\n                     request.nextUrl.pathname === '/register';\r\n\r\n  // If accessing a protected route without a token\r\n  if (isProtectedPath && !accessToken) {\r\n    const loginUrl = new URL('/login', request.url);\r\n    return NextResponse.redirect(loginUrl);\r\n  }\r\n\r\n  // If accessing login/register with a token\r\n  if (isAuthPath && accessToken) {\r\n    const dashboardUrl = new URL('/dashboard', request.url);\r\n    return NextResponse.redirect(dashboardUrl);\r\n  }\r\n\r\n  return NextResponse.next();\r\n}\r\n\r\n// Configure the middleware to run on specific paths\r\nexport const config = {\r\n  matcher: [\r\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\r\n  ],\r\n};"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,eAAe,WAAW,OAAoB;IACnD,oCAAoC;IACpC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IAEzD,+CAA+C;IAC/C,MAAM,kBAAkB,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAE5D,+CAA+C;IAC/C,MAAM,aAAa,QAAQ,OAAO,CAAC,QAAQ,KAAK,YAC7B,QAAQ,OAAO,CAAC,QAAQ,KAAK;IAEhD,iDAAiD;IACjD,IAAI,mBAAmB,CAAC,aAAa;QACnC,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC9C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,2CAA2C;IAC3C,IAAI,cAAc,aAAa;QAC7B,MAAM,eAAe,IAAI,IAAI,cAAc,QAAQ,GAAG;QACtD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}