"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrganizationSettingsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class S3ConfigDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'AWS S3 Access Key ID', example: 'AKIAIOSFODNN7EXAMPLE' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], S3ConfigDto.prototype, "accessKeyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'AWS S3 Secret Access Key', example: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], S3ConfigDto.prototype, "secretAccessKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'AWS S3 Region', example: 'us-east-1' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], S3ConfigDto.prototype, "region", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'S3 Bucket Name', example: 'my-organization-knowledge-base' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], S3ConfigDto.prototype, "bucketName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Enable S3 storage for knowledge base', example: true }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], S3ConfigDto.prototype, "enabled", void 0);
class UpdateOrganizationSettingsDto {
}
exports.UpdateOrganizationSettingsDto = UpdateOrganizationSettingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Day of month when credits reset (1-28)', example: 1 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(28),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateOrganizationSettingsDto.prototype, "monthlyResetDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Client full name for email personalization', example: 'John Doe' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateOrganizationSettingsDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email address to send credit notifications to', example: '<EMAIL>' }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateOrganizationSettingsDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'S3 configuration for knowledge base file storage', type: S3ConfigDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => S3ConfigDto),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", S3ConfigDto)
], UpdateOrganizationSettingsDto.prototype, "s3Config", void 0);
//# sourceMappingURL=update-organization-settings.dto.js.map