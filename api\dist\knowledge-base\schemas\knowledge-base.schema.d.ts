import { Schema, Types } from 'mongoose';
export declare const KnowledgeBaseFolderSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
    organizationId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    createdBy: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    description?: string;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    organizationId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    createdBy: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    description?: string;
}>> & import("mongoose").FlatRecord<{
    organizationId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    createdBy: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    description?: string;
}> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const KnowledgeBaseItemSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
    type: "text" | "url" | "document";
    title: string;
    organizationId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    createdAt: NativeDate;
    updatedAt: NativeDate;
    status: "error" | "processing" | "uploading" | "ready";
    folderId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    uploadedBy: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    trieveChunkIds: string[];
    content?: string;
    url?: string;
    size?: number;
    filename?: string;
    originalName?: string;
    pages?: number;
    errorMessage?: string;
    mimeType?: string;
    s3Key?: string;
    s3Url?: string;
    s3Bucket?: string;
    lastSynced?: NativeDate;
    processedAt?: NativeDate;
    trieveDatasetId?: string;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    type: "text" | "url" | "document";
    title: string;
    organizationId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    createdAt: NativeDate;
    updatedAt: NativeDate;
    status: "error" | "processing" | "uploading" | "ready";
    folderId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    uploadedBy: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    trieveChunkIds: string[];
    content?: string;
    url?: string;
    size?: number;
    filename?: string;
    originalName?: string;
    pages?: number;
    errorMessage?: string;
    mimeType?: string;
    s3Key?: string;
    s3Url?: string;
    s3Bucket?: string;
    lastSynced?: NativeDate;
    processedAt?: NativeDate;
    trieveDatasetId?: string;
}>> & import("mongoose").FlatRecord<{
    type: "text" | "url" | "document";
    title: string;
    organizationId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    createdAt: NativeDate;
    updatedAt: NativeDate;
    status: "error" | "processing" | "uploading" | "ready";
    folderId: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    uploadedBy: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
    trieveChunkIds: string[];
    content?: string;
    url?: string;
    size?: number;
    filename?: string;
    originalName?: string;
    pages?: number;
    errorMessage?: string;
    mimeType?: string;
    s3Key?: string;
    s3Url?: string;
    s3Bucket?: string;
    lastSynced?: NativeDate;
    processedAt?: NativeDate;
    trieveDatasetId?: string;
}> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
