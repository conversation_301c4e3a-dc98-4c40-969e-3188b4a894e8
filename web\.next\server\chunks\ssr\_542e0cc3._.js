module.exports = {

"[project]/src/lib/validations/authSchema.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "loginSchema": (()=>loginSchema),
    "registerSchema": (()=>registerSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
const registerSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    fullName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(2, {
        message: "Full name must be at least 2 characters"
    }),
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().email({
        message: "Please enter a valid email address"
    }),
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(3, {
        message: "Password must be at least 6 characters"
    })
});
const loginSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().email({
        message: "Please enter a valid email address"
    }),
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(1, {
        message: "Password is required"
    })
});
}}),
"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-unused-vars */ /* __next_internal_action_entry_do_not_use__ {"006e6a7598517a64a0222bb89b73ddd257764d50ac":"getCurrentUser","006f4095f78e30eaa701b85c396d30de187fe7f260":"logoutUser","00c349c6c9b93ac440dea00915d65f1b054d5a71a5":"refreshAccessToken","408d4ffcda2adfadc68aafde596a5102a7ebb51409":"registerUser","40ae9d6f5ad7238997dd0ef7497e335399ead00b2f":"loginUser"} */ __turbopack_context__.s({
    "getCurrentUser": (()=>getCurrentUser),
    "loginUser": (()=>loginUser),
    "logoutUser": (()=>logoutUser),
    "refreshAccessToken": (()=>refreshAccessToken),
    "registerUser": (()=>registerUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2f$authSchema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validations/authSchema.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:4000") || "";
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ registerUser(formData) {
    // Extract data from form
    const rawData = {
        fullName: formData.get("fullName"),
        email: formData.get("email"),
        password: formData.get("password")
    };
    // Validate with Zod
    const validationResult = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2f$authSchema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerSchema"].safeParse(rawData);
    if (!validationResult.success) {
        // Return validation errors
        return {
            success: false,
            message: "Validation failed",
            fieldErrors: validationResult.error.flatten().fieldErrors
        };
    }
    // Validation passed, destructure the validated data
    const { fullName, email, password } = validationResult.data;
    try {
        // Make API request to registration endpoint
        const response = await fetch(`${API_BASE_URL}/api/users/register`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                fullName,
                email,
                password
            }),
            cache: "no-store"
        });
        // First check if response is ok
        if (!response.ok) {
            try {
                // Try to parse as JSON for error details
                const errorData = await response.json();
                return {
                    success: false,
                    message: errorData.message || "Registration failed. Please try again."
                };
            } catch  {
                // If not JSON, get text or use statusText
                const errorText = await response.text().catch(()=>response.statusText);
                return {
                    success: false,
                    message: errorText || "Registration failed. Please try again."
                };
            }
        }
        // For successful responses, get the text content
        const successText = await response.text();
        return {
            success: true,
            message: successText || "User registered successfully! Awaiting admin approval."
        };
    } catch (error) {
        console.error("Registration error:", error);
        return {
            success: false,
            message: "An unexpected error occurred. Please try again later."
        };
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ loginUser(formData) {
    const rawData = {
        email: formData.get("email"),
        password: formData.get("password")
    };
    // Validate with Zod
    const validationResult = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2f$authSchema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["loginSchema"].safeParse(rawData);
    if (!validationResult.success) {
        // Return validation errors
        return {
            success: false,
            message: "Validation failed",
            fieldErrors: validationResult.error.flatten().fieldErrors
        };
    }
    // Validation passed, destructure the validated data
    const { email, password } = validationResult.data;
    try {
        // Make API request to login endpoint
        const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                email,
                password
            }),
            cache: "no-store"
        });
        // Check if the response is OK
        if (!response.ok) {
            try {
                const errorData = await response.json();
                return {
                    success: false,
                    message: errorData.message || "Login failed. Please check your credentials."
                };
            } catch  {
                return {
                    success: false,
                    message: response.statusText || "Login failed. Please try again."
                };
            }
        }
        // Parse the successful response to get tokens
        try {
            const data = await response.json();
            if (data.access_token && data.refresh_token) {
                // Return tokens in the response rather than setting cookies
                return {
                    success: true,
                    message: "Login successful!",
                    redirect: "/dashboard",
                    tokens: {
                        access_token: data.access_token,
                        refresh_token: data.refresh_token
                    }
                };
            } else {
                return {
                    success: false,
                    message: "Invalid response from server. Missing authentication tokens."
                };
            }
        } catch (jsonError) {
            return {
                success: false,
                message: "Failed to process login response."
            };
        }
    } catch (error) {
        console.error("Login error:", error);
        return {
            success: false,
            message: "An unexpected error occurred. Please try again later."
        };
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ refreshAccessToken() {
    // Get the refresh token from cookies
    const refreshToken = (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])()).get("refresh_token")?.value;
    if (!refreshToken) {
        // Without a refresh token, we can't refresh the session
        return {
            success: false
        };
    }
    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${refreshToken}`
            },
            body: JSON.stringify({
                refreshToken
            }),
            cache: "no-store"
        });
        if (!response.ok) {
            // If the refresh token is invalid or expired, user needs to log in again
            return {
                success: false
            };
        }
        const data = await response.json();
        if (data.access_token) {
            // Update the access token in cookies
            (await // Update the access token in cookies
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])()).set("access_token", data.access_token, {
                httpOnly: true,
                secure: ("TURBOPACK compile-time value", "development") === "production",
                path: "/",
                sameSite: "lax",
                maxAge: 60 * 60
            });
            return {
                success: true,
                newAccessToken: data.access_token
            };
        }
        return {
            success: false
        };
    } catch (error) {
        console.error("Token refresh error:", error);
        return {
            success: false
        };
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ getCurrentUser() {
    let accessToken;
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // We're on the server
        accessToken = (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])()).get("access_token")?.value;
    }
    if (!accessToken) {
        console.log("No access token found");
        return {
            success: false,
            error: "Not authenticated"
        };
    }
    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/me`, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${accessToken}`
            },
            cache: "no-store"
        });
        if (!response.ok) {
            // If response is 401 or 403, try to refresh the token
            if (response.status === 401 || response.status === 403) {
                // Attempt to refresh the token
                const refreshResult = await refreshAccessToken();
                if (refreshResult.success) {
                    // Retry with new token
                    const retryResponse = await fetch(`${API_BASE_URL}/api/auth/me`, {
                        method: "GET",
                        headers: {
                            "Authorization": `Bearer ${refreshResult.newAccessToken}`
                        },
                        cache: "no-store"
                    });
                    if (retryResponse.ok) {
                        const userData = await retryResponse.json();
                        // Check if we got actual user data back (not empty object)
                        if (userData && userData.userId && userData.email) {
                            return {
                                success: true,
                                user: userData
                            };
                        } else {
                            return {
                                success: false,
                                error: "Account not authorized or pending approval"
                            };
                        }
                    }
                }
                // If refresh failed or retry failed
                return {
                    success: false,
                    error: "Not authorized"
                };
            }
            // For other errors
            return {
                success: false,
                error: `Error: ${response.status}`
            };
        }
        // If first attempt was successful
        const userData = await response.json();
        // This confirms the user has proper approval, not just valid tokens
        if (userData && userData.userId && userData.email) {
            return {
                success: true,
                user: userData
            };
        } else {
            // User has valid tokens but no data - likely pending approval
            return {
                success: false,
                error: "Account not authorized or pending approval"
            };
        }
    } catch (error) {
        console.error("Error fetching user data:", error);
        return {
            success: false,
            error: "An error occurred while fetching user data"
        };
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ logoutUser() {
    try {
        // Clear the cookies
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        cookieStore.delete("access_token");
        cookieStore.delete("refresh_token");
        return {
            success: true,
            message: "Logged out successfully",
            redirect: "/"
        };
    } catch (error) {
        console.error("Logout error:", error);
        return {
            success: false,
            message: "An error occurred during logout"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    registerUser,
    loginUser,
    refreshAccessToken,
    getCurrentUser,
    logoutUser
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(registerUser, "408d4ffcda2adfadc68aafde596a5102a7ebb51409", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(loginUser, "40ae9d6f5ad7238997dd0ef7497e335399ead00b2f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(refreshAccessToken, "00c349c6c9b93ac440dea00915d65f1b054d5a71a5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCurrentUser, "006e6a7598517a64a0222bb89b73ddd257764d50ac", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(logoutUser, "006f4095f78e30eaa701b85c396d30de187fe7f260", null);
}}),
"[project]/.next-internal/server/app/(workspace)/workspaces/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/(workspace)/workspaces/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(workspace)/workspaces/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(workspace)/workspaces/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "006e6a7598517a64a0222bb89b73ddd257764d50ac": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCurrentUser"]),
    "006f4095f78e30eaa701b85c396d30de187fe7f260": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logoutUser"]),
    "00c349c6c9b93ac440dea00915d65f1b054d5a71a5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["refreshAccessToken"]),
    "408d4ffcda2adfadc68aafde596a5102a7ebb51409": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerUser"]),
    "40ae9d6f5ad7238997dd0ef7497e335399ead00b2f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["loginUser"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(workspace)/workspaces/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(workspace)/workspaces/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "006e6a7598517a64a0222bb89b73ddd257764d50ac": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["006e6a7598517a64a0222bb89b73ddd257764d50ac"]),
    "006f4095f78e30eaa701b85c396d30de187fe7f260": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["006f4095f78e30eaa701b85c396d30de187fe7f260"]),
    "00c349c6c9b93ac440dea00915d65f1b054d5a71a5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00c349c6c9b93ac440dea00915d65f1b054d5a71a5"]),
    "408d4ffcda2adfadc68aafde596a5102a7ebb51409": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["408d4ffcda2adfadc68aafde596a5102a7ebb51409"]),
    "40ae9d6f5ad7238997dd0ef7497e335399ead00b2f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40ae9d6f5ad7238997dd0ef7497e335399ead00b2f"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(workspace)/workspaces/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$workspace$292f$workspaces$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f28$auth$292f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(workspace)/workspaces/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/(workspace)/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/(workspace)/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx <module evaluation>", "default");
}}),
"[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx", "default");
}}),
"[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$workspace$292f$workspaces$2f$OrganizationsContent$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$workspace$292f$workspaces$2f$OrganizationsContent$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$workspace$292f$workspaces$2f$OrganizationsContent$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/(workspace)/workspaces/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>OrganizationsPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$workspace$292f$workspaces$2f$OrganizationsContent$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/(workspace)/workspaces/OrganizationsContent.tsx [app-rsc] (ecmascript)");
;
;
function OrganizationsPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$workspace$292f$workspaces$2f$OrganizationsContent$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/app/(workspace)/workspaces/page.tsx",
        lineNumber: 5,
        columnNumber: 10
    }, this);
}
}}),
"[project]/src/app/(workspace)/workspaces/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/(workspace)/workspaces/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_542e0cc3._.js.map