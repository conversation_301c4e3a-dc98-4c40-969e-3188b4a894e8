import { Model } from 'mongoose';
import { KnowledgeBaseFolder, KnowledgeBaseItem } from './interfaces/knowledge-base.interface';
import { CreateKnowledgeBaseFolderDto, UpdateKnowledgeBaseFolderDto, CreateKnowledgeBaseItemDto, UpdateKnowledgeBaseItemDto } from './dto/knowledge-base.dto';
import { S3Service } from '../s3/s3.service';
import { OrganizationsService } from '../organizations/organizations.service';
export declare class KnowledgeBaseService {
    private folderModel;
    private itemModel;
    private s3Service;
    private organizationsService;
    constructor(folderModel: Model<KnowledgeBaseFolder>, itemModel: Model<KnowledgeBaseItem>, s3Service: S3Service, organizationsService: OrganizationsService);
    createFolder(organizationId: string, userId: string, createFolderDto: CreateKnowledgeBaseFolderDto): Promise<KnowledgeBaseFolder>;
    getFoldersByOrganization(organizationId: string): Promise<KnowledgeBaseFolder[]>;
    getFolderById(folderId: string, organizationId: string): Promise<KnowledgeBaseFolder>;
    updateFolder(folderId: string, organizationId: string, updateFolderDto: UpdateKnowledgeBaseFolderDto): Promise<KnowledgeBaseFolder>;
    deleteFolder(folderId: string, organizationId: string): Promise<void>;
    createItem(organizationId: string, userId: string, createItemDto: CreateKnowledgeBaseItemDto): Promise<KnowledgeBaseItem>;
    uploadDocumentToS3(itemId: string, organizationId: string, file: Buffer, originalName: string, mimeType: string): Promise<KnowledgeBaseItem>;
    getItemsByFolder(folderId: string, organizationId: string): Promise<KnowledgeBaseItem[]>;
    getItemById(itemId: string, organizationId: string): Promise<KnowledgeBaseItem>;
    updateItem(itemId: string, organizationId: string, updateItemDto: UpdateKnowledgeBaseItemDto): Promise<KnowledgeBaseItem>;
    deleteItem(itemId: string, organizationId: string): Promise<void>;
    getPresignedDownloadUrl(itemId: string, organizationId: string): Promise<string>;
}
