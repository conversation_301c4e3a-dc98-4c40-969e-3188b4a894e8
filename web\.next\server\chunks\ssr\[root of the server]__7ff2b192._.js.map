{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/components/theme/ThemeProvider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ThemeProvider as NextThemesProvider, type ThemeProviderProps } from \"next-themes\";\r\n\r\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\r\n    return (\r\n        <NextThemesProvider \r\n          {...props}\r\n          enableSystem\r\n          attribute=\"class\"\r\n          defaultTheme=\"system\"\r\n          disableTransitionOnChange={false}\r\n        >\r\n          {children}\r\n        </NextThemesProvider>\r\n      );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACpE,qBACI,8OAAC,gJAAA,CAAA,gBAAkB;QAChB,GAAG,KAAK;QACT,YAAY;QACZ,WAAU;QACV,cAAa;QACb,2BAA2B;kBAE1B;;;;;;AAGX", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive%20-%20OS%20Websolutions%20B.V/projects/agent/dev/orova-vapi/web/src/lib/providers/ReactQueryProvider.tsx"], "sourcesContent": ["\r\n\"use client\";\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\r\nimport { useState } from \"react\";\r\n\r\n\r\nexport default function ReactQueryProvider({ children }: { children: React.ReactNode }) {\r\n  const [queryClient] = useState(() => new QueryClient());\r\n\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      {children}\r\n    </QueryClientProvider>\r\n  );\r\n\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAFA;;;;AAKe,SAAS,mBAAmB,EAAE,QAAQ,EAAiC;IACpF,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,IAAI,6KAAA,CAAA,cAAW;IAEpD,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC1B;;;;;;AAIP", "debugId": null}}]}